"""
阿里云OSS客户端模块，用于上传文件到阿里云对象存储
"""

import oss2
import uuid
import base64
import tempfile
import asyncio
from typing import Optional, List, Tuple, Dict
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

from config import global_config
from log import RustyLogger

logger = RustyLogger("OSS", log_file="logs/oss.log")


class OSSClient:
    def __init__(self):
        """初始化OSS客户端"""
        self.config = global_config.oss
        self.executor = ThreadPoolExecutor(max_workers=10)  # 用于异步上传的线程池

        if not all([self.config.access_key_id, self.config.access_key_secret,
                   self.config.endpoint, self.config.bucket_name]):
            logger.warn("OSS配置不完整，OSS功能将不可用")
            self.bucket = None
            return

        try:
            auth = oss2.Auth(self.config.access_key_id, self.config.access_key_secret)
            self.bucket = oss2.Bucket(auth, self.config.endpoint, self.config.bucket_name)
            logger.info(f"OSS客户端初始化成功，bucket: {self.config.bucket_name}")
        except Exception as e:
            logger.error(f"OSS客户端初始化失败: {str(e)}")
            self.bucket = None

    def is_available(self) -> bool:
        """检查OSS是否可用"""
        return self.bucket is not None

    def upload_base64_image(self, base64_data: str, data_id: str, page_title: str, subpage_index: Optional[int] = None) -> Optional[str]:
        """
        上传base64编码的图片到OSS

        Args:
            base64_data (str): base64编码的图片数据
            data_id (str): 数据ID
            page_title (str): 页面标题
            subpage_index (Optional[int]): 子页面索引，如果是子页面则提供

        Returns:
            Optional[str]: 上传成功返回OSS链接，失败返回None
        """
        if not self.is_available():
            logger.warn("OSS不可用，跳过上传")
            return None

        try:
            # 解码base64数据
            image_data = base64.b64decode(base64_data)

            # 生成文件名
            if subpage_index is not None:
                # 子页面文件名: {{data_id}}_{{页面标题}}_子页面{{i}}.png
                filename = f"{data_id}_{page_title}_子页面{subpage_index}.png"
            else:
                # 主页面文件名: {{data_id}}_{{页面标题}}.png
                filename = f"{data_id}_{page_title}.png"

            # 构造完整的对象名称
            object_name = f"{self.config.file_dir}/{filename}"

            # 上传到OSS
            result = self.bucket.put_object(object_name, image_data)

            if result.status == 200:
                # 构造访问链接
                endpoint_clean = self.config.endpoint.replace('https://', '').replace('http://', '')
                oss_url = f"https://{self.config.bucket_name}.{endpoint_clean}/{object_name}"
                logger.info(f"图片上传成功: {oss_url}")
                return oss_url
            else:
                logger.error(f"图片上传失败，状态码: {result.status}")
                return None

        except Exception as e:
            logger.error(f"上传图片到OSS时出错: {str(e)}")
            return None

    def upload_file(self, file_path: str, object_name: Optional[str] = None) -> Optional[str]:
        """
        上传文件到OSS

        Args:
            file_path (str): 本地文件路径
            object_name (str): OSS对象名称，如果为None则使用文件名

        Returns:
            Optional[str]: 上传成功返回OSS链接，失败返回None
        """
        if not self.is_available():
            logger.warning("OSS不可用，跳过上传")
            return None

        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.error(f"文件不存在: {file_path}")
                return None

            if object_name is None:
                file_id = str(uuid.uuid4())
                object_name = f"files/{file_id}_{file_path.name}"

            # 上传文件
            result = self.bucket.put_object_from_file(object_name, str(file_path))

            if result.status == 200:
                # 构造访问链接
                oss_url = f"https://{self.config.bucket_name}.{self.config.endpoint.replace('https://', '').replace('http://', '')}/{object_name}"
                logger.info(f"文件上传成功: {oss_url}")
                return oss_url
            else:
                logger.error(f"文件上传失败，状态码: {result.status}")
                return None

        except Exception as e:
            logger.error(f"上传文件到OSS时出错: {str(e)}")
            return None

    async def async_upload_base64_image(self, base64_data: str, data_id: str, page_title: str, subpage_index: Optional[int] = None) -> Optional[str]:
        """
        异步上传base64编码的图片到OSS

        Args:
            base64_data (str): base64编码的图片数据
            data_id (str): 数据ID
            page_title (str): 页面标题
            subpage_index (Optional[int]): 子页面索引，如果是子页面则提供

        Returns:
            Optional[str]: 上传成功返回OSS链接，失败返回None
        """
        if not self.is_available():
            logger.warn("OSS不可用，跳过上传")
            return None

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor,
            self.upload_base64_image,
            base64_data, data_id, page_title, subpage_index
        )

    async def async_upload_file(self, file_path: str, object_name: Optional[str] = None) -> Optional[str]:
        """
        异步上传文件到OSS

        Args:
            file_path (str): 本地文件路径
            object_name (str): OSS对象名称，如果为None则使用文件名

        Returns:
            Optional[str]: 上传成功返回OSS链接，失败返回None
        """
        if not self.is_available():
            logger.warning("OSS不可用，跳过上传")
            return None

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor,
            self.upload_file,
            file_path, object_name
        )

    async def batch_upload_files(self, file_uploads: List[Tuple[str, str]]) -> Dict[str, Optional[str]]:
        """
        批量异步上传文件到OSS

        Args:
            file_uploads (List[Tuple[str, str]]): 文件上传列表，每个元素为(file_path, object_name)

        Returns:
            Dict[str, Optional[str]]: 文件路径到OSS链接的映射，上传失败的为None
        """
        if not self.is_available():
            logger.warning("OSS不可用，跳过批量上传")
            return {file_path: None for file_path, _ in file_uploads}

        # 创建异步任务
        tasks = []
        for file_path, object_name in file_uploads:
            task = self.async_upload_file(file_path, object_name)
            tasks.append((file_path, task))

        # 并发执行所有上传任务
        results = {}
        for file_path, task in tasks:
            try:
                oss_url = await task
                results[file_path] = oss_url
                if oss_url:
                    logger.info(f"批量上传成功: {file_path} -> {oss_url}")
                else:
                    logger.error(f"批量上传失败: {file_path}")
            except Exception as e:
                logger.error(f"批量上传异常: {file_path} - {str(e)}")
                results[file_path] = None

        return results

    # def delete_object(self, object_name: str) -> bool:
    #     """
    #     删除OSS对象

    #     Args:
    #         object_name (str): OSS对象名称

    #     Returns:
    #         bool: 删除成功返回True，失败返回False
    #     """
    #     if not self.is_available():
    #         logger.warning("OSS不可用，无法删除对象")
    #         return False

    #     try:
    #         result = self.bucket.delete_object(object_name)
    #         if result.status == 204:
    #             logger.info(f"对象删除成功: {object_name}")
    #             return True
    #         else:
    #             logger.error(f"对象删除失败，状态码: {result.status}")
    #             return False

    #     except Exception as e:
    #         logger.error(f"删除OSS对象时出错: {str(e)}")
    #         return False


# 全局OSS客户端实例
oss_client = OSSClient()
