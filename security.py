import json
import base64
import aiohttp
import requests


class SecurityUtils:
    @staticmethod
    def int_to_js_hex(n: int) -> str:
        """
        模拟 security.js 中 RSAUtils.biToHex
        将整数按 16 位（2 字节为一组，每组固定导出 4 个16进制数字）转换为 hex 字符串，
        注意是把整数拆成 base 2^16 的各"位"，再把每位固定输出 4 个 hex 数字，然后顺序倒置。
        """
        if n == 0:
            return "0"
        digits = []
        while n:
            digits.append(n & 0xFFFF)
            n >>= 16
        # 每个 16 位转换为4位 hex，不足前面补0，然后倒序拼接（js 代码中从最高位开始）
        hex_str = "".join(format(d, "04x") for d in reversed(digits))
        return hex_str

    @staticmethod
    def rsa_encrypt(text: str, modulus_hex: str, exponent_hex: str) -> str:
        """
        模拟 RSAUtils.encryptedString(key, s)
        1. 首先将 text 转换为字符码数组（js 里用 charCodeAt，取出的数值均在 0～255 范围内）；
        2. 如果字符数不是块大小的整数倍，则末尾补 0；
        3. 每块按"2字节（低字节在前）"方式转换为整数，然后 c = pow(整数, e, modulus) 得到一个 RSA 加密块，
        4. 最后对每块用类似 biToHex 的方法转换为 hex 字符串，各块之间用空格分隔。
        """
        e = int(exponent_hex, 16)
        modulus = int(modulus_hex, 16)
        # 计算 modulus 占用的"16 位字"个数
        num_digits = (modulus.bit_length() + 15) // 16
        # 每个块中字节数 = 2*(k-1)
        chunk_size = 2 * (num_digits - 1)

        # 注意：js 的 charCodeAt 直接得到字符的 Unicode 编码（通常 <256）
        # 故此处直接按 latin1 编码即可
        text_bytes = bytearray(text, "latin1")
        # 补 0 使得数据块长度为 chunk_size 的整数倍
        if len(text_bytes) % chunk_size != 0:
            pad_len = chunk_size - (len(text_bytes) % chunk_size)
            text_bytes += b"\x00" * pad_len

        encrypted_blocks = []
        # 对每个块进行加密
        for i in range(0, len(text_bytes), chunk_size):
            block = text_bytes[i: i + chunk_size]
            # 将每块看作"低字节在前"：js 里 对每两字节处理后以 16^16 进位累加，
            # 直接用 from_bytes(..., byteorder="little") 得到相同结果
            block_int = int.from_bytes(block, byteorder="little")
            # RSA 加密（无填充），相当于进行模幂运算
            c = pow(block_int, e, modulus)
            encrypted_blocks.append(SecurityUtils.int_to_js_hex(c))
        # 将各块以空格分隔并去除末尾多余的空格（js 中最后用 substring 切除最后的空格）
        return " ".join(encrypted_blocks)


class CaptchaClient:
    def __init__(self, session: requests.Session):
        self.api_url = "http://www.fdyscloud.com.cn/tuling/predict"
        self.username = "sam7200"
        self.password = "1_52Dzhp"
        self.version = "3.1.1"
        self.model_id = "04897896"
        
        self.session = session
    
    def save_captcha(self, captcha_url: str, save_path: str ="captcha.jpg") -> str:
        """
        获取验证码图片并保存到本地文件
        
        Args:
            session: requests.Session对象
            captcha_url: 验证码的URL
            save_path: 保存验证码的文件路径，默认为"captcha.jpg"
            
        Returns:
            str: 保存的验证码图片文件路径
        """
        response = self.session.get(captcha_url)
        response.raise_for_status()
        
        with open(save_path, "wb") as f:
            f.write(response.content)

        return save_path
    
    def captcha_base64(self, captcha_url: str) -> str:
        """
        获取验证码图片并转换为base64编码字符串
        
        Args:
            session: requests.Session对象
            captcha_url: 验证码的URL
            
        Returns:
            str: 验证码图片的base64编码字符串
        """
        
        response = self.session.get(captcha_url)
        response.raise_for_status()
        image_base64 = base64.b64encode(response.content).decode('utf-8')
        
        return image_base64
    
    def parse_captcha(self, captcha_base64: str) -> str:
        """
        解析验证码图片，返回识别结果
        
        Args:
            captcha_base64: 验证码图片的base64编码字符串
            
        Returns:
            str: 识别结果
        """        
        payload = {
            "username": self.username,
            "password": self.password,
            "b64": captcha_base64,
            "ID": self.model_id,
            "version": self.version
        }

        response = requests.post(self.api_url, json=payload)
        result = response.json()
        if result.get("code") == 1:
            return result["data"]["result"]
        else:
            raise RuntimeError(f"验证码识别失败: {result.get('msg', '未知错误')}")


class AsyncCaptchaClient:
    def __init__(self, session: aiohttp.ClientSession):
        self.api_url = "http://www.fdyscloud.com.cn/tuling/predict"
        self.username = "sam7200"
        self.password = "1_52Dzhp"
        self.version = "3.1.1"
        self.model_id = "04897896"
        
        self.session = session
    
    async def save_captcha(self, captcha_url: str, save_path: str ="captcha.jpg") -> str:
        """
        获取验证码图片并保存到本地文件
        
        Args:
            captcha_url: 验证码的URL
            save_path: 保存验证码的文件路径，默认为"captcha.jpg"
            
        Returns:
            str: 保存的验证码图片文件路径
        """
        async with self.session.get(captcha_url) as response:
            response.raise_for_status()
            content = await response.read()
        
        with open(save_path, "wb") as f:
            f.write(content)
            
        return save_path
    
    async def captcha_base64(self, captcha_url: str) -> str:
        """
        获取验证码图片并转换为base64编码字符串
        
        Args:
            captcha_url: 验证码的URL
            
        Returns:
            str: 验证码图片的base64编码字符串
        """
        async with self.session.get(captcha_url) as response:
            response.raise_for_status()
            content = await response.read()
        
        image_base64 = base64.b64encode(content).decode('utf-8')
        
        return image_base64
    
    async def parse_captcha(self, captcha_base64: str) -> str:
        """
        解析验证码图片，返回识别结果
        
        Args:
            captcha_base64: 验证码图片的base64编码字符串
            
        Returns:
            str: 识别结果
        """        
        payload = {
            "username": self.username,
            "password": self.password,
            "b64": captcha_base64,
            "ID": self.model_id,
            "version": self.version
        }
        
        for _ in range(3):
            # Note: Original used requests.post instead of session.post
            async with self.session.post(self.api_url, json=payload) as response:
                text = await response.text()
                result = json.loads(text)
            
            if result.get("code") == 1:
                return result["data"]["result"]
            
        # 如果多次尝试都失败，则抛出异常
        raise RuntimeError("验证码识别失败, 请前往: http://www.fdyscloud.com.cn/#/userCenterPage 查看明细")
