


<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="X-UA-Compatible" content="IE=8" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
 
 
 






<link rel="stylesheet" type="text/css" href="/permitExt/thscommon/style/artDialog/aero.css" />

<script type="text/javascript" src="/permitExt/thscommon/javascript/jquery.min.js"></script>
<script type="text/javascript" src="/permitExt/thscommon/javascript/baiduStatistics.js"></script>

<script type="text/javascript" src="/permitExt/thscommon/javascript/json2.js"></script>
<script type="text/javascript" src="/permitExt/thscommon/javascript/jquery.artDialog.js"></script>
<script type="text/javascript" src="/permitExt/thscommon/javascript/My97DatePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/permitExt/thscommon/javascript/jquery.media.js"></script>
<script type="text/javascript" src="/permitExt/thscommon/javascript/upload.js"></script>
<script type="text/javascript" src="/permitExt/thscommon/javascript/exportWord.js"></script>
<script type="text/javascript" src="/permitExt/util/xzxk/swfupload/swfupload.js"></script>
<script type="text/javascript" src="/permitExt/util/xzxk/swfupload/handlers.js"></script>
<script type="text/javascript" src="/permitExt/util/xzxk/swfupload/fileprogress.js"></script>
<script type="text/javascript" src="/permitExt/js/content.js"></script>
<!--validate start  -->
<link rel="stylesheet" href="/permitExt/thscommon/javascript/jQuery-Validation-Engine-master/css/validationEngine.jquery.css" type="text/css" />
<link rel="stylesheet" href="/permitExt/thscommon/javascript/jQuery-Validation-Engine-master/css/template.css" type="text/css" />
<script src="/permitExt/thscommon/javascript/jQuery-Validation-Engine-master/js/languages/jquery.validationEngine-zh_CN.js" type="text/javascript" charset="utf-8"></script>
<script src="/permitExt/thscommon/javascript/jQuery-Validation-Engine-master/js/jquery.validationEngine.js" type="text/javascript" charset="utf-8"></script>
<!--validate end  -->


<!-- base64  -->
<script type="text/javascript" src="/permitExt/thscommon/javascript/jquery.base64.js"></script>

<link rel="stylesheet" type="text/css" href="/permitExt/util/xzxk/css/xzxk_button.css" />
<link rel="stylesheet" type="text/css" href="/permitExt/util/xzxk/css/xzxk_style.css" />
<link rel="stylesheet" type="text/css" href="/permitExt/util/xzxk/swfupload/default.css" />
<link rel=stylesheet type=text/css href="/permitExt/thscommon/style/page-base.css"/>

<script type="text/javascript" src="/permitExt/js/urlhanzi.js"></script>

<!-- èªå®ä¹éç¨æ ·å¼ -->
<link rel="stylesheet" type="text/css" href="/permitExt/util/css/common/custom.css" />
<!-- æºè½å®¡æ ¸js -->
<script type="text/javascript" src="/permitExt/report/common/layer/layer.js"></script>
<script type="text/javascript" src="/permitExt/js/intelligent_checking.js"></script>
<script>
    var ctx = "/permitExt";
    
	//æ¥å¿è®°å½
	function saveOperationLog(dataId,operate,cardtype,enterId,powerId){
		
	}


		/**
	 * æ°å­è½¬ç§å­¦è®¡æ°æ³
	 */
	function scientificFormat(str) {
		try {
			if (isNumeric(str)) {
				if (Number(str) < 0.00001 && Number(str) > 0) {
					return Number(Number(str).toFixed(9)).toExponential();
				} else {
					return numberFixed9(parseFloat(str));
				}
			} else {
				return "/";
			}
		} catch (err) {
			return "/";
		}
	}
	
	/**
	 * å¤æ­å­ç¬¦ä¸²æ¯å¦ä¸ºæ°å­
	 */
	function isNumeric(str) {
	    return !isNaN(str) && !isNaN(parseFloat(str));
	}
	
	
	/**
	 * æ°å­ç²¾ç¡®å°9ä½å°æ°ï¼å¹¶å»ææ«å°¾å¤ä½ç0
	 */
	function numberFixed9(num) {
		try {
			return num.toFixed(9).replace(/\.?0+$/, '')
		} catch (err) {
			return num;
		}
	}
    
</script>
<title>排污单位基本情况-工业噪声排放信息</title>
<style type="text/css">
.AutoNewline
{
  white-space:normal;word-break: break-all;
}
 table td {
	height: 18px;
	text-align: center;
}

table td input[type=text] {
	height: 99%;
	width: 90%;
}

</style>

<script type="text/javascript" src="/permitExt/js/itemcard_right.js"></script>
<script type="text/javascript" src="/permitExt/js/util/util.js"></script>
<script>

	$(document).ready(function() {
		$("#mainForm").validationEngine("attach", {
			promptPosition : "topLeft",
			autoPositionUpdate : true,
			autoHidePrompt : true,
			autoHideDelay : 3000,
			showOneMessage : true
		});
		// 合并单元格
		formatTable('5,4,3,2,1,0',"basic_table");
	});
	
	
	var tmpstate = "";
	function setTmpstate(){
		//当前页面状态
		var curcardstate = $("#curcardstate").val();
		//数据库存的项目状态
		var cardstate = $("#cardstate").val();
		//要跳转的状态
		tmpstate = parent.getCardState(curcardstate,cardstate);
		$("#cardstate").val(tmpstate);
	}
	
	//点击暂存后调用,并存在整个表单
	function saveFormChange(isNext){
		//点击保存之前，保存其他信息
		if(!saveSoundOther()){
			return false;
		};
		$.ajax({
			type: "POST",
			url: "/permitExt/syssb/wysb/hpsp/sound/sound!saveSound.action",
			data: $('#mainForm').serialize(), 
			async: false,
			success: function(json){//调用成功的话
				parent.art.dialog.tips("暂存成功！");
				if (json && json.length > 0) {
					var jsonData = JSON.parse(json);
					parent.itemID = jsonData.DATAID;
					parent.addFixedTip();
					parent.modifyflag = false;
					parent.iframesave = true;
				}
			}
		});
		return true;
	}
	
	//点击暂存后调用,并存在整个表单
	function saveSoundOther(){
		$.ajax({
			type: "POST",
			url: "/permitExt/syssb/wysb/hpsp/sound/sound-other!saveInfo.action",
			data: $('#mainForm').serialize(), 
			async: false,
			success: function(json){//调用成功的话
			}
		});
		return true;
	}
	
	function next() {
		if (saveForm(true)) {
			var nextCard = "";
			if(!nextCard){
				nextCard = "card10";
			}
			parent.loadCardList(tmpstate, 'XZXKTYPE_A', nextCard);
		}
	}
	
	//点击暂存后调用,并存在整个表单
	function saveForm(isNext){
		if($("#mainForm").validationEngine("validate")) {
			//点击保存之前，保存其他信息
			if(!saveSoundOther()){
				return false;
			};
/* 			if(!checkIscurrsave()){	
				alert("您还有数据未填写完成！");
				$.ajax({
					type: "POST",
					 url:"/permitExt/common/flowextend/flow!updateCardid.action",
					data: $('#mainForm').serialize(), 
					async: false,
					success: function(msg){
						parent.addFixedTip();
					}
				});
				return false;
			} */
			setTmpstate();
			var saveChange = "";
			//内外网都用这个页面，下一步打对勾，toolbar保存不打对勾
			if(isNext == true){
				saveChange = 1;
			}
			$.ajax({
				type: "POST",
				url: "/permitExt/syssb/wysb/hpsp/sound/sound!saveSound.action?saveChange="+saveChange,
				data: $('#mainForm').serialize(), 
				async: false,
				success: function(json){//调用成功的话
					if (json && json.length > 0) {
						var jsonData = JSON.parse(json);
						parent.itemID = jsonData.DATAID;
						parent.art.dialog.tips("保存成功！");
						parent.addFixedTip();
						parent.modifyflag = false;
						parent.iframesave = true;
						parent.isCopyCard = false;
					}
				}
			});
			return true;
		}else{
			return false;
		}
	}
	
	//点击暂存后调用,并存在整个表单
	function saveFormYy(isNext){
		if($("#mainForm").validationEngine("validate")) {
			// 点击保存之前，保存其他信息
			if(!saveSoundOther()){
				return false;
			};
			$.ajax({
				type: "POST",
				url: "/permitExt/syssb/wysb/hpsp/sound/sound!saveSound.action?saveChange=1",
				data: $('#mainForm').serialize(), 
				async: false,
				success: function(json){//调用成功的话
					if (json && json.length > 0) {
						var jsonData = JSON.parse(json);
						parent.itemID = jsonData.DATAID;
						parent.art.dialog.tips("保存成功！");
						parent.addFixedTip();
						parent.modifyflag = false;
						parent.iframesave = true;
					}
				}
			});
		}
	}
	
	//验证当前页面点击下一步，是否可以打对勾
	function checkIscurrsave(){
		var flag = true;
		$.ajax({
			type: "POST",
			url: "/permitExt/syssb/wysb/hpsp/sound/sound!checkIscurrsave.action",
			data: $('#mainForm').serialize(), 
			async: false,
			success: function(msg){
				if(msg != 'true'){
					flag = false;
				}
			}
		});
		return flag;
	}
	
 	//回调方法刷新当前页面
	function reloadJsp() {
		parent.addFixedTip('card1021');
		location.reload();
	}

	/** 弹出 产噪环节信息表  修改或者添加页面*/
	function openSoundInfo(pkid,type,business,title,initwidth,initheight){
	 	$.dialog.openhanzi('/permitExt/syssb/wysb/hpsp/sound/sound-'+business+'!gotoEdit.action?dataid=ecbe689dacd14a7eb419551f3bb5751e&pkid='+pkid+"&isAdmin=&hyid=C2319&type="+type,{
		 	lock: true,
		 	background: '#333', // 背景色
		 	opacity: 0.30,	// 透明度
		 	title:title,
		 	lock:'ture',
		 	width:initwidth+'px',
		 	height:initheight+'px',
		 	fixed: true,
		 	close:function(){
		 		reloadJsp();
		 	}
	 	});
	}
	
	function deleteSoundInfo(pkid,type,business){
		var isAdmin = '';
		var dataid = $("#dataid").val();
		if(confirm("确定要这条删除数据吗？")){
			$.ajax({
				type: "GET",
				url: "/permitExt/syssb/wysb/hpsp/sound/sound-"+business+"!deleteInfo.action?isAdmin="+isAdmin+"&dataid=ecbe689dacd14a7eb419551f3bb5751e&pkid="+pkid,
				async: false,
				success: function(json){//调用成功的话
					if (json=="success") {
						location.reload();
					}
				}
			});
			//initPage('readonly');
		}
	}
	
	
</script>
</head>
<body onload="initPage('readonly')" >
<div class="list-title">
	<span><img src="/permitExt/util/xzxk/images/title.png" /></span>当前位置：工业噪声排放信息
</div>
<font color="red" style="font-weight: bold; width: 96%" class="annotation comment_text" id="wwnotice">注：*为必填项，没有相应内容的请填写“无”或“/”</font>
<br />

<form name="mainForm" method="post"  id="mainForm" target="_parent">
	<input type="hidden" id="itemMap.dataid" name="itemMap.dataid" value="ecbe689dacd14a7eb419551f3bb5751e" /> 
	<input type="hidden" id="dataid" name="dataid" value="ecbe689dacd14a7eb419551f3bb5751e" />
	<input type="hidden" id="cardid" name="cardid" value="card1021" /> 
	<input type="hidden" id="industryId" name="industryId" value="1525228904875" /> 
	<input type="hidden" id="hyid" name="hyid" value="C2319" /> 
	<input type="hidden" id="savedcardid" name="itemMap.savedcardid" value="[card107][card2][card103][card3][card4][card5][card6][card7][card16][card8][card9][card102][card1021][card10][card11][card14][card12][card13][card17][card18][card19][card101][card1]" /> 
	<input type="hidden" id="saveType" name="saveType" value="apply" /> 
	<input type="hidden" id="isAdmin" name="isAdmin" value="" />
	<input type="hidden" id="cardstate" name="itemMap.cardstate" value="暂存"/>
	
	<!-- 产噪环节信息表 -->
	<br/><div style="font-family: Microsoft Yahei; font-size: 18px;"><span class="add_title">（1）产噪环节</span></div>
	
	<div style="color: blue;margin-top: 5px;margin-bottom: 5px;">
		<!-- <span style="float: left;">说明：请根据《xxx》填报。</span> -->
		<span class="add_title" style="color: red; float: right;margin-top: -10px">
			
				<input type="button" class="button normal" value="添加"  onclick="openSoundInfo('','add','produce','添加产噪环节信息','1000','450')"/>
			
		</span>
	</div>
	<br />
	<br />
	<table id="basic_table" class="edit-grid-gg">
		<tr>
			<th style="text-align:center;width:10%" >行业类别</th>
			<th style="text-align:center;width:10%" >产噪单元编号</th>
			<th style="text-align:center;width:10%" >产噪单元名称</th>
			<th style="text-align:center;width:10%" >主要产噪设施及数量</th>
			<th style="text-align:center;width:10%" >主要噪声污染防治设施及数量</th>
			
		</tr>
		
			
				<tr id="tr_cfd5b0149c4948b69413c81f07dfd10b">
				<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="hynamecfd5b0149c4948b69413c81f07dfd10b">包装装潢及其他印刷</td>
					<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="process_idcfd5b0149c4948b69413c81f07dfd10b">CZ0001</td>
					<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="gtfwlbnamecfd5b0149c4948b69413c81f07dfd10b">印刷 </td>
						<td
							
								 alt="08746b46dca946788875c096b6a92d96"
							
							
						>
							
							
								风机
							
								/2台
						</td>
		
					<td 
					  
								 alt="8eef2a4641c64c6398bd3cfe22f971e4"
							
							
					 >
						
						
							基础减振
						
							/76个					
					</td>
					
	  			</tr>
			
				<tr id="tr_cfd5b0149c4948b69413c81f07dfd10b">
				<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="hynamecfd5b0149c4948b69413c81f07dfd10b">包装装潢及其他印刷</td>
					<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="process_idcfd5b0149c4948b69413c81f07dfd10b">CZ0001</td>
					<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="gtfwlbnamecfd5b0149c4948b69413c81f07dfd10b">印刷 </td>
						<td
							
								 alt="fdcd4c199f88451d983bfaba90ac46c0"
							
							
						>
							
								复合机
							
							
								/12台
						</td>
		
					<td 
					  
								 alt="8eef2a4641c64c6398bd3cfe22f971e4"
							
							
					 >
						
						
							基础减振
						
							/76个					
					</td>
					
	  			</tr>
			
				<tr id="tr_cfd5b0149c4948b69413c81f07dfd10b">
				<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="hynamecfd5b0149c4948b69413c81f07dfd10b">包装装潢及其他印刷</td>
					<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="process_idcfd5b0149c4948b69413c81f07dfd10b">CZ0001</td>
					<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="gtfwlbnamecfd5b0149c4948b69413c81f07dfd10b">印刷 </td>
						<td
							
								 alt="30f99cb86ba9488fbaeca2ab78f9e211"
							
							
						>
							
							
								空压机
							
								/1座
						</td>
		
					<td 
					  
								 alt="8eef2a4641c64c6398bd3cfe22f971e4"
							
							
					 >
						
						
							基础减振
						
							/76个					
					</td>
					
	  			</tr>
			
				<tr id="tr_cfd5b0149c4948b69413c81f07dfd10b">
				<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="hynamecfd5b0149c4948b69413c81f07dfd10b">包装装潢及其他印刷</td>
					<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="process_idcfd5b0149c4948b69413c81f07dfd10b">CZ0001</td>
					<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="gtfwlbnamecfd5b0149c4948b69413c81f07dfd10b">印刷 </td>
						<td
							
								 alt="b6489414f32b425eb9b873ed1d1644d3"
							
							
						>
							
								制袋机
							
							
								/45台
						</td>
		
					<td 
					  
								 alt="d6f6f7033f6e4be180ee088ff96736c4"
							
							
					 >
						
						
							厂房隔声
						
							/2座					
					</td>
					
	  			</tr>
			
				<tr id="tr_cfd5b0149c4948b69413c81f07dfd10b">
				<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="hynamecfd5b0149c4948b69413c81f07dfd10b">包装装潢及其他印刷</td>
					<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="process_idcfd5b0149c4948b69413c81f07dfd10b">CZ0001</td>
					<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="gtfwlbnamecfd5b0149c4948b69413c81f07dfd10b">印刷 </td>
						<td
							
								 alt="2f84e272bd034e20af27da7d83129088"
							
							
						>
							
								分切机
							
							
								/11台
						</td>
		
					<td 
					  
								 alt="d6f6f7033f6e4be180ee088ff96736c4"
							
							
					 >
						
						
							厂房隔声
						
							/2座					
					</td>
					
	  			</tr>
			
				<tr id="tr_cfd5b0149c4948b69413c81f07dfd10b">
				<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="hynamecfd5b0149c4948b69413c81f07dfd10b">包装装潢及其他印刷</td>
					<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="process_idcfd5b0149c4948b69413c81f07dfd10b">CZ0001</td>
					<td alt="cfd5b0149c4948b69413c81f07dfd10b" id="gtfwlbnamecfd5b0149c4948b69413c81f07dfd10b">印刷 </td>
						<td
							
								 alt="c7db163bf07443059922190d3b39dccb"
							
							
						>
							
								印刷机
							
							
								/6台
						</td>
		
					<td 
					  
								 alt="d6f6f7033f6e4be180ee088ff96736c4"
							
							
					 >
						
						
							厂房隔声
						
							/2座					
					</td>
					
	  			</tr>
			
		
	</table>
	<br />
	<div style="font-family: Microsoft Yahei; font-size: 18px;"><span class="add_title">（2）执行标准</span></div>
	
	<div style="color: blue;margin-top: 5px;margin-bottom: 5px;">
		<!-- <span style="float: left;">说明：请根据《xxx》填报。</span> -->
		<span class="add_title" style="color: red; float: right;margin-top: -10px">
			
				<input type="button" class="button normal" value="添加"  onclick="openSoundInfo('','add','standard','添加执行标准信息','700','180');"/>
			
		</span>
	</div>
	<br/>
	<br />
	<table id="basic_table" class="edit-grid-gg">
		<tr>
			<th style="text-align:center;width:10%" rowspan="2">排放标准名称及编号</th>
			<th style="text-align:center;width:10%" colspan="2">生产时段</th>
			
		</tr>
		<tr>
			<th style="text-align:center;width:10%">昼间</th>
			<th style="text-align:center;width:10%">夜间</th>
		</tr>
		
			
				<tr id="tr_a0272f8fa9b2466aa707e23bc072aadf">
					<td alt="a0272f8fa9b2466aa707e23bc072aadf">工业企业厂界环境噪声排放标准/GB 12348—2008</td>
					<td id="gtfwlbnamea0272f8fa9b2466aa707e23bc072aadf">06:00-22:00 </td>
					<td id="gtfwmcnamea0272f8fa9b2466aa707e23bc072aadf">22:00-次日06:00 </td>
					
	  			</tr>
			
		
	</table>
	
	<br/><div style="font-family: Microsoft Yahei; font-size: 18px;"><span class="add_title">（3）工业噪声排放许可管理要求</span></div>
	
	<div style="color: blue;margin-top: 5px;margin-bottom: 5px;">
		<!-- <span style="float: left;">说明：请根据《xxx》填报。</span> -->
		<span class="add_title" style="color: red; float: right;margin-top: -10px">
			
				<input type="button" class="button normal" value="添加"  onclick="openSoundInfo('','add','manage','添加工业噪声排放许可管理要求信息','700','280');"/>
			
		</span>
	</div>
	<br/>
	<br/>
	<table id="basic_table" class="edit-grid-gg">
		<tr>
			<th style="text-align:center;width:10%" rowspan="3" >厂界噪声点位名称</th>
			<th style="text-align:center;width:10%" rowspan="3" >厂界外声环境功能区类别</th>
			<th style="text-align:center;width:10%" colspan="4" >工业噪声许可排放限值 dB(A)</th>
			
		</tr>
		<tr>
			<th style="text-align:center;width:10%">昼间</th>
			<th style="text-align:center;width:10%" colspan="3">夜间</th>
		</tr>
		<tr>
			<th style="text-align:center;width:10%">等效声级</th>
			<th style="text-align:center;width:10%">等效声级</th>
			<th style="text-align:center;width:10%">频发噪声最大声级</th>
			<th style="text-align:center;width:10%">偶发噪声最大声级</th>
		</tr>
		
			
				<tr id="tr_14c0795ecf43481598c980cdd19d2685">
					<td id="gtfwlbname14c0795ecf43481598c980cdd19d2685">厂界北侧外1米 </td>
					<td id="facilities14c0795ecf43481598c980cdd19d2685">3</td>
					<td id="facilitiescode14c0795ecf43481598c980cdd19d2685">65</td>
					<td id="sslxname14c0795ecf43481598c980cdd19d2685"> 55</td>
					<td id="sslxname14c0795ecf43481598c980cdd19d2685"> 65</td>
					<td id="sslxname14c0795ecf43481598c980cdd19d2685"> 70</td>
					
	  			</tr>
			
				<tr id="tr_423e2d5547c34303813098c506c88901">
					<td id="gtfwlbname423e2d5547c34303813098c506c88901">厂界西侧外1米 </td>
					<td id="facilities423e2d5547c34303813098c506c88901">3</td>
					<td id="facilitiescode423e2d5547c34303813098c506c88901">65</td>
					<td id="sslxname423e2d5547c34303813098c506c88901"> 55</td>
					<td id="sslxname423e2d5547c34303813098c506c88901"> 65</td>
					<td id="sslxname423e2d5547c34303813098c506c88901"> 70</td>
					
	  			</tr>
			
				<tr id="tr_461e3ff7bc68458d8701bccd3220cf27">
					<td id="gtfwlbname461e3ff7bc68458d8701bccd3220cf27">厂界南侧外1米 </td>
					<td id="facilities461e3ff7bc68458d8701bccd3220cf27">3</td>
					<td id="facilitiescode461e3ff7bc68458d8701bccd3220cf27">65</td>
					<td id="sslxname461e3ff7bc68458d8701bccd3220cf27"> 55</td>
					<td id="sslxname461e3ff7bc68458d8701bccd3220cf27"> 65</td>
					<td id="sslxname461e3ff7bc68458d8701bccd3220cf27"> 70</td>
					
	  			</tr>
			
				<tr id="tr_8164bb1005e4429cb58aa9766337b4b7">
					<td id="gtfwlbname8164bb1005e4429cb58aa9766337b4b7">厂界东侧外1米 </td>
					<td id="facilities8164bb1005e4429cb58aa9766337b4b7">3</td>
					<td id="facilitiescode8164bb1005e4429cb58aa9766337b4b7">65</td>
					<td id="sslxname8164bb1005e4429cb58aa9766337b4b7"> 55</td>
					<td id="sslxname8164bb1005e4429cb58aa9766337b4b7"> 65</td>
					<td id="sslxname8164bb1005e4429cb58aa9766337b4b7"> 70</td>
					
	  			</tr>
			
		
	</table>
	<br/><br/>
	
	<div style="font-family: Microsoft Yahei; font-size: 18px;"><span class="add_title">（4）自行监测要求</span></div>
	
	<div style="color: blue;margin-top: 5px;margin-bottom: 5px;">
		<!-- <span style="float: left;">说明：请根据《xxx》填报。</span> -->
		<span class="add_title" style="color: red; float: right;margin-top: -10px">
			
				<input type="button" class="button normal" value="添加"  onclick="openSoundInfo('','add','monitor','添加自行监测要求信息','700','272');"/>
			
		</span>
	</div>
	<br/>
	<br/>
	<table id="basic_table" class="edit-grid-gg">
		<tr>
			<th style="text-align:center;width:10%">厂界噪声点位名称</th>
			<th style="text-align:center;width:10%">监测指标</th>
			<th style="text-align:center;width:10%">监测技术</th>
			<th style="text-align:center;width:10%">自动监测是否应联网</th>
			<th style="text-align:center;width:10%">手工监测频次</th>
			
		</tr>
		
			
				<tr id="tr_30e5e34b9f70405daad9643c487f9cc6">
					<td id="gtfwlbname30e5e34b9f70405daad9643c487f9cc6">厂界北侧外1米 </td>
					<td id="facilities30e5e34b9f70405daad9643c487f9cc6">等效声级,最大声级</td>
					<td id="facilitiescode30e5e34b9f70405daad9643c487f9cc6">手工</td>
					<td id="sslxname30e5e34b9f70405daad9643c487f9cc6"> 否</td>
					<td id="opelngd30e5e34b9f70405daad9643c487f9cc6">
						
						
							1次/季
						
					</td>
					
	  			</tr>
			
				<tr id="tr_0d63e95f3f9746a79d7f503cfbeeb90a">
					<td id="gtfwlbname0d63e95f3f9746a79d7f503cfbeeb90a">厂界西侧外1米 </td>
					<td id="facilities0d63e95f3f9746a79d7f503cfbeeb90a">等效声级,最大声级</td>
					<td id="facilitiescode0d63e95f3f9746a79d7f503cfbeeb90a">手工</td>
					<td id="sslxname0d63e95f3f9746a79d7f503cfbeeb90a"> 否</td>
					<td id="opelngd0d63e95f3f9746a79d7f503cfbeeb90a">
						
						
							1次/季
						
					</td>
					
	  			</tr>
			
				<tr id="tr_6b922d819a0c4168acdd0d302ee6f14d">
					<td id="gtfwlbname6b922d819a0c4168acdd0d302ee6f14d">厂界南侧外1米 </td>
					<td id="facilities6b922d819a0c4168acdd0d302ee6f14d">等效声级,最大声级</td>
					<td id="facilitiescode6b922d819a0c4168acdd0d302ee6f14d">手工</td>
					<td id="sslxname6b922d819a0c4168acdd0d302ee6f14d"> 否</td>
					<td id="opelngd6b922d819a0c4168acdd0d302ee6f14d">
						
						
							1次/季
						
					</td>
					
	  			</tr>
			
				<tr id="tr_800b43a2015349a3b58c969f70aa5093">
					<td id="gtfwlbname800b43a2015349a3b58c969f70aa5093">厂界东侧外1米 </td>
					<td id="facilities800b43a2015349a3b58c969f70aa5093">等效声级,最大声级</td>
					<td id="facilitiescode800b43a2015349a3b58c969f70aa5093">手工</td>
					<td id="sslxname800b43a2015349a3b58c969f70aa5093"> 否</td>
					<td id="opelngd800b43a2015349a3b58c969f70aa5093">
						
						
							1次/季
						
					</td>
					
	  			</tr>
			
		
	</table>
	<br/><br/>
	
	<div style="font-family: Microsoft Yahei; font-size: 18px;"><span class="add_title">（5）其他信息</span></div>
	<!-- <div style="color: blue;margin-top: 3px;float: left;margin-right: 10px;padding-left: 24px;">
	    <img src="/permitExt/images/wwindex/u1315.png" alt="" />
	</div>
	<div style="color: blue;margin-top: 5px;margin-bottom: 5px;">
		<span style="float: left;">
			工业噪声污染防治应满足GB/T 50087和HJ 2034中噪声控制相关要求。
		</span>
		<span style="float: left;">
			a）优化产噪设施布局和物流运输路线，优先采用低噪声设备和运输工具。<br/>
			b）设备的运行和维护应符合设备说明书和相关技术规范的规定，定期检查其活动机构（如铰链、锁扣等）和密封机构（材料）的磨损情况等，及时保养、更换。<br/>
			c）大型噪声综合治理工程应制定检修计划和应急预案。污染治理系统检修时间应与工艺设备同步，对可能有问题的治理系统或设备应随时检查，检修和检查结果应记录并存档。<br/>
			d）噪声控制设备中的易损设备、配件和通用材料，由工业噪声排污单位按机械设备管理规程和工艺安全运行要求储备，保证治理设施的正常使用。<br/>
			e）所有噪声与振动控制设备，都应根据其使用环境的卫生条件、介质属性等要素，制定相应的运行和维护规程，确保其性能和使用寿命。<br/>
			f）定期对噪声污染防治设施进行检查维护，确保噪声污染防治设施可靠有效。
		</span>
	</div> -->
	<br/>
	<table id="apply_table"  class="edit-grid" >
	    <colgroup>
	    	<col width="60%" ></col>
	    	<col width="40%" ></col>
	    </colgroup>
		<tr >
			<td colspan="2" style="text-align: left;">
				
				
						1、工业噪声排污单位应当按照HJ 819 等标准要求，根据自行监测方案及开展状况，梳理全过程监测质量控制要求，建立自行监测数据质量保证与质量控制体系。
2、工业噪声排污单位应按照《排污许可管理条例》，如实在全国排污许可证管理信息平台上公开自行监测信息。
3、工业噪声污染防治应满足GB/T 50087 和HJ 2034 中噪声控制相关要求。
a） 优化产噪设施布局和物流运输路线，优先采用低噪声设备和运输工具。
b） 设备的运行和维护应符合设备说明书和相关技术规范的规定，定期检查其活动机构（如铰链、锁扣等）和密封机构（材料）的磨损情况等，及时保养、更换。
c） 大型噪声综合治理工程应制定检修计划和应急预案。污染治理系统检修时间应与工艺设备同步，对可能有问题的治理系统或设备应随时检查，检修和检查结果应记录并存档。
d） 噪声控制设备中的易损设备、配件和通用材料，由工业噪声排污单位按机械设备管理规程和工艺安全运行要求储备，保证治理设施的正常使用。
e） 所有噪声与振动控制设备，都应根据其使用环境的卫生条件、介质属性等要素，制定相应的运行和维护规程，确保其性能和使用寿命。
f） 定期对噪声污染防治设施进行检查维护，确保噪声污染防治设施可靠有效。
					
			</td>
		</tr>
	</table>
   	
	
		<div align="center">
			<input id="save_button" type="button" class="button normal" value="暂存" onclick="saveFormChange('save')" class="staging" style="min-width: 90px;" />
			<input id="next_button" type="button" class="button normal" value="下一步" onclick="next()" class="staging" style="min-width: 90px;" />
		</div>
	
</form>
</body>
</html>
