
import redis
from config import RedisConfig, global_config

redis_config = global_config.redis


class RedisClient:
    def __init__(self, config: RedisConfig=redis_config):
        self.client = redis.Redis(
            host=config.host, 
            port=config.port,
            password=config.password,
            decode_responses=False,
            db=config.db
        )
    
    def set(self, key: str, value, ex=None, px=None, nx=False, xx=False):
        """设置键值对"""
        return self.client.set(key, value, ex=ex, px=px, nx=nx, xx=xx)
    
    def get(self, key: str):
        """获取键值"""
        return self.client.get(key)
    
    def delete(self, *keys):
        """删除键"""
        return self.client.delete(*keys)
    
    def exists(self, *keys):
        """检查键是否存在"""
        return self.client.exists(*keys)
    
    def expire(self, key: str, time: int):
        """设置键过期时间"""
        return self.client.expire(key, time)
    
    def hset(self, name: str, key=None, value=None, mapping=None):
        """设置哈希表字段"""
        return self.client.hset(name, key=key, value=value, mapping=mapping)
    
    def hget(self, name: str, key: str):
        """获取哈希表字段值"""
        return self.client.hget(name, key)
    
    def hgetall(self, name: str):
        """获取哈希表所有字段和值"""
        return self.client.hgetall(name)
    
    def lpush(self, name: str, *values):
        """将值推入列表左端"""
        return self.client.lpush(name, *values)
    
    def rpop(self, name: str, count=None):
        """从列表右端弹出值"""
        return self.client.rpop(name, count)
    
    def pipeline(self):
        """返回一个Redis管道对象，用于批量执行命令"""
        return self.client.pipeline()
    
    def pubsub(self):
        """返回一个PubSub对象，用于发布/订阅模式"""
        return self.client.pubsub()
