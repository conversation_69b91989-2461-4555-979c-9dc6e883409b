import json
from elasticsearch import Elasticsearch

from log import <PERSON><PERSON><PERSON><PERSON>
from config import ESConfig, global_config


es_config = global_config.es
logger = RustyLogger("ElasticSearch", log_file="logs/es.log")


class ElaasticSearchClient:
    def __init__(self, config: ESConfig=es_config):
        self.client = Elasticsearch(
            hosts=["{}:{}".format(config.host, config.port)],
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Basic {config.auth}"
            }
        )
    
    def index(self, index: str, body: dict):
        try:
            res = self.client.index(index=index, body=body)
        except Exception as e:
            logger.error(f"Error(index): {index} - {e}")
            raise e
        
        return res
    
    def delete(self, index: str, id: str):
        try:
            res = self.client.delete(index=index, id=id)
        except Exception as e:
            logger.error(f"Error(delete): {index} - {e}")
            raise e
        return res
    
    def update(self, index: str, id: str, body: dict):
        try:
            res = self.client.update(index=index, id=id, body=body)
        except Exception as e:
            logger.error(f"Error(update): {index} - {e}")
            raise e
        return res
    
    def update_by_query(self, index: str, body: dict):
        try:
            res = self.client.update_by_query(index=index, body=body)
        except Exception as e:
            logger.error(f"Error(update_by_query): {index} - {e}")
            raise e
        return res
    
    def search(self, index: str, body: dict):
        try:
            res = self.client.search(index=index, body=body)
        except Exception as e:
            logger.error(f"Error(search): {index} - {e}")
            raise e
        return res