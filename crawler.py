"""
permit.mee.gov.cn 爬虫模块，用于模拟登录和获取数据

实现原理:
1. GET 请求 https://permit.mee.gov.cn/cas/login 获取在页面中隐藏的 lt, execution, _eventId, AUTH_SESSION_KEY, hid_modulus, hid_exponent
2. 同一 session GET 请求 https://permit.mee.gov.cn/cas/kaptcha.jpg 获取验证码图片并调用api解析验证码
3. 调用 SecurityUtils.rsa_encrypt 加密用户名和密码
4. 构造登录表单数据，同一 session 提交登录 POST 请求
5. 登录成功后，获取 cookies 中的 DATALIST
6. 遍历 DATALIST, 同一 session POST 请求, 解析结果后拼接每个 dataid 对应的卡片链接
7. 遍历卡片链接, 同一 session GET 请求获取卡片内容
8. 清洗卡片html内容, 去除所有 JavaScript 和标签属性，只保留标签和内容

总结: 该网站是基于 cookie 验证的, 只要有 cookie, 什么都能请求; 若无 cookie, 则重定向到登录页

Tips: 有趣的是, 如果您尝试将卡片网址中的 `&operate=readonly` 参数删掉, 您甚至能修改卡片内容, 但是这样做是违法的, 请勿尝试!
"""

import re
import json
import base64
import aiohttp
import asyncio
import urllib.parse

from htmlmin import minify
from bs4 import BeautifulSoup
from typing import Any, Dict, List, Optional, Tuple

from industry_index import IT
from config import global_config
from llm import LLMClient, extract_md_content
from prompt import SELECT_FILE_PROMPT, TAG_PROMPT
from security import SecurityUtils, AsyncCaptchaClient
from filekit import delete_file, download_file, pdf2text_str, split_text, pdf_to_pages_with_images, get_file_id_from_url, map_text_chunks_to_pages, split_text_with_page_mapping
from service import ChunkData, CrawlStatus, PersistDataStruct, PersistService
from oss_client import oss_client


def minify_html(html_content: str) -> str:
    """
    Minify HTML content while preserving functionality
    """
    return minify(html_content,
        remove_comments=True,      # 移除HTML注释
        remove_empty_space=True,   # 移除多余空格
        remove_all_empty_space=False,  # 保留必要的空格
        reduce_boolean_attributes=True  # 简化布尔属性
    )


def clean_html(html_content: str, clean_display_none: bool=False) -> str:
    """
    Clean HTML by removing JavaScript scripts and selectively removing tag attributes,
    keeping only tags and their content. Returns prettified HTML.

    Args:
        html_content (str): HTML content to clean
        clean_display_none (bool): Whether to remove elements with display:none style
        preserve_attributes (List[str]): List of attributes to preserve (e.g., ['rowspan', 'colspan', 'class', 'id'])

    Returns:
        str: Cleaned HTML content with proper formatting
    """
    preserve_attributes = ['rowspan', 'colspan']

    soup = BeautifulSoup(html_content, "html.parser")

    for tag in soup.find_all(True):
        if not tag.attrs:
            continue

        style = tag.attrs.get("style", "")
        if style:
            if ("display: none" in style.lower() or "display:none" in style.lower()) and clean_display_none:
                tag.decompose()
                continue

        # For input and a tags, preserve all attributes
        if tag.name in ["input", "a"]:
            continue

        # For other tags, only preserve specified attributes
        attrs_to_keep = {}
        for attr_name, attr_value in tag.attrs.items():
            if attr_name in preserve_attributes:
                attrs_to_keep[attr_name] = attr_value

        tag.attrs = attrs_to_keep

    for script in soup.find_all("script"):
        script.decompose()

    for script in soup.find_all("style"):
        script.decompose()

    return minify_html(soup.prettify())


class AsyncPermitMeeClient:
    def __init__(self, username: str, password: str):
        self.base_url = "https://permit.mee.gov.cn/permitExt/outside/LicenseRedirect"
        self.captcha_url = "https://permit.mee.gov.cn/cas/kaptcha.jpg"
        self.login_url = "https://permit.mee.gov.cn/cas/login"
        self.username = username
        self.password = password
        self.is_login = False
        self.session = aiohttp.ClientSession()
        self.splash_url = f"{global_config.app.splash_url}/render.html"
        self.splash_url_png = f"{global_config.app.splash_url}/render.png"

        self.headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }

        self.persist_service = PersistService()

    async def crawl(self, data_id: str) -> List[ChunkData]:
        if not self.is_login:
            await self.login_retry(10)

        if not self.is_login:
            raise Exception("登陆失败, 用户名或密码错误")

        semaphore = asyncio.Semaphore(25)

        async def fetch_card_with_screenshot(url: Tuple[str, str], use_splash: bool=True) -> Tuple[str, str, Optional[str]]:
            """获取卡片HTML内容和截图，返回(title, html_content, oss_url)"""
            async with semaphore:
                html_content = ""
                oss_url = None

                try:
                    cookies_str = self._get_cookies_str()

                    if use_splash:
                        # 使用render.json一次性获取HTML和PNG
                        render_json_url = f"{global_config.app.splash_url}/render.json"
                        params = {
                            "url": url[1],
                            "wait": 2,
                            "html": 1,  # 包含HTML
                            "png": 1,   # 包含PNG截图
                            "render_all": 1,  # 全页面截图
                            "headers": {
                                "Cookie": cookies_str,
                                "User-Agent": self.headers["User-Agent"]
                            }
                        }

                        async with self.session.post(render_json_url, json=params) as res:
                            if res.status == 200:
                                result = await res.json()

                                # 处理HTML内容
                                if "html" in result:
                                    html_content = clean_html(
                                        result["html"],
                                        clean_display_none=True if url[0]=="排污单位基本情况" else False
                                    )

                                # 处理PNG截图并上传到OSS
                                if "png" in result and result["png"]:
                                    base64_image = result["png"]

                                    # 异步上传到OSS
                                    if oss_client.is_available():
                                        oss_url = await oss_client.async_upload_base64_image(base64_image, data_id, url[0])
                                        if oss_url:
                                            print(f"截图已上传到OSS: {url[0]} -> {oss_url}")
                                        else:
                                            print(f"截图上传OSS失败: {url[0]}")
                                    else:
                                        print("OSS不可用，跳过截图上传")
                            else:
                                print(f"Splash请求失败，状态码: {res.status}")
                    else:
                        # 不使用splash，只获取HTML
                        async with self.session.get(url[1]) as res:
                            text = await res.text()
                            html_content = clean_html(
                                text,
                                clean_display_none=True if url[0]=="排污单位基本情况" else False
                            )

                except Exception as e:
                    print(f"Error fetching {url[1]}: {str(e)}")
                    html_content = ""

                return (url[0], html_content, oss_url)

        card_url_list = await self._fetch_card_url_list(data_id)
        card_results = await asyncio.gather(*(fetch_card_with_screenshot(url) for url in card_url_list))

        # 处理固体废物管理信息页面的子页面爬取
        async def fetch_subpage_with_screenshot(url: Tuple[str, str]) -> Tuple[str, str, Optional[str]]:
            """获取子页面HTML内容和截图，返回(title, html_content, oss_url)"""
            async with semaphore:
                html_content = ""
                oss_url = None

                try:
                    cookies_str = self._get_cookies_str()

                    # 使用render.json一次性获取HTML和PNG
                    render_json_url = f"{global_config.app.splash_url}/render.json"
                    params = {
                        "url": url[1],
                        "wait": 2,
                        "html": 1,  # 包含HTML
                        "png": 1,   # 包含PNG截图
                        "render_all": 1,  # 全页面截图
                        "headers": {
                            "Cookie": cookies_str,
                            "User-Agent": self.headers["User-Agent"]
                        }
                    }

                    async with self.session.post(render_json_url, json=params) as res:
                        if res.status == 200:
                            result = await res.json()

                            # 处理HTML内容
                            if "html" in result:
                                html_content = clean_html(
                                    result["html"],
                                    clean_display_none=False
                                )

                            # 处理PNG截图并上传到OSS
                            if "png" in result and result["png"]:
                                base64_image = result["png"]

                                # 异步上传到OSS，使用子页面标识
                                if oss_client.is_available():
                                    # 从URL中提取子页面索引
                                    subpage_index = int(url[0].split('_')[1]) if '_' in url[0] else 1
                                    oss_url = await oss_client.async_upload_base64_image(base64_image, data_id, "固体废物管理信息", subpage_index)
                                    if oss_url:
                                        print(f"子页面截图已上传到OSS: 固体废物管理信息_子页面{subpage_index} -> {oss_url}")
                                    else:
                                        print(f"子页面截图上传OSS失败: 固体废物管理信息_子页面{subpage_index}")
                                else:
                                    print("OSS不可用，跳过子页面截图上传")
                        else:
                            print(f"子页面Splash请求失败，状态码: {res.status}")

                except Exception as e:
                    print(f"Error fetching subpage {url[1]}: {str(e)}")
                    html_content = ""

                return (url[0], html_content, oss_url)

        # 针对固体废物管理信息页面的子页面爬取
        for i, (title, html, oss_url) in enumerate(card_results):
            if title == "固体废物管理信息":
                pattern = r"openInformationCk\('([^']*)'\)"
                matches = re.findall(pattern, html)
                if matches:
                    lv2_url_list = [
                        (f"子页面_{j+1}", f"https://permit.mee.gov.cn/permitExt/syssb/wysb/hpsp/gtfw/gtfw-basic!goInformationCk.action?dataid={data_id}&pkid={match}")
                        for j, match in enumerate(matches)
                    ]
                    # 获取子页面内容和截图
                    lv2_results = await asyncio.gather(*(fetch_subpage_with_screenshot(url) for url in lv2_url_list))

                    # 收集子页面的HTML内容和OSS链接
                    subpage_html_list = []
                    subpage_oss_urls = []

                    for _, subpage_html, subpage_oss_url in lv2_results:
                        if subpage_html:
                            subpage_html_list.append(f"<child>{subpage_html}</child>")
                        if subpage_oss_url:
                            subpage_oss_urls.append(subpage_oss_url)

                    # 更新主页面的HTML内容
                    updated_html = html + "".join(subpage_html_list)

                    # 合并OSS链接：主页面 + 子页面
                    combined_oss_urls = []
                    if oss_url:  # 主页面的OSS链接
                        combined_oss_urls.append(oss_url)
                    combined_oss_urls.extend(subpage_oss_urls)  # 子页面的OSS链接

                    # 更新结果，使用特殊标记来保存所有OSS链接
                    card_results[i] = (title, updated_html, combined_oss_urls)

                    print(f"固体废物管理信息页面处理完成:")
                    print(f"  - 主页面OSS链接: {oss_url}")
                    print(f"  - 子页面数量: {len(subpage_oss_urls)}")
                    print(f"  - 子页面OSS链接: {subpage_oss_urls}")
                    print(f"  - 总OSS链接数: {len(combined_oss_urls)}")

        # 构建最终结果，特殊处理固体废物管理信息页面
        result_chunks = []
        for title, html, oss_data in card_results:
            if title == "固体废物管理信息" and isinstance(oss_data, list):
                # 固体废物管理信息页面，oss_data 是包含所有OSS链接的列表
                chunk = ChunkData(
                    chunk_type=[title],
                    chunk_from="web",
                    content=html,
                    multy_file_list=oss_data  # 包含主页面和所有子页面的OSS链接
                )
            else:
                # 其他页面，oss_data 是单个OSS链接或None
                chunk = ChunkData(
                    chunk_type=[title],
                    chunk_from="web",
                    content=html,
                    multy_file_list=[oss_data] if oss_data else []
                )
            result_chunks.append(chunk)

        return result_chunks

    async def crawl_persist(self, data_id: str) -> List[ChunkData]:
        """
        爬取 permit.mee.gov.cn 的数据
        :param data_id: 数据 ID
        :param cache: 是否使用缓存, 若使用, 则先从redis中获取内容, 没获取到则爬取并存入redis

        :return: 爬取到的数据
        """
        persist_data = self.persist_service.search(self.username, data_id)
        if persist_data:
            if persist_data.crawl_status in [CrawlStatus.WEB_DATA, CrawlStatus.BOTH_DATA]:
                return [chunk for chunk in persist_data.chunk_list if chunk.chunk_from == "web"]

        persist_data_new = PersistDataStruct(
            username=self.username,
            password=self.password,
            data_id=data_id,
            crawl_status=CrawlStatus.WEB_DATA,
            chunk_list= await self.crawl(data_id)
        )
        self.persist_service.persist(persist_data_new)
        return persist_data_new.chunk_list

    async def crawl_from_file(self, data_id: str) -> List[ChunkData]:
        """
        从文件中读取数据
        :param file_path: 文件路径

        :return: 爬取到的文件内容
        """
        file_type_map = {
            "report": "环评报告",
            "approval": "环评批复",
            "calculate": "计算过程"
        }

        async def fetch_content_with_screenshots(data: Tuple[str, str, str]) -> Tuple[str, str, str, str, List[Tuple[str, str]], List[str]]:
            """ (文件名称, 文件链接, 文件类型, 文件内容, 页面数据列表[(页面文本, 图片路径)], 页面文本列表) """
            pdf_content = ""
            pages_data = []
            page_texts = []
            file_path = None

            try:
                # 下载PDF文件
                file_path = await download_file(data[1])

                # 转换PDF为文本（用于整体处理）
                pdf_content = pdf2text_str(file_path)

                # 按页面提取文本和图片
                pages_data = pdf_to_pages_with_images(file_path)
                page_texts = [page_text for page_text, _ in pages_data]

            except Exception as e:
                print(f"Error fetching content from {data[1]}: {e}")
                pass
            finally:
                if file_path:
                    delete_file(file_path)

            return (data[0], data[1], data[2], pdf_content, pages_data, page_texts)

        html_chunk_list = await self.crawl_persist(data_id)
        appendix_html = "".join([chunk.content for chunk in html_chunk_list if "相关附件" in chunk.chunk_type])

        file_list = await self._fetch_filelist_from_url_and_filter_with_llm(appendix_html)
        file_contents = await asyncio.gather(*(fetch_content_with_screenshots(data) for data in file_list))
        chunk_list: List[ChunkData] = []

        for _, file_url, file_type, content, pages_data, page_texts in file_contents:
            if not content.strip():
                continue

            # 获取文件ID用于OSS命名
            file_id = get_file_id_from_url(file_url)

            # 优化1: 先进行OSS批量上传（为LLM处理提供页面映射）
            page_to_oss_url = {}
            if oss_client.is_available() and pages_data:
                # 准备批量上传的文件列表
                file_uploads = []
                for page_num, (_, image_path) in enumerate(pages_data, 1):
                    # 命名格式: {{data_id}}_{{原file_id}}_{{page页码}}.png
                    object_name = f"review_pollution_files/{data_id}_{file_id}_{page_num}.png"
                    file_uploads.append((image_path, object_name))

                # 批量异步上传
                try:
                    upload_results = await oss_client.batch_upload_files(file_uploads)

                    # 构建页面到OSS URL的映射
                    for page_num, (_, image_path) in enumerate(pages_data, 1):
                        oss_url = upload_results.get(image_path)
                        if oss_url:
                            page_to_oss_url[page_num] = oss_url
                            print(f"PDF页面截图已上传到OSS: 第{page_num}页 -> {oss_url}")
                        else:
                            print(f"PDF页面截图上传OSS失败: 第{page_num}页")

                        # 删除临时图片文件
                        delete_file(image_path)

                except Exception as e:
                    print(f"批量上传PDF页面截图失败: {e}")
                    # 如果批量上传失败，回退到单个上传
                    for page_num, (_, image_path) in enumerate(pages_data, 1):
                        try:
                            object_name = f"review_pollution_files/{data_id}_{file_id}_{page_num}.png"
                            oss_url = await oss_client.async_upload_file(image_path, object_name)

                            if oss_url:
                                page_to_oss_url[page_num] = oss_url
                                print(f"PDF页面截图已上传到OSS: 第{page_num}页 -> {oss_url}")
                            else:
                                print(f"PDF页面截图上传OSS失败: 第{page_num}页")

                            # 删除临时图片文件
                            delete_file(image_path)

                        except Exception as e:
                            print(f"上传第{page_num}页图片失败: {e}")
            else:
                print("OSS不可用或无页面数据，跳过PDF截图上传")

            # 优化2: 在OSS上传完成后启动LLM解析任务
            llm_task = None
            if file_type == "report":
                # 异步启动LLM解析任务，传递页面信息和OSS映射
                llm_task = asyncio.create_task(self._parse_tag_with_llm(content, page_texts, page_to_oss_url))

            if file_type == "report" and llm_task:
                # 等待LLM解析完成（页面映射已在_parse_tag_with_llm中处理）
                cl = await llm_task
                chunk_list.extend(cl)

                # 添加完整文档，包含所有页面的OSS链接
                all_oss_urls = list(page_to_oss_url.values())
                chunk_list.append(ChunkData(
                    chunk_type=["报告文件全文"],
                    chunk_from="file",
                    content=content,
                    multy_file_list=all_oss_urls
                ))
            else:
                # 对于非报告类型，包含所有页面的OSS链接
                all_oss_urls = list(page_to_oss_url.values())
                chunk_list.append(ChunkData(
                    chunk_type=[file_type_map[file_type]],
                    chunk_from="file",
                    content=content,
                    multy_file_list=all_oss_urls
                ))

        return chunk_list

    async def crawl_from_file_persist(self, data_id: str) -> List[ChunkData]:
        """
        从文件中读取数据
        :param data_id: 数据 ID

        :return: 爬取到的文件内容
        """

        persist_data = self.persist_service.search(self.username, data_id)
        if persist_data:
            if persist_data.crawl_status in [CrawlStatus.FILE_DATA, CrawlStatus.BOTH_DATA]:
                return [chunk for chunk in persist_data.chunk_list if chunk.chunk_from == "file"]

        persist_data_new = PersistDataStruct(
            username=self.username,
            password=self.password,
            data_id=data_id,
            crawl_status=CrawlStatus.FILE_DATA,
            chunk_list= await self.crawl_from_file(data_id)
        )
        self.persist_service.persist(persist_data_new)
        return persist_data_new.chunk_list

    async def get_排污许可名录相关信息(self, data_id: str) -> dict:
        chunk_list = await self.crawl_persist(data_id)
        排污单位基本情况_html = "".join([chunk.content for chunk in chunk_list if "排污单位基本情况" in chunk.chunk_type])
        inf = self._parse_排污单位基本情况页面中的重点信息(排污单位基本情况_html)

        industry_code_list = []
        if inf["other_industry_code_list"]:
            industry_code_list.extend(inf["other_industry_code_list"])
        if inf["industry_code"]:
            industry_code_list.append(industry_code_list)

        # 查询判断是重点管理还是简化管理还是登记管理的条件
        result = []
        for i in industry_code_list:
            node = IT.get_node_by_id(i)
            if node:
                result.extend(node.get_排污许可名录())

        # 查询是否在重点监管名单中
        with open("source/重点单位.txt", "r", encoding="utf-8") as f:
            data = f.read()
        if inf["company_name"] in data:
            is_重点监管单位 = True
        else:
            is_重点监管单位 = False

        return {
            "单位名称": inf["company_name"],
            "是否在重点监管名单中": is_重点监管单位,
            "许可证管理类别判断依据": result,
        }

    async def login(self) -> bool:
        """
        模拟登录过程，captcha 参数需要自行获取后填写
        """
        # 1. 先获取登录页随机公钥和验证码
        fields = await self._parse_login_page()
        auth_session_key = fields["AUTH_SESSION_KEY"]
        modulus_hex = fields["hid_modulus"]
        exponent_hex = fields["hid_exponent"]

        captcha_code = await self._parse_captcha_code()

        # 3. 根据 js 代码，待加密的内容为：原始用户名/密码 + AUTH_SESSION_KEY
        plain_username = self.username + auth_session_key
        plain_password = self.password + auth_session_key

        # 4. 分别采用 RSA 加密
        encrypted_username = SecurityUtils.rsa_encrypt(plain_username, modulus_hex, exponent_hex)
        encrypted_password = SecurityUtils.rsa_encrypt(plain_password, modulus_hex, exponent_hex)

        # 5. 构造登录所需的表单数据
        payload = {
            "username": encrypted_username,
            "password": encrypted_password,
            "captcha": captcha_code,
            "lt": fields["lt"],
            "execution": fields["execution"],
            "_eventId": fields["_eventId"],
        }
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }

        async with self.session.post(self.login_url, data=payload, headers=headers) as resp:
            text = await resp.text()

        if "登录成功" in text:
            self.is_login = True
        else:
            self.is_login = False

        return self.is_login



    async def login_retry(self, retry_times: int = 10) -> bool:
        """
        登录失败重试
        """
        for _ in range(retry_times):
            await self.login()
            if self.is_login:
                return True

            # 登录失败，关闭当前 session 并重新创建, 防止某一窗口多次重试导致被 ban
            await self.session.close()
            self.session = aiohttp.ClientSession()

        return False

    async def fetch_datalist_from_html(self) -> List[Dict[str, Any]]:
        """
        从登录页 html 中提取 DATALIST
        """
        # 若没登陆, 尝试登陆
        if not self.is_login:
            await self.login_retry(10)
        if not self.is_login:
            raise Exception("登陆失败, 用户名或密码错误")

        urls = {
            "许可证重新申请": "https://permit.mee.gov.cn/permitExt/syssb/ckxm/ckxm!listCxsq.action?itemTypeID=XZXKTYPE_A&itemtype=TYPEI",
            "许可证首次申请": "https://permit.mee.gov.cn/permitExt/syssb/ckxm/ckxm!list.action?itemTypeID=XZXKTYPE_A&itemtype=TYPEA",
            "整改后申请": "https://permit.mee.gov.cn/permitExt/syssb/ckxm/ckxm!listBctb.action?itemTypeID=XZXKTYPE_A&itemtype=TYPEB",
            "许可证基本信息变更": "https://permit.mee.gov.cn/permitExt/syssb/ckxm/ckxm!listBcbg.action?itemTypeID=XZXKTYPE_C&itemtype=TYPEC&searchItem=TYPEC_2",
            "许可证变更": "https://permit.mee.gov.cn/permitExt/syssb/ckxm/ckxm!listBcbg.action?itemTypeID=XZXKTYPE_A&itemtype=TYPEC&searchItem=TYPEC_1",
        }

        datalist = []
        for category, url in urls.items():
            try:
                async with self.session.get(url, headers=self.headers) as resp:
                    text = await resp.text()
                soup = BeautifulSoup(text, "html.parser")
                rows = soup.find_all("tr", style=True)
                for row in rows:
                    cols = row.find_all("td")
                    if len(cols) >= 5:
                        links = row.find_all("a")
                        data_id = ""
                        for link in links:
                            onclick = link.get("href", "")
                            if "zxtb" in onclick:
                                params = re.findall(r"'([^']*)'", onclick)
                            if len(params) >= 4:
                                data_id = params[3]
                            break

                    item = {
                        "category": category,
                        "data_id": data_id,
                        "company_name": cols[1].get_text().strip(), # 单位名称
                        "review_status": cols[2].get_text().strip(), # 审核状态
                        "submit_time": cols[3].get_text().strip() # 提交时间
                    }
                    datalist.append(item)

            except Exception as e:
                print(f"Error fetching data from {url}: {e}")
                continue

        return datalist

    async def _fetch_filelist_from_url_and_filter_with_llm(self, appendix_html: str) -> List[Tuple[str, str, str]]:
        """
        从文件列表中获取文件链接，并过滤出符合条件的文件
        :param data_id: 数据 ID
        :return: 文件列表[(文件名称, 文件链接, 文件类型)]
        """
        llm_client = LLMClient()
        response = await llm_client.async_call_llm(
            messages=[
                {"role": "system", "content": SELECT_FILE_PROMPT},
                {"role": "user", "content": appendix_html}
            ]
        )
        print(response)

        try:
            file_dict = json.loads(extract_md_content(response, "json")[0])
        except ValueError:
            file_dict = json.loads(response)

        file_list = [
            (
                file_dict[f"{key}_file_name"], # 文件名称
                f'https://permit.mee.gov.cn/permitExt/upanddown.do?method=download&datafileid={file_dict[f"{key}_file_id"]}', # 文件 Url
                key # 文件类型
            )
            for key in ["report", "approval", "calculate"] if file_dict[f"{key}_file_name"] and file_dict[f"{key}_file_id"]
        ]

        return file_list

    async def _parse_tag_with_llm(self, text: str, page_texts: List[str] = None, page_to_oss_url: Dict[int, str] = None) -> List[ChunkData]:
        """
        使用 LLM 解析文本中的标签
        :param text: 文本内容
        :param page_texts: 按页面分割的文本列表
        :param page_to_oss_url: 页面编号到OSS链接的映射
        :return: 标签列表
        """
        semaphore = asyncio.Semaphore(50)

        async def call_llm(t: str, pages: List[int]) -> Optional[ChunkData]:
            async with semaphore:
                try:
                    llm_client = LLMClient()
                    response = await llm_client.async_call_llm(
                        messages=[
                            {"role": "system", "content": TAG_PROMPT},
                            {"role": "user", "content": t}
                        ]
                    )
                    try:
                        res_json = json.loads(extract_md_content(response, "json")[0])
                    except ValueError as e:
                        res_json = json.loads(response)

                    chunk_type_list = res_json.get("chunk_type_list", [])

                    # 根据页面信息设置OSS链接
                    chunk_oss_urls = []
                    if page_to_oss_url and pages:
                        chunk_oss_urls = [page_to_oss_url[page] for page in pages if page in page_to_oss_url]

                    return ChunkData(
                        chunk_type=chunk_type_list,
                        chunk_from="file",
                        content=t,
                        multy_file_list=chunk_oss_urls
                    )
                except Exception as e:
                    print(f"Error parsing text with LLM: {e}")
                    return None

        # 如果提供了页面信息，使用新的分割方法
        if page_texts is not None:
            chunks_with_pages = split_text_with_page_mapping(text, page_texts, 1000, 100)
            chunk_list = await asyncio.gather(*(call_llm(chunk_text, pages) for chunk_text, pages in chunks_with_pages))
        else:
            # 兼容旧的分割方法
            text_list = split_text(text, 1000, 100)
            chunk_list = await asyncio.gather(*(call_llm(t, []) for t in text_list))

        return [chunk for chunk in chunk_list if chunk]

    async def _load_cookies(self):
        # 先进入主页获取 cookies, 主页返回什么都不重要, 只要能获取到 cookies 就行
        async with self.session.get(self.base_url, headers=self.headers) as resp:
            await resp.text()

    async def _parse_login_page(self) -> dict:
        """
        从登录页 html 中提取隐藏字段：lt, execution, _eventId, AUTH_SESSION_KEY, hid_modulus, hid_exponent
        """
        async with self.session.get(self.login_url, headers=self.headers) as resp:
            text = await resp.text()
        soup = BeautifulSoup(text, "html.parser")

        lt = soup.find("input", attrs={"name": "lt"})["value"]
        execution = soup.find("input", attrs={"name": "execution"})["value"]
        event = soup.find("input", attrs={"name": "_eventId"})["value"]
        auth_session_key = soup.find("input", id="AUTH_SESSION_KEY")["value"]
        hid_modulus = soup.find("input", id="hid_modulus")["value"]
        hid_exponent = soup.find("input", id="hid_exponent")["value"]

        return {
            "lt": lt,
            "execution": execution,
            "_eventId": event,
            "AUTH_SESSION_KEY": auth_session_key,
            "hid_modulus": hid_modulus,
            "hid_exponent": hid_exponent,
        }

    async def _parse_captcha_code(self) -> str:
        """
        获取验证码图片并解析返回验证码内容
        """
        # 使用异步验证码客户端
        captcha_parser = AsyncCaptchaClient(self.session)
        for _ in range(3):
            try:
                captcha_base64 = await captcha_parser.captcha_base64(self.captcha_url)
                captcha = await captcha_parser.parse_captcha(captcha_base64)
                return captcha
            except:
                continue

    async def _fetch_card_url_list(self, dataid: str) -> List[Tuple[str, str]]:
        await self._load_cookies()

        url = "https://permit.mee.gov.cn/permitExt/common/itemcardflush/item-card-flush!itemcardmain.action"
        payload = f"state=暂存&cardtype=XZXKTYPE_A&cursel=card1&isAjax=1&operate=readonly&wwip=&itemtype=TYPEI&isyxedit=&dataid={dataid}"
        headers = {
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        }
        async with self.session.post(url, data=payload, headers=headers) as response:
            result_str = await response.text()
            result = json.loads(result_str)

        html_str = result.get("html")
        soup = BeautifulSoup(html_str, "html.parser")
        links = soup.find_all("a", href="#")

        card_url_list: List[Tuple[str, str]] = []
        for link in links:
            onclick_attr = link.get("onclick")
            title = link.get("title")
            if title == "阅读填报指南":
                continue

            if onclick_attr and "swtichCard" in onclick_attr:
                parts = onclick_attr.split(",")
                if len(parts) >= 3:
                    try:
                        url_path = parts[0].split("(")[1].strip().strip('"').strip("'")
                    except Exception:
                        continue
                    operate_mode = parts[1].strip().strip('"').strip("'")
                    full_url = urllib.parse.urljoin(url, url_path)
                    full_url = f"{full_url}?&dataid={dataid}&operate={operate_mode}"
                    full_url = full_url.replace("PWXKZFILE?", "PWXKZFILE")
                    card_url_list.append((title, full_url))

        return card_url_list

    # 懒的取名了, 直接用中文言简意赅
    def _parse_排污单位基本情况页面中的重点信息(self, html) -> dict:
        # --------------------------
        # 提取“排污许可证管理类别”
        # 注意这里先匹配<th>标签，再提取<td>标签内嵌套的两个<span>中的内容
        pattern_permit = r"<th>\s*排污许可证管理类别：\s*</th>\s*<td>\s*<span>\s*<span>\s*(.*?)\s*</span>"
        m_permit = re.search(pattern_permit, html, re.DOTALL)
        permit_category = m_permit.group(1).strip() if m_permit else None

        # --------------------------
        # 提取“单位名称”
        # 先匹配<th>单元，再取<td>标签中<span>里的文字
        pattern_company = r"<th>\s*单位名称：\s*</th>\s*<td>.*?<span>\s*(.*?)\s*</span>"
        m_company = re.search(pattern_company, html, re.DOTALL)
        company_name = m_company.group(1).strip() if m_company else None

        # --------------------------
        # 提取“行业类别及其代码”
        # 利用id=hyid来锁定行业代码对应的<input>标签
        pattern_industry = r"<th>\s*行业类别：\s*</th>\s*<td>.*?<span>\s*(.*?)\s*</span>.*?<input[^>]*?id=hyid[^>]*?value=([^ \t\n>]+)"
        m_industry = re.search(pattern_industry, html, re.DOTALL)
        if m_industry:
            industry_name = m_industry.group(1).strip()
            industry_code = m_industry.group(2).strip()
        else:
            industry_name = industry_code = None

        # --------------------------
        # 提取“其他行业类别及其代码”
        # 利用id=qthyid锁定对应的<input>标签
        pattern_other = r"<th>\s*其他行业类别：\s*</th>\s*<td>.*?<span>\s*(.*?)\s*</span>.*?<input[^>]*?id=qthyid[^>]*?value=([^ \t\n>]+)"
        m_other = re.search(pattern_other, html, re.DOTALL)
        if m_other:
            other_industry_name = m_other.group(1).strip().split(",")
            other_industry_code = m_other.group(2).strip().split(",")
        else:
            other_industry_name = other_industry_code = None

        # --------------------------
        # print("排污许可证管理类别:", permit_category)
        # print("单位名称:", company_name)
        # print("行业类别:", industry_name, ", 代码:", industry_code)
        # print("其他行业类别:", other_industry_name, ", 代码:", other_industry_code)

        return {
            "permit_category": permit_category,
            "company_name": company_name,
            "industry_name": industry_name,
            "industry_code": industry_code,
            "other_industry_name_list": other_industry_name,
            "other_industry_code_list": other_industry_code
        }

    def _get_cookies_str(self) -> str:
        """获取session中的所有cookie字符串"""
        cookies = self.session.cookie_jar
        cookies_str =  ";".join([f"{cookie.key}={cookie.value}" for cookie in cookies])
        return cookies_str



async def test():
    username = "hainingyuehai"
    password = "Sj1234567"
    client = AsyncPermitMeeClient(username, password)
    # await client.login_retry()
    # await client._load_cookies()
    # client.get_cookies_str()
    results = await client.get_排污许可名录相关信息("ecbe689dacd14a7eb419551f3bb5751e")

    print("Results:", results)
    # for result in results:
    #     print(f"Title: {result.chunk_type}, Content: {result.content[:100]}...")  # Print first 100 chars of content
    # await client.session.close()


# asyncio.run(test())