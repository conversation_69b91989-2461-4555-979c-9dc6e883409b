import aiohttp
import tempfile
import subprocess
import os

from typing import List, <PERSON><PERSON>
from pathlib import Path
from subprocess import PIPE


# 换页符(^L)
PDF_PAGE_SIGN = ""


def check_pdftotext():
    """检查系统中是否安装了pdftotext工具

    Raises:
        RuntimeError: 未安装pdftotext时抛出异常
    """
    try:
        subprocess.run(["pdftotext", "-v"], stdout=PIPE, stderr=PIPE)
    except FileNotFoundError:
        raise RuntimeError("未找到pdftotext命令, 请确保已安装poppler-utils, 安装指令: apt-get install poppler-utils")


async def download_file(url: str, suffix: str = "") -> str:
    """从URL下载文件并保存到临时文件

    Args:
        url (str): 要下载的文件URL
        suffix (str): 可选的文件后缀，包含点号（例如 '.png'）

    Returns:
        str: 下载的临时文件路径

    Raises:
        ConnectionRefusedError: 下载失败时抛出异常
    """

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive"
    }

    with tempfile.NamedTemporaryFile(suffix=suffix, delete=False) as tmp_file:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    tmp_file.write(await response.read())
                    return tmp_file.name
                else:
                    raise ConnectionRefusedError(f"Download Error: {url}")


def delete_file(file_name: str):
    """删除指定的文件，如果文件存在

    Args:
        file_name (str): 要删除的文件路径

    Raises:
        PermissionError: 没有权限删除文件时抛出异常
    """
    path = Path(file_name)
    if path.exists():
        try:
            path.unlink()
        except PermissionError as e:
            raise PermissionError(f"没有权限删除文件: {str(e)}")


def pdf2text_str(file_path: str) -> str:
    """将PDF文件转换为文本

    Args:
        file_path (str): PDF文件路径

    Returns:
        str: 转换后的文本内容

    Raises:
        FileNotFoundError: 文件不存在时抛出异常
        RuntimeError: 转换失败时抛出异常
    """
    if not Path(file_path).exists():
        raise FileNotFoundError("PDF文件不存在")

    try:
        result = subprocess.run(["pdftotext", file_path, "-layout", "-"], stdout=PIPE, stderr=PIPE)

        if result.returncode != 0:
            raise RuntimeError(f"PDF转换失败: {result.stderr.decode('utf-8')}")

        return result.stdout.decode('utf-8')
    except FileNotFoundError:
        raise RuntimeError("未找到pdftotext命令, 请确保已安装poppler-utils")


def pdf2text_vec(file_path: str) -> List[str]:
    """将PDF文件转换为文本列表

    Args:
        file_path (str): PDF文件路径

    Returns:
        List[str]: 转换后的文本内容按照页码划分的数组

    Raises:
        FileNotFoundError: 文件不存在时抛出异常
        RuntimeError: 转换失败时抛出异常
    """
    pdf_text = pdf2text_str(file_path)
    return pdf_text.split(PDF_PAGE_SIGN)


def pdf2text_chunk(file_path: str, chunk_size: int) -> List[str]:
    """将PDF文件转换为文本块

    Args:
        file_path (str): PDF文件路径
        chunk_size (int): 每个文本块的大小

    Returns:
        List[str]: 转换后的文本块列表

    Raises:
        FileNotFoundError: 文件不存在时抛出异常
        RuntimeError: 转换失败时抛出异常
    """
    pdf_text = pdf2text_str(file_path)
    return [
        pdf_text[i:i + chunk_size]
        for i in range(0, len(pdf_text), chunk_size)
    ]

def split_text(text: str, chunk_size: int, overlap: int) -> List[str]:
    """将文本分割为多个块

    Args:
        text (str): 要分割的文本
        chunk_size (int): 每个块的大小
        overlap (int): 重叠的字符数

    Returns:
        List[str]: 分割后的文本块列表
    """
    return [
        text[i:i + chunk_size]
        for i in range(0, len(text), chunk_size - overlap)
    ]


def pdf_to_images(file_path: str, output_dir: str = None) -> List[str]:
    """将PDF文件转换为PNG图片

    Args:
        file_path (str): PDF文件路径
        output_dir (str): 输出目录，如果为None则使用临时目录

    Returns:
        List[str]: 生成的PNG图片文件路径列表，按页码顺序排列

    Raises:
        FileNotFoundError: 文件不存在时抛出异常
        RuntimeError: 转换失败时抛出异常
    """
    if not Path(file_path).exists():
        raise FileNotFoundError("PDF文件不存在")

    # 如果没有指定输出目录，使用临时目录
    if output_dir is None:
        output_dir = tempfile.mkdtemp()
    else:
        os.makedirs(output_dir, exist_ok=True)

    # 生成输出文件前缀
    output_prefix = os.path.join(output_dir, "page")

    try:
        # 使用pdftoppm将PDF转换为PNG图片
        # -png: 输出PNG格式
        # -r 150: 设置分辨率为150 DPI
        result = subprocess.run([
            "pdftoppm",
            "-png",
            "-r", "150",
            file_path,
            output_prefix
        ], stdout=PIPE, stderr=PIPE)

        if result.returncode != 0:
            raise RuntimeError(f"PDF转图片失败: {result.stderr.decode('utf-8')}")

        # 查找生成的PNG文件
        png_files = []
        for file in os.listdir(output_dir):
            if file.startswith("page") and file.endswith(".png"):
                png_files.append(os.path.join(output_dir, file))

        # 按文件名排序，确保页码顺序正确
        png_files.sort()

        return png_files

    except FileNotFoundError:
        raise RuntimeError("未找到pdftoppm命令, 请确保已安装poppler-utils")


def pdf_to_pages_with_images(file_path: str) -> List[Tuple[str, str]]:
    """将PDF文件按页面转换为文本和图片的对应关系

    Args:
        file_path (str): PDF文件路径

    Returns:
        List[Tuple[str, str]]: 每页的(文本内容, 图片文件路径)列表

    Raises:
        FileNotFoundError: 文件不存在时抛出异常
        RuntimeError: 转换失败时抛出异常
    """
    if not Path(file_path).exists():
        raise FileNotFoundError("PDF文件不存在")

    # 创建临时目录
    temp_dir = tempfile.mkdtemp()

    try:
        # 1. 转换PDF为图片
        image_paths = pdf_to_images(file_path, temp_dir)

        # 2. 按页面提取文本
        page_texts = []
        for i in range(len(image_paths)):
            page_num = i + 1
            try:
                # 使用pdftotext提取单页文本
                result = subprocess.run([
                    "pdftotext",
                    "-f", str(page_num),  # 起始页
                    "-l", str(page_num),  # 结束页
                    "-layout",
                    file_path,
                    "-"
                ], stdout=PIPE, stderr=PIPE)

                if result.returncode == 0:
                    page_text = result.stdout.decode('utf-8')
                    page_texts.append(page_text)
                else:
                    page_texts.append("")  # 如果提取失败，使用空字符串

            except Exception as e:
                print(f"提取第{page_num}页文本失败: {e}")
                page_texts.append("")

        # 3. 组合文本和图片路径
        pages_data = []
        for i, (text, image_path) in enumerate(zip(page_texts, image_paths)):
            pages_data.append((text, image_path))

        return pages_data

    except Exception as e:
        # 清理临时文件
        if 'image_paths' in locals():
            for image_path in image_paths:
                delete_file(image_path)
        raise e


def get_file_id_from_url(url: str) -> str:
    """从下载URL中提取文件ID

    Args:
        url (str): 下载URL，格式如: https://permit.mee.gov.cn/permitExt/upanddown.do?method=download&datafileid=xxx

    Returns:
        str: 提取的文件ID
    """
    try:
        # 从URL中提取datafileid参数
        if "datafileid=" in url:
            return url.split("datafileid=")[1].split("&")[0]
        else:
            # 如果无法提取，返回一个默认值
            return "unknown"
    except Exception:
        return "unknown"


def split_text_with_page_mapping(text: str, page_texts: List[str], chunk_size: int, overlap: int) -> List[Tuple[str, List[int]]]:
    """将文本分割为多个块，同时记录每个块对应的页面

    Args:
        text (str): 要分割的完整文本
        page_texts (List[str]): 按页面分割的文本列表
        chunk_size (int): 每个块的大小
        overlap (int): 重叠的字符数

    Returns:
        List[Tuple[str, List[int]]]: 分割后的文本块列表，每个元素为(文本块, 对应页面编号列表)
    """
    # 首先创建页面位置映射
    page_positions = []
    current_pos = 0

    for page_text in page_texts:
        # 在完整文本中查找页面文本的位置
        page_start = text.find(page_text.strip(), current_pos) if page_text.strip() else -1
        if page_start != -1:
            page_end = page_start + len(page_text.strip())
            page_positions.append((page_start, page_end))
            current_pos = page_start + 1  # 从下一个位置开始查找
        else:
            # 如果找不到精确匹配，使用估算位置
            estimated_start = current_pos
            estimated_end = min(current_pos + len(page_text), len(text))
            page_positions.append((estimated_start, estimated_end))
            current_pos = estimated_end

    # 分割文本
    chunks_with_pages = []
    for i in range(0, len(text), chunk_size - overlap):
        chunk_text = text[i:i + chunk_size]
        chunk_start = i
        chunk_end = i + len(chunk_text)

        # 找到与当前文本块重叠的页面
        chunk_pages = []
        for page_idx, (page_start, page_end) in enumerate(page_positions):
            # 检查文本块和页面是否有重叠
            if chunk_start < page_end and chunk_end > page_start:
                chunk_pages.append(page_idx + 1)  # 页面编号从1开始

        # 如果没有找到重叠的页面，使用最接近的页面
        if not chunk_pages:
            # 找到最接近的页面
            chunk_center = (chunk_start + chunk_end) / 2
            closest_page = 1
            min_distance = float('inf')

            for page_idx, (page_start, page_end) in enumerate(page_positions):
                page_center = (page_start + page_end) / 2
                distance = abs(chunk_center - page_center)
                if distance < min_distance:
                    min_distance = distance
                    closest_page = page_idx + 1

            chunk_pages.append(closest_page)

        chunks_with_pages.append((chunk_text, chunk_pages))

    return chunks_with_pages


def map_text_chunks_to_pages(text_chunks: List[str], page_texts: List[str]) -> List[List[int]]:
    """将文本块映射到对应的页面

    Args:
        text_chunks (List[str]): 文本块列表
        page_texts (List[str]): 按页面分割的文本列表

    Returns:
        List[List[int]]: 每个文本块对应的页面编号列表（从1开始）
    """
    chunk_to_pages = []

    for chunk in text_chunks:
        chunk_pages = []
        chunk_text = chunk.strip()

        # 如果文本块为空，跳过
        if not chunk_text:
            chunk_to_pages.append([])
            continue

        # 计算每个页面与当前文本块的相似度
        page_scores = []
        for page_idx, page_text in enumerate(page_texts):
            page_text = page_text.strip()

            if not page_text:
                page_scores.append(0)
                continue

            # 方法1: 直接字符串包含检查
            contains_score = 0
            if chunk_text in page_text:
                contains_score = 1.0
            elif page_text in chunk_text:
                contains_score = 0.8

            # 方法2: 词汇重叠度
            chunk_words = set(chunk_text.split())
            page_words = set(page_text.split())

            if chunk_words and page_words:
                intersection = chunk_words.intersection(page_words)
                overlap_ratio = len(intersection) / len(chunk_words)
            else:
                overlap_ratio = 0

            # 方法3: 字符级相似度（简单版本）
            char_similarity = 0
            if chunk_text and page_text:
                # 计算共同字符的比例
                chunk_chars = set(chunk_text)
                page_chars = set(page_text)
                common_chars = chunk_chars.intersection(page_chars)
                char_similarity = len(common_chars) / len(chunk_chars) if chunk_chars else 0

            # 综合评分
            final_score = max(contains_score, overlap_ratio * 0.7, char_similarity * 0.3)
            page_scores.append(final_score)

        # 找到评分大于阈值的页面
        threshold = 0.1  # 10%的相似度阈值
        for page_idx, score in enumerate(page_scores):
            if score >= threshold:
                chunk_pages.append(page_idx + 1)  # 页面编号从1开始

        # 如果没有找到匹配的页面，使用最高分的页面
        if not chunk_pages and page_scores:
            best_page = page_scores.index(max(page_scores))
            chunk_pages.append(best_page + 1)

        chunk_to_pages.append(chunk_pages)

    return chunk_to_pages


def test():
    """测试函数"""
    check_pdftotext()
    file_path = "/data6/source/eia_pdf_rust_version_20250217/073e86e4ea7544f08e84c652fc95f147.pdf"

    # 测试PDF转文本
    text = pdf2text_str(file_path)
    print(f"PDF文本长度: {len(text)}")

    # 测试新的按页面提取功能
    try:
        pages_data = pdf_to_pages_with_images(file_path)
        print(f"提取了 {len(pages_data)} 个页面:")

        # 只显示前3页的信息
        for i, (page_text, image_path) in enumerate(pages_data[:3]):
            print(f"  页面 {i+1}: 文本长度={len(page_text)}, 图片={image_path}")

        # 测试文本块映射
        page_texts = [page_text for page_text, _ in pages_data]
        test_chunks = [text[:1000], text[1000:2000]]  # 测试两个文本块
        chunk_to_pages = map_text_chunks_to_pages(test_chunks, page_texts)
        print(f"文本块映射结果: {chunk_to_pages}")

        # 清理测试文件
        for _, image_path in pages_data:
            delete_file(image_path)

    except Exception as e:
        print(f"PDF按页面处理测试失败: {e}")

    # 测试URL解析
    test_url = "https://permit.mee.gov.cn/permitExt/upanddown.do?method=download&datafileid=12345"
    file_id = get_file_id_from_url(test_url)
    print(f"从URL提取的文件ID: {file_id}")

# test()