[{"id": "A", "pId": "", "singlehyname": "农、林、牧、渔业", "name": "农、林、牧、渔业(A)", "isParent": true, "parentcode": ""}, {"id": "A01", "pId": "A", "singlehyname": "农业", "name": "农业(A01)", "isParent": true, "parentcode": "A"}, {"id": "A011", "pId": "A01", "singlehyname": "谷物种植", "name": "谷物种植(A011)", "isParent": true, "parentcode": "A01"}, {"id": "A0111", "pId": "A011", "singlehyname": "稻谷种植", "name": "稻谷种植(A0111)", "isParent": false, "parentcode": "A011"}, {"id": "A0112", "pId": "A011", "singlehyname": "小麦种植", "name": "小麦种植(A0112)", "isParent": false, "parentcode": "A011"}, {"id": "A0113", "pId": "A011", "singlehyname": "玉米种植", "name": "玉米种植(A0113)", "isParent": false, "parentcode": "A011"}, {"id": "A0119", "pId": "A011", "singlehyname": "其他谷物种植", "name": "其他谷物种植(A0119)", "isParent": false, "parentcode": "A011"}, {"id": "A012", "pId": "A01", "singlehyname": "豆类、油料和薯类种植", "name": "豆类、油料和薯类种植(A012)", "isParent": true, "parentcode": "A01"}, {"id": "A0121", "pId": "A012", "singlehyname": "豆类种植", "name": "豆类种植(A0121)", "isParent": false, "parentcode": "A012"}, {"id": "A0122", "pId": "A012", "singlehyname": "油料种植", "name": "油料种植(A0122)", "isParent": false, "parentcode": "A012"}, {"id": "A0123", "pId": "A012", "singlehyname": "薯类种植", "name": "薯类种植(A0123)", "isParent": false, "parentcode": "A012"}, {"id": "A013", "pId": "A01", "singlehyname": "棉、麻、糖、烟草种植", "name": "棉、麻、糖、烟草种植(A013)", "isParent": true, "parentcode": "A01"}, {"id": "A0131", "pId": "A013", "singlehyname": "棉花种植", "name": "棉花种植(A0131)", "isParent": false, "parentcode": "A013"}, {"id": "A0132", "pId": "A013", "singlehyname": "麻类种植", "name": "麻类种植(A0132)", "isParent": false, "parentcode": "A013"}, {"id": "A0133", "pId": "A013", "singlehyname": "糖料种植", "name": "糖料种植(A0133)", "isParent": false, "parentcode": "A013"}, {"id": "A0134", "pId": "A013", "singlehyname": "烟草种植", "name": "烟草种植(A0134)", "isParent": false, "parentcode": "A013"}, {"id": "A014", "pId": "A01", "singlehyname": "蔬菜、食用菌及园艺作物种植", "name": "蔬菜、食用菌及园艺作物种植(A014)", "isParent": true, "parentcode": "A01"}, {"id": "A0141", "pId": "A014", "singlehyname": "蔬菜种植", "name": "蔬菜种植(A0141)", "isParent": false, "parentcode": "A014"}, {"id": "A0142", "pId": "A014", "singlehyname": "食用菌种植", "name": "食用菌种植(A0142)", "isParent": false, "parentcode": "A014"}, {"id": "A0143", "pId": "A014", "singlehyname": "花卉种植", "name": "花卉种植(A0143)", "isParent": false, "parentcode": "A014"}, {"id": "A0149", "pId": "A014", "singlehyname": "其他园艺作物种植", "name": "其他园艺作物种植(A0149)", "isParent": false, "parentcode": "A014"}, {"id": "A015", "pId": "A01", "singlehyname": "水果种植", "name": "水果种植(A015)", "isParent": true, "parentcode": "A01"}, {"id": "A0151", "pId": "A015", "singlehyname": "仁果类和核果类水果种植", "name": "仁果类和核果类水果种植(A0151)", "isParent": false, "parentcode": "A015"}, {"id": "A0152", "pId": "A015", "singlehyname": "葡萄种植", "name": "葡萄种植(A0152)", "isParent": false, "parentcode": "A015"}, {"id": "A0153", "pId": "A015", "singlehyname": "柑橘类种植", "name": "柑橘类种植(A0153)", "isParent": false, "parentcode": "A015"}, {"id": "A0154", "pId": "A015", "singlehyname": "香蕉等亚热带水果种植", "name": "香蕉等亚热带水果种植(A0154)", "isParent": false, "parentcode": "A015"}, {"id": "A0159", "pId": "A015", "singlehyname": "其他水果种植", "name": "其他水果种植(A0159)", "isParent": false, "parentcode": "A015"}, {"id": "A016", "pId": "A01", "singlehyname": "坚果、含油果、香料和饮料作物种植", "name": "坚果、含油果、香料和饮料作物种植(A016)", "isParent": true, "parentcode": "A01"}, {"id": "A0161", "pId": "A016", "singlehyname": "坚果种植", "name": "坚果种植(A0161)", "isParent": false, "parentcode": "A016"}, {"id": "A0162", "pId": "A016", "singlehyname": "含油果种植", "name": "含油果种植(A0162)", "isParent": false, "parentcode": "A016"}, {"id": "A0163", "pId": "A016", "singlehyname": "香料作物种植", "name": "香料作物种植(A0163)", "isParent": false, "parentcode": "A016"}, {"id": "A0164", "pId": "A016", "singlehyname": "茶叶种植", "name": "茶叶种植(A0164)", "isParent": false, "parentcode": "A016"}, {"id": "A0169", "pId": "A016", "singlehyname": "其他饮料作物种植", "name": "其他饮料作物种植(A0169)", "isParent": false, "parentcode": "A016"}, {"id": "A017", "pId": "A01", "singlehyname": "中药材种植", "name": "中药材种植(A017)", "isParent": true, "parentcode": "A01"}, {"id": "A0171", "pId": "A017", "singlehyname": "中草药种植", "name": "中草药种植(A0171)", "isParent": false, "parentcode": "A017"}, {"id": "A0179", "pId": "A017", "singlehyname": "其他中药材种植", "name": "其他中药材种植(A0179)", "isParent": false, "parentcode": "A017"}, {"id": "A018", "pId": "A01", "singlehyname": "草种植及割草", "name": "草种植及割草(A018)", "isParent": true, "parentcode": "A01"}, {"id": "A0181", "pId": "A018", "singlehyname": "草种植", "name": "草种植(A0181)", "isParent": false, "parentcode": "A018"}, {"id": "A0182", "pId": "A018", "singlehyname": "天然草原割草", "name": "天然草原割草(A0182)", "isParent": false, "parentcode": "A018"}, {"id": "A019", "pId": "A01", "singlehyname": "其他农业", "name": "其他农业(A019)", "isParent": false, "parentcode": "A01"}, {"id": "A02", "pId": "A", "singlehyname": "林业", "name": "林业(A02)", "isParent": true, "parentcode": "A"}, {"id": "A021", "pId": "A02", "singlehyname": "林木育种和育苗", "name": "林木育种和育苗(A021)", "isParent": true, "parentcode": "A02"}, {"id": "A0211", "pId": "A021", "singlehyname": "林木育种", "name": "林木育种(A0211)", "isParent": false, "parentcode": "A021"}, {"id": "A0212", "pId": "A021", "singlehyname": "林木育苗", "name": "林木育苗(A0212)", "isParent": false, "parentcode": "A021"}, {"id": "A022", "pId": "A02", "singlehyname": "造林和更新", "name": "造林和更新(A022)", "isParent": false, "parentcode": "A02"}, {"id": "A023", "pId": "A02", "singlehyname": "森林经营、管护和改培", "name": "森林经营、管护和改培(A023)", "isParent": true, "parentcode": "A02"}, {"id": "A0231", "pId": "A023", "singlehyname": "森林经营和管护", "name": "森林经营和管护(A0231)", "isParent": false, "parentcode": "A023"}, {"id": "A0232", "pId": "A023", "singlehyname": "森林改培", "name": "森林改培(A0232)", "isParent": false, "parentcode": "A023"}, {"id": "A024", "pId": "A02", "singlehyname": "木材和竹材采运", "name": "木材和竹材采运(A024)", "isParent": true, "parentcode": "A02"}, {"id": "A0241", "pId": "A024", "singlehyname": "木材采运", "name": "木材采运(A0241)", "isParent": false, "parentcode": "A024"}, {"id": "A0242", "pId": "A024", "singlehyname": "竹材采运", "name": "竹材采运(A0242)", "isParent": false, "parentcode": "A024"}, {"id": "A025", "pId": "A02", "singlehyname": "林产品采集", "name": "林产品采集(A025)", "isParent": true, "parentcode": "A02"}, {"id": "A0251", "pId": "A025", "singlehyname": "木竹材林产品采集", "name": "木竹材林产品采集(A0251)", "isParent": false, "parentcode": "A025"}, {"id": "A0252", "pId": "A025", "singlehyname": "非木竹材林产品采集", "name": "非木竹材林产品采集(A0252)", "isParent": false, "parentcode": "A025"}, {"id": "A03", "pId": "A", "singlehyname": "畜牧业", "name": "畜牧业(A03)", "isParent": true, "parentcode": "A"}, {"id": "A031", "pId": "A03", "singlehyname": "牲畜饲养", "name": "牲畜饲养(A031)", "isParent": true, "parentcode": "A03"}, {"id": "A0311", "pId": "A031", "singlehyname": "牛的饲养", "name": "牛的饲养(A0311)", "isParent": false, "parentcode": "A031"}, {"id": "A0312", "pId": "A031", "singlehyname": "马的饲养", "name": "马的饲养(A0312)", "isParent": false, "parentcode": "A031"}, {"id": "A0313", "pId": "A031", "singlehyname": "猪的饲养", "name": "猪的饲养(A0313)", "isParent": false, "parentcode": "A031"}, {"id": "A0314", "pId": "A031", "singlehyname": "羊的饲养", "name": "羊的饲养(A0314)", "isParent": false, "parentcode": "A031"}, {"id": "A0315", "pId": "A031", "singlehyname": "骆驼饲养", "name": "骆驼饲养(A0315)", "isParent": false, "parentcode": "A031"}, {"id": "A0319", "pId": "A031", "singlehyname": "其他牲畜饲养", "name": "其他牲畜饲养(A0319)", "isParent": false, "parentcode": "A031"}, {"id": "A032", "pId": "A03", "singlehyname": "家禽饲养", "name": "家禽饲养(A032)", "isParent": true, "parentcode": "A03"}, {"id": "A0321", "pId": "A032", "singlehyname": "鸡的饲养", "name": "鸡的饲养(A0321)", "isParent": false, "parentcode": "A032"}, {"id": "A0322", "pId": "A032", "singlehyname": "鸭的饲养", "name": "鸭的饲养(A0322)", "isParent": false, "parentcode": "A032"}, {"id": "A0323", "pId": "A032", "singlehyname": "鹅的饲养", "name": "鹅的饲养(A0323)", "isParent": false, "parentcode": "A032"}, {"id": "A0329", "pId": "A032", "singlehyname": "其他家禽饲养", "name": "其他家禽饲养(A0329)", "isParent": false, "parentcode": "A032"}, {"id": "A033", "pId": "A03", "singlehyname": "狩猎和捕捉动物", "name": "狩猎和捕捉动物(A033)", "isParent": false, "parentcode": "A03"}, {"id": "A039", "pId": "A03", "singlehyname": "其他畜牧业", "name": "其他畜牧业(A039)", "isParent": true, "parentcode": "A03"}, {"id": "A0391", "pId": "A039", "singlehyname": "兔的饲养", "name": "兔的饲养(A0391)", "isParent": false, "parentcode": "A039"}, {"id": "A0392", "pId": "A039", "singlehyname": "蜜蜂的饲养", "name": "蜜蜂的饲养(A0392)", "isParent": false, "parentcode": "A039"}, {"id": "A0399", "pId": "A039", "singlehyname": "其他未列明畜牧业", "name": "其他未列明畜牧业(A0399)", "isParent": false, "parentcode": "A039"}, {"id": "A04", "pId": "A", "singlehyname": "渔业", "name": "渔业(A04)", "isParent": true, "parentcode": "A"}, {"id": "A041", "pId": "A04", "singlehyname": "水产养殖", "name": "水产养殖(A041)", "isParent": true, "parentcode": "A04"}, {"id": "A0411", "pId": "A041", "singlehyname": "海水养殖", "name": "海水养殖(A0411)", "isParent": false, "parentcode": "A041"}, {"id": "A0412", "pId": "A041", "singlehyname": "内陆养殖", "name": "内陆养殖(A0412)", "isParent": false, "parentcode": "A041"}, {"id": "A042", "pId": "A04", "singlehyname": "水产捕捞", "name": "水产捕捞(A042)", "isParent": true, "parentcode": "A04"}, {"id": "A0421", "pId": "A042", "singlehyname": "海水捕捞", "name": "海水捕捞(A0421)", "isParent": false, "parentcode": "A042"}, {"id": "A0422", "pId": "A042", "singlehyname": "内陆捕捞", "name": "内陆捕捞(A0422)", "isParent": false, "parentcode": "A042"}, {"id": "A05", "pId": "A", "singlehyname": "农、林、牧、渔专业及辅助性活动", "name": "农、林、牧、渔专业及辅助性活动(A05)", "isParent": true, "parentcode": "A"}, {"id": "A051", "pId": "A05", "singlehyname": "农业专业及辅助性活动", "name": "农业专业及辅助性活动(A051)", "isParent": true, "parentcode": "A05"}, {"id": "A0511", "pId": "A051", "singlehyname": "种子种苗培育活动", "name": "种子种苗培育活动(A0511)", "isParent": false, "parentcode": "A051"}, {"id": "A0512", "pId": "A051", "singlehyname": "农业机械活动", "name": "农业机械活动(A0512)", "isParent": false, "parentcode": "A051"}, {"id": "A0513", "pId": "A051", "singlehyname": "灌溉活动", "name": "灌溉活动(A0513)", "isParent": false, "parentcode": "A051"}, {"id": "A0514", "pId": "A051", "singlehyname": "农产品初加工活动", "name": "农产品初加工活动(A0514)", "isParent": false, "parentcode": "A051"}, {"id": "A0515", "pId": "A051", "singlehyname": "农作物病虫害防治活动", "name": "农作物病虫害防治活动(A0515)", "isParent": false, "parentcode": "A051"}, {"id": "A0519", "pId": "A051", "singlehyname": "其他农业专业及辅助性活动", "name": "其他农业专业及辅助性活动(A0519)", "isParent": false, "parentcode": "A051"}, {"id": "A052", "pId": "A05", "singlehyname": "林业专业及辅助性活动", "name": "林业专业及辅助性活动(A052)", "isParent": true, "parentcode": "A05"}, {"id": "A0521", "pId": "A052", "singlehyname": "林业有害生物防治活动", "name": "林业有害生物防治活动(A0521)", "isParent": false, "parentcode": "A052"}, {"id": "A0522", "pId": "A052", "singlehyname": "森林防火活动", "name": "森林防火活动(A0522)", "isParent": false, "parentcode": "A052"}, {"id": "A0523", "pId": "A052", "singlehyname": "林产品初级加工活动", "name": "林产品初级加工活动(A0523)", "isParent": false, "parentcode": "A052"}, {"id": "A0529", "pId": "A052", "singlehyname": "其他林业专业及辅助性活动", "name": "其他林业专业及辅助性活动(A0529)", "isParent": false, "parentcode": "A052"}, {"id": "A053", "pId": "A05", "singlehyname": "畜牧专业及辅助性活动", "name": "畜牧专业及辅助性活动(A053)", "isParent": true, "parentcode": "A05"}, {"id": "A0531", "pId": "A053", "singlehyname": "畜牧良种繁殖活动", "name": "畜牧良种繁殖活动(A0531)", "isParent": false, "parentcode": "A053"}, {"id": "A0532", "pId": "A053", "singlehyname": "畜禽粪污处理活动", "name": "畜禽粪污处理活动(A0532)", "isParent": false, "parentcode": "A053"}, {"id": "A0539", "pId": "A053", "singlehyname": "其他畜牧专业及辅助性活动", "name": "其他畜牧专业及辅助性活动(A0539)", "isParent": false, "parentcode": "A053"}, {"id": "A054", "pId": "A05", "singlehyname": "渔业专业及辅助性活动", "name": "渔业专业及辅助性活动(A054)", "isParent": true, "parentcode": "A05"}, {"id": "A0541", "pId": "A054", "singlehyname": "鱼苗及鱼种场活动", "name": "鱼苗及鱼种场活动(A0541)", "isParent": false, "parentcode": "A054"}, {"id": "A0549", "pId": "A054", "singlehyname": "其他渔业专业及辅助性活动", "name": "其他渔业专业及辅助性活动(A0549)", "isParent": false, "parentcode": "A054"}, {"id": "B", "pId": "", "singlehyname": "采矿业", "name": "采矿业(B)", "isParent": true, "parentcode": ""}, {"id": "B06", "pId": "B", "singlehyname": "煤炭开采和洗选业", "name": "煤炭开采和洗选业(B06)", "isParent": true, "parentcode": "B"}, {"id": "B061", "pId": "B06", "singlehyname": "烟煤和无烟煤开采洗选", "name": "烟煤和无烟煤开采洗选(B061)", "isParent": false, "parentcode": "B06"}, {"id": "B062", "pId": "B06", "singlehyname": "褐煤开采洗选", "name": "褐煤开采洗选(B062)", "isParent": false, "parentcode": "B06"}, {"id": "B069", "pId": "B06", "singlehyname": "其他煤炭采选", "name": "其他煤炭采选(B069)", "isParent": false, "parentcode": "B06"}, {"id": "B07", "pId": "B", "singlehyname": "石油和天然气开采业", "name": "石油和天然气开采业(B07)", "isParent": true, "parentcode": "B"}, {"id": "B071", "pId": "B07", "singlehyname": "石油开采", "name": "石油开采(B071)", "isParent": true, "parentcode": "B07"}, {"id": "B0711", "pId": "B071", "singlehyname": "陆地石油开采", "name": "陆地石油开采(B0711)", "isParent": false, "parentcode": "B071"}, {"id": "B0712", "pId": "B071", "singlehyname": "海洋石油开采", "name": "海洋石油开采(B0712)", "isParent": false, "parentcode": "B071"}, {"id": "B072", "pId": "B07", "singlehyname": "天然气开采", "name": "天然气开采(B072)", "isParent": true, "parentcode": "B07"}, {"id": "B0721", "pId": "B072", "singlehyname": "陆地天然气开采", "name": "陆地天然气开采(B0721)", "isParent": false, "parentcode": "B072"}, {"id": "B0722", "pId": "B072", "singlehyname": "海洋天然气及可燃冰开采", "name": "海洋天然气及可燃冰开采(B0722)", "isParent": false, "parentcode": "B072"}, {"id": "B08", "pId": "B", "singlehyname": "黑色金属矿采选业", "name": "黑色金属矿采选业(B08)", "isParent": true, "parentcode": "B"}, {"id": "B081", "pId": "B08", "singlehyname": "铁矿采选", "name": "铁矿采选(B081)", "isParent": false, "parentcode": "B08"}, {"id": "B082", "pId": "B08", "singlehyname": "锰矿、铬矿采选", "name": "锰矿、铬矿采选(B082)", "isParent": false, "parentcode": "B08"}, {"id": "B089", "pId": "B08", "singlehyname": "其他黑色金属矿采选", "name": "其他黑色金属矿采选(B089)", "isParent": false, "parentcode": "B08"}, {"id": "B09", "pId": "B", "singlehyname": "有色金属矿采选业", "name": "有色金属矿采选业(B09)", "isParent": true, "parentcode": "B"}, {"id": "B091", "pId": "B09", "singlehyname": "常用有色金属矿采选", "name": "常用有色金属矿采选(B091)", "isParent": true, "parentcode": "B09"}, {"id": "B0911", "pId": "B091", "singlehyname": "铜矿采选", "name": "铜矿采选(B0911)", "isParent": false, "parentcode": "B091"}, {"id": "B0912", "pId": "B091", "singlehyname": "铅锌矿采选", "name": "铅锌矿采选(B0912)", "isParent": false, "parentcode": "B091"}, {"id": "B0913", "pId": "B091", "singlehyname": "镍钴矿采选", "name": "镍钴矿采选(B0913)", "isParent": false, "parentcode": "B091"}, {"id": "B0914", "pId": "B091", "singlehyname": "锡矿采选", "name": "锡矿采选(B0914)", "isParent": false, "parentcode": "B091"}, {"id": "B0915", "pId": "B091", "singlehyname": "锑矿采选", "name": "锑矿采选(B0915)", "isParent": false, "parentcode": "B091"}, {"id": "B0916", "pId": "B091", "singlehyname": "铝矿采选", "name": "铝矿采选(B0916)", "isParent": false, "parentcode": "B091"}, {"id": "B0917", "pId": "B091", "singlehyname": "镁矿采选", "name": "镁矿采选(B0917)", "isParent": false, "parentcode": "B091"}, {"id": "B0919", "pId": "B091", "singlehyname": "其他常用有色金属矿采选", "name": "其他常用有色金属矿采选(B0919)", "isParent": false, "parentcode": "B091"}, {"id": "B092", "pId": "B09", "singlehyname": "贵金属矿采选", "name": "贵金属矿采选(B092)", "isParent": true, "parentcode": "B09"}, {"id": "B0921", "pId": "B092", "singlehyname": "金矿采选", "name": "金矿采选(B0921)", "isParent": false, "parentcode": "B092"}, {"id": "B0922", "pId": "B092", "singlehyname": "银矿采选", "name": "银矿采选(B0922)", "isParent": false, "parentcode": "B092"}, {"id": "B0929", "pId": "B092", "singlehyname": "其他贵金属矿采选", "name": "其他贵金属矿采选(B0929)", "isParent": false, "parentcode": "B092"}, {"id": "B093", "pId": "B09", "singlehyname": "稀有稀土金属矿采选", "name": "稀有稀土金属矿采选(B093)", "isParent": true, "parentcode": "B09"}, {"id": "B0931", "pId": "B093", "singlehyname": "钨钼矿采选", "name": "钨钼矿采选(B0931)", "isParent": false, "parentcode": "B093"}, {"id": "B0932", "pId": "B093", "singlehyname": "稀土金属矿采选", "name": "稀土金属矿采选(B0932)", "isParent": false, "parentcode": "B093"}, {"id": "B0933", "pId": "B093", "singlehyname": "放射性金属矿采选", "name": "放射性金属矿采选(B0933)", "isParent": false, "parentcode": "B093"}, {"id": "B0939", "pId": "B093", "singlehyname": "其他稀有金属矿采选", "name": "其他稀有金属矿采选(B0939)", "isParent": false, "parentcode": "B093"}, {"id": "B10", "pId": "B", "singlehyname": "非金属矿采选业", "name": "非金属矿采选业(B10)", "isParent": true, "parentcode": "B"}, {"id": "B101", "pId": "B10", "singlehyname": "土砂石开采", "name": "土砂石开采(B101)", "isParent": true, "parentcode": "B10"}, {"id": "B1011", "pId": "B101", "singlehyname": "石灰石、石膏开采", "name": "石灰石、石膏开采(B1011)", "isParent": false, "parentcode": "B101"}, {"id": "B1012", "pId": "B101", "singlehyname": "建筑装饰用石开采", "name": "建筑装饰用石开采(B1012)", "isParent": false, "parentcode": "B101"}, {"id": "B1013", "pId": "B101", "singlehyname": "耐火土石开采", "name": "耐火土石开采(B1013)", "isParent": false, "parentcode": "B101"}, {"id": "B1019", "pId": "B101", "singlehyname": "粘土及其他土砂石开采", "name": "粘土及其他土砂石开采(B1019)", "isParent": false, "parentcode": "B101"}, {"id": "B102", "pId": "B10", "singlehyname": "化学矿开采", "name": "化学矿开采(B102)", "isParent": false, "parentcode": "B10"}, {"id": "B103", "pId": "B10", "singlehyname": "采盐", "name": "采盐(B103)", "isParent": false, "parentcode": "B10"}, {"id": "B109", "pId": "B10", "singlehyname": "石棉及其他非金属矿采选", "name": "石棉及其他非金属矿采选(B109)", "isParent": true, "parentcode": "B10"}, {"id": "B1091", "pId": "B109", "singlehyname": "石棉、云母矿采选", "name": "石棉、云母矿采选(B1091)", "isParent": false, "parentcode": "B109"}, {"id": "B1092", "pId": "B109", "singlehyname": "石墨、滑石采选", "name": "石墨、滑石采选(B1092)", "isParent": false, "parentcode": "B109"}, {"id": "B1093", "pId": "B109", "singlehyname": "宝石、玉石采选", "name": "宝石、玉石采选(B1093)", "isParent": false, "parentcode": "B109"}, {"id": "B1099", "pId": "B109", "singlehyname": "其他未列明非金属矿采选", "name": "其他未列明非金属矿采选(B1099)", "isParent": false, "parentcode": "B109"}, {"id": "B11", "pId": "B", "singlehyname": "开采专业及辅助性活动", "name": "开采专业及辅助性活动(B11)", "isParent": true, "parentcode": "B"}, {"id": "B111", "pId": "B11", "singlehyname": "煤炭开采和洗选专业及辅助性活动", "name": "煤炭开采和洗选专业及辅助性活动(B111)", "isParent": false, "parentcode": "B11"}, {"id": "B112", "pId": "B11", "singlehyname": "石油和天然气开采专业及辅助性活动", "name": "石油和天然气开采专业及辅助性活动(B112)", "isParent": false, "parentcode": "B11"}, {"id": "B119", "pId": "B11", "singlehyname": "其他开采专业及辅助性活动", "name": "其他开采专业及辅助性活动(B119)", "isParent": false, "parentcode": "B11"}, {"id": "B12", "pId": "B", "singlehyname": "其他采矿业", "name": "其他采矿业(B12)", "isParent": true, "parentcode": "B"}, {"id": "B1200", "pId": "B12", "singlehyname": "其他采矿业", "name": "其他采矿业(B1200)", "isParent": false, "parentcode": "B12"}, {"id": "C", "pId": "", "singlehyname": "制造业", "name": "制造业(C)", "isParent": true, "parentcode": ""}, {"id": "C13", "pId": "C", "singlehyname": "农副食品加工业", "name": "农副食品加工业(C13)", "isParent": true, "parentcode": "C"}, {"id": "C131", "pId": "C13", "singlehyname": "谷物磨制", "name": "谷物磨制(C131)", "isParent": true, "parentcode": "C13"}, {"id": "C1311", "pId": "C131", "singlehyname": "稻谷加工", "name": "稻谷加工(C1311)", "isParent": false, "parentcode": "C131"}, {"id": "C1312", "pId": "C131", "singlehyname": "小麦加工", "name": "小麦加工(C1312)", "isParent": false, "parentcode": "C131"}, {"id": "C1313", "pId": "C131", "singlehyname": "玉米加工", "name": "玉米加工(C1313)", "isParent": false, "parentcode": "C131"}, {"id": "C1314", "pId": "C131", "singlehyname": "杂粮加工", "name": "杂粮加工(C1314)", "isParent": false, "parentcode": "C131"}, {"id": "C1319", "pId": "C131", "singlehyname": "其他谷物磨制", "name": "其他谷物磨制(C1319)", "isParent": false, "parentcode": "C131"}, {"id": "C132", "pId": "C13", "singlehyname": "饲料加工", "name": "饲料加工(C132)", "isParent": true, "parentcode": "C13"}, {"id": "C1321", "pId": "C132", "singlehyname": "宠物饲料加工", "name": "宠物饲料加工(C1321)", "isParent": false, "parentcode": "C132"}, {"id": "C1329", "pId": "C132", "singlehyname": "其他饲料加工", "name": "其他饲料加工(C1329)", "isParent": false, "parentcode": "C132"}, {"id": "C133", "pId": "C13", "singlehyname": "植物油加工", "name": "植物油加工(C133)", "isParent": true, "parentcode": "C13"}, {"id": "C1331", "pId": "C133", "singlehyname": "食用植物油加工", "name": "食用植物油加工(C1331)", "isParent": false, "parentcode": "C133"}, {"id": "C1332", "pId": "C133", "singlehyname": "非食用植物油加工", "name": "非食用植物油加工(C1332)", "isParent": false, "parentcode": "C133"}, {"id": "C134", "pId": "C13", "singlehyname": "制糖业", "name": "制糖业(C134)", "isParent": false, "parentcode": "C13"}, {"id": "C135", "pId": "C13", "singlehyname": "屠宰及肉类加工", "name": "屠宰及肉类加工(C135)", "isParent": true, "parentcode": "C13"}, {"id": "C1351", "pId": "C135", "singlehyname": "牲畜屠宰", "name": "牲畜屠宰(C1351)", "isParent": false, "parentcode": "C135"}, {"id": "C1352", "pId": "C135", "singlehyname": "禽类屠宰", "name": "禽类屠宰(C1352)", "isParent": false, "parentcode": "C135"}, {"id": "C1353", "pId": "C135", "singlehyname": "肉制品及副产品加工", "name": "肉制品及副产品加工(C1353)", "isParent": false, "parentcode": "C135"}, {"id": "C136", "pId": "C13", "singlehyname": "水产品加工", "name": "水产品加工(C136)", "isParent": true, "parentcode": "C13"}, {"id": "C1361", "pId": "C136", "singlehyname": "水产品冷冻加工", "name": "水产品冷冻加工(C1361)", "isParent": false, "parentcode": "C136"}, {"id": "C1362", "pId": "C136", "singlehyname": "鱼糜制品及水产品干腌制加工", "name": "鱼糜制品及水产品干腌制加工(C1362)", "isParent": false, "parentcode": "C136"}, {"id": "C1363", "pId": "C136", "singlehyname": "鱼油提取及制品制造", "name": "鱼油提取及制品制造(C1363)", "isParent": false, "parentcode": "C136"}, {"id": "C1369", "pId": "C136", "singlehyname": "其他水产品加工", "name": "其他水产品加工(C1369)", "isParent": false, "parentcode": "C136"}, {"id": "C137", "pId": "C13", "singlehyname": "蔬菜、菌类、水果和坚果加工", "name": "蔬菜、菌类、水果和坚果加工(C137)", "isParent": true, "parentcode": "C13"}, {"id": "C1371", "pId": "C137", "singlehyname": "蔬菜加工", "name": "蔬菜加工(C1371)", "isParent": false, "parentcode": "C137"}, {"id": "C1372", "pId": "C137", "singlehyname": "食用菌加工", "name": "食用菌加工(C1372)", "isParent": false, "parentcode": "C137"}, {"id": "C1373", "pId": "C137", "singlehyname": "水果和坚果加工", "name": "水果和坚果加工(C1373)", "isParent": false, "parentcode": "C137"}, {"id": "C139", "pId": "C13", "singlehyname": "其他农副食品加工", "name": "其他农副食品加工(C139)", "isParent": true, "parentcode": "C13"}, {"id": "C1391", "pId": "C139", "singlehyname": "淀粉及淀粉制品制造", "name": "淀粉及淀粉制品制造(C1391)", "isParent": false, "parentcode": "C139"}, {"id": "C1392", "pId": "C139", "singlehyname": "豆制品制造", "name": "豆制品制造(C1392)", "isParent": false, "parentcode": "C139"}, {"id": "C1393", "pId": "C139", "singlehyname": "蛋品加工", "name": "蛋品加工(C1393)", "isParent": false, "parentcode": "C139"}, {"id": "C1399", "pId": "C139", "singlehyname": "其他未列明农副食品加工", "name": "其他未列明农副食品加工(C1399)", "isParent": false, "parentcode": "C139"}, {"id": "C14", "pId": "C", "singlehyname": "食品制造业", "name": "食品制造业(C14)", "isParent": true, "parentcode": "C"}, {"id": "C141", "pId": "C14", "singlehyname": "焙烤食品制造", "name": "焙烤食品制造(C141)", "isParent": true, "parentcode": "C14"}, {"id": "C1411", "pId": "C141", "singlehyname": "糕点、面包制造", "name": "糕点、面包制造(C1411)", "isParent": false, "parentcode": "C141"}, {"id": "C1419", "pId": "C141", "singlehyname": "饼干及其他焙烤食品制造", "name": "饼干及其他焙烤食品制造(C1419)", "isParent": false, "parentcode": "C141"}, {"id": "C142", "pId": "C14", "singlehyname": "糖果、巧克力及蜜饯制造", "name": "糖果、巧克力及蜜饯制造(C142)", "isParent": true, "parentcode": "C14"}, {"id": "C1421", "pId": "C142", "singlehyname": "糖果、巧克力制造", "name": "糖果、巧克力制造(C1421)", "isParent": false, "parentcode": "C142"}, {"id": "C1422", "pId": "C142", "singlehyname": "蜜饯制作", "name": "蜜饯制作(C1422)", "isParent": false, "parentcode": "C142"}, {"id": "C143", "pId": "C14", "singlehyname": "方便食品制造", "name": "方便食品制造(C143)", "isParent": true, "parentcode": "C14"}, {"id": "C1431", "pId": "C143", "singlehyname": "米、面制品制造", "name": "米、面制品制造(C1431)", "isParent": false, "parentcode": "C143"}, {"id": "C1432", "pId": "C143", "singlehyname": "速冻食品制造", "name": "速冻食品制造(C1432)", "isParent": false, "parentcode": "C143"}, {"id": "C1433", "pId": "C143", "singlehyname": "方便面制造", "name": "方便面制造(C1433)", "isParent": false, "parentcode": "C143"}, {"id": "C1439", "pId": "C143", "singlehyname": "其他方便食品制造", "name": "其他方便食品制造(C1439)", "isParent": false, "parentcode": "C143"}, {"id": "C144", "pId": "C14", "singlehyname": "乳制品制造", "name": "乳制品制造(C144)", "isParent": true, "parentcode": "C14"}, {"id": "C1441", "pId": "C144", "singlehyname": "液体乳制造", "name": "液体乳制造(C1441)", "isParent": false, "parentcode": "C144"}, {"id": "C1442", "pId": "C144", "singlehyname": "乳粉制造", "name": "乳粉制造(C1442)", "isParent": false, "parentcode": "C144"}, {"id": "C1449", "pId": "C144", "singlehyname": "其他乳制品制造", "name": "其他乳制品制造(C1449)", "isParent": false, "parentcode": "C144"}, {"id": "C145", "pId": "C14", "singlehyname": "罐头食品制造", "name": "罐头食品制造(C145)", "isParent": true, "parentcode": "C14"}, {"id": "C1451", "pId": "C145", "singlehyname": "肉、禽类罐头制造", "name": "肉、禽类罐头制造(C1451)", "isParent": false, "parentcode": "C145"}, {"id": "C1452", "pId": "C145", "singlehyname": "水产品罐头制造", "name": "水产品罐头制造(C1452)", "isParent": false, "parentcode": "C145"}, {"id": "C1453", "pId": "C145", "singlehyname": "蔬菜、水果罐头制造", "name": "蔬菜、水果罐头制造(C1453)", "isParent": false, "parentcode": "C145"}, {"id": "C1459", "pId": "C145", "singlehyname": "其他罐头食品制造", "name": "其他罐头食品制造(C1459)", "isParent": false, "parentcode": "C145"}, {"id": "C146", "pId": "C14", "singlehyname": "调味品、发酵制品制造", "name": "调味品、发酵制品制造(C146)", "isParent": true, "parentcode": "C14"}, {"id": "C1461", "pId": "C146", "singlehyname": "味精制造", "name": "味精制造(C1461)", "isParent": false, "parentcode": "C146"}, {"id": "C1462", "pId": "C146", "singlehyname": "酱油、食醋及类似制品制造", "name": "酱油、食醋及类似制品制造(C1462)", "isParent": false, "parentcode": "C146"}, {"id": "C1469", "pId": "C146", "singlehyname": "其他调味品、发酵制品制造", "name": "其他调味品、发酵制品制造(C1469)", "isParent": false, "parentcode": "C146"}, {"id": "C149", "pId": "C14", "singlehyname": "其他食品制造", "name": "其他食品制造(C149)", "isParent": true, "parentcode": "C14"}, {"id": "C1491", "pId": "C149", "singlehyname": "营养食品制造", "name": "营养食品制造(C1491)", "isParent": false, "parentcode": "C149"}, {"id": "C1492", "pId": "C149", "singlehyname": "保健食品制造", "name": "保健食品制造(C1492)", "isParent": false, "parentcode": "C149"}, {"id": "C1493", "pId": "C149", "singlehyname": "冷冻饮品及食用冰制造", "name": "冷冻饮品及食用冰制造(C1493)", "isParent": false, "parentcode": "C149"}, {"id": "C1494", "pId": "C149", "singlehyname": "盐加工", "name": "盐加工(C1494)", "isParent": false, "parentcode": "C149"}, {"id": "C1495", "pId": "C149", "singlehyname": "食品及饲料添加剂制造", "name": "食品及饲料添加剂制造(C1495)", "isParent": false, "parentcode": "C149"}, {"id": "C1499", "pId": "C149", "singlehyname": "其他未列明食品制造", "name": "其他未列明食品制造(C1499)", "isParent": false, "parentcode": "C149"}, {"id": "C15", "pId": "C", "singlehyname": "酒、饮料和精制茶制造业", "name": "酒、饮料和精制茶制造业(C15)", "isParent": true, "parentcode": "C"}, {"id": "C151", "pId": "C15", "singlehyname": "酒的制造", "name": "酒的制造(C151)", "isParent": true, "parentcode": "C15"}, {"id": "C1511", "pId": "C151", "singlehyname": "酒精制造", "name": "酒精制造(C1511)", "isParent": false, "parentcode": "C151"}, {"id": "C1512", "pId": "C151", "singlehyname": "白酒制造", "name": "白酒制造(C1512)", "isParent": false, "parentcode": "C151"}, {"id": "C1513", "pId": "C151", "singlehyname": "啤酒制造", "name": "啤酒制造(C1513)", "isParent": false, "parentcode": "C151"}, {"id": "C1514", "pId": "C151", "singlehyname": "黄酒制造", "name": "黄酒制造(C1514)", "isParent": false, "parentcode": "C151"}, {"id": "C1515", "pId": "C151", "singlehyname": "葡萄酒制造", "name": "葡萄酒制造(C1515)", "isParent": false, "parentcode": "C151"}, {"id": "C1519", "pId": "C151", "singlehyname": "其他酒制造", "name": "其他酒制造(C1519)", "isParent": false, "parentcode": "C151"}, {"id": "C152", "pId": "C15", "singlehyname": "饮料制造", "name": "饮料制造(C152)", "isParent": true, "parentcode": "C15"}, {"id": "C1521", "pId": "C152", "singlehyname": "碳酸饮料制造", "name": "碳酸饮料制造(C1521)", "isParent": false, "parentcode": "C152"}, {"id": "C1522", "pId": "C152", "singlehyname": "瓶(罐)装饮用水制造", "name": "瓶(罐)装饮用水制造(C1522)", "isParent": false, "parentcode": "C152"}, {"id": "C1523", "pId": "C152", "singlehyname": "果菜汁及果菜汁饮料制造", "name": "果菜汁及果菜汁饮料制造(C1523)", "isParent": false, "parentcode": "C152"}, {"id": "C1524", "pId": "C152", "singlehyname": "含乳饮料和植物蛋白饮料制造", "name": "含乳饮料和植物蛋白饮料制造(C1524)", "isParent": false, "parentcode": "C152"}, {"id": "C1525", "pId": "C152", "singlehyname": "固体饮料制造", "name": "固体饮料制造(C1525)", "isParent": false, "parentcode": "C152"}, {"id": "C1529", "pId": "C152", "singlehyname": "茶饮料及其他饮料制造", "name": "茶饮料及其他饮料制造(C1529)", "isParent": false, "parentcode": "C152"}, {"id": "C153", "pId": "C15", "singlehyname": "精制茶加工", "name": "精制茶加工(C153)", "isParent": false, "parentcode": "C15"}, {"id": "C16", "pId": "C", "singlehyname": "烟草制品业", "name": "烟草制品业(C16)", "isParent": true, "parentcode": "C"}, {"id": "C161", "pId": "C16", "singlehyname": "烟叶复烤", "name": "烟叶复烤(C161)", "isParent": false, "parentcode": "C16"}, {"id": "C162", "pId": "C16", "singlehyname": "卷烟制造", "name": "卷烟制造(C162)", "isParent": false, "parentcode": "C16"}, {"id": "C169", "pId": "C16", "singlehyname": "其他烟草制品制造", "name": "其他烟草制品制造(C169)", "isParent": false, "parentcode": "C16"}, {"id": "C17", "pId": "C", "singlehyname": "纺织业", "name": "纺织业(C17)", "isParent": true, "parentcode": "C"}, {"id": "C171", "pId": "C17", "singlehyname": "棉纺织及印染精加工", "name": "棉纺织及印染精加工(C171)", "isParent": true, "parentcode": "C17"}, {"id": "C1711", "pId": "C171", "singlehyname": "棉纺纱加工", "name": "棉纺纱加工(C1711)", "isParent": false, "parentcode": "C171"}, {"id": "C1712", "pId": "C171", "singlehyname": "棉织造加工", "name": "棉织造加工(C1712)", "isParent": false, "parentcode": "C171"}, {"id": "C1713", "pId": "C171", "singlehyname": "棉印染精加工", "name": "棉印染精加工(C1713)", "isParent": false, "parentcode": "C171"}, {"id": "C172", "pId": "C17", "singlehyname": "毛纺织及染整精加工", "name": "毛纺织及染整精加工(C172)", "isParent": true, "parentcode": "C17"}, {"id": "C1721", "pId": "C172", "singlehyname": "毛条和毛纱线加工", "name": "毛条和毛纱线加工(C1721)", "isParent": false, "parentcode": "C172"}, {"id": "C1722", "pId": "C172", "singlehyname": "毛织造加工", "name": "毛织造加工(C1722)", "isParent": false, "parentcode": "C172"}, {"id": "C1723", "pId": "C172", "singlehyname": "毛染整精加工", "name": "毛染整精加工(C1723)", "isParent": false, "parentcode": "C172"}, {"id": "C173", "pId": "C17", "singlehyname": "麻纺织及染整精加工", "name": "麻纺织及染整精加工(C173)", "isParent": true, "parentcode": "C17"}, {"id": "C1731", "pId": "C173", "singlehyname": "麻纤维纺前加工和纺纱", "name": "麻纤维纺前加工和纺纱(C1731)", "isParent": false, "parentcode": "C173"}, {"id": "C1732", "pId": "C173", "singlehyname": "麻织造加工", "name": "麻织造加工(C1732)", "isParent": false, "parentcode": "C173"}, {"id": "C1733", "pId": "C173", "singlehyname": "麻染整精加工", "name": "麻染整精加工(C1733)", "isParent": false, "parentcode": "C173"}, {"id": "C174", "pId": "C17", "singlehyname": "丝绢纺织及印染精加工", "name": "丝绢纺织及印染精加工(C174)", "isParent": true, "parentcode": "C17"}, {"id": "C1741", "pId": "C174", "singlehyname": "缫丝加工", "name": "缫丝加工(C1741)", "isParent": false, "parentcode": "C174"}, {"id": "C1742", "pId": "C174", "singlehyname": "绢纺和丝织加工", "name": "绢纺和丝织加工(C1742)", "isParent": false, "parentcode": "C174"}, {"id": "C1743", "pId": "C174", "singlehyname": "丝印染精加工", "name": "丝印染精加工(C1743)", "isParent": false, "parentcode": "C174"}, {"id": "C175", "pId": "C17", "singlehyname": "化纤织造及印染精加工", "name": "化纤织造及印染精加工(C175)", "isParent": true, "parentcode": "C17"}, {"id": "C1751", "pId": "C175", "singlehyname": "化纤织造加工", "name": "化纤织造加工(C1751)", "isParent": false, "parentcode": "C175"}, {"id": "C1752", "pId": "C175", "singlehyname": "化纤织物染整精加工", "name": "化纤织物染整精加工(C1752)", "isParent": false, "parentcode": "C175"}, {"id": "C176", "pId": "C17", "singlehyname": "针织或钩针编织物及其制品制造", "name": "针织或钩针编织物及其制品制造(C176)", "isParent": true, "parentcode": "C17"}, {"id": "C1761", "pId": "C176", "singlehyname": "针织或钩针编织物织造", "name": "针织或钩针编织物织造(C1761)", "isParent": false, "parentcode": "C176"}, {"id": "C1762", "pId": "C176", "singlehyname": "针织或钩针编织物印染精加工", "name": "针织或钩针编织物印染精加工(C1762)", "isParent": false, "parentcode": "C176"}, {"id": "C1763", "pId": "C176", "singlehyname": "针织或钩针编织品制造", "name": "针织或钩针编织品制造(C1763)", "isParent": false, "parentcode": "C176"}, {"id": "C177", "pId": "C17", "singlehyname": "家用纺织制成品制造", "name": "家用纺织制成品制造(C177)", "isParent": true, "parentcode": "C17"}, {"id": "C1771", "pId": "C177", "singlehyname": "床上用品制造", "name": "床上用品制造(C1771)", "isParent": false, "parentcode": "C177"}, {"id": "C1772", "pId": "C177", "singlehyname": "毛巾类制品制造", "name": "毛巾类制品制造(C1772)", "isParent": false, "parentcode": "C177"}, {"id": "C1773", "pId": "C177", "singlehyname": "窗帘、布艺类产品制造", "name": "窗帘、布艺类产品制造(C1773)", "isParent": false, "parentcode": "C177"}, {"id": "C1779", "pId": "C177", "singlehyname": "其他家用纺织制成品制造", "name": "其他家用纺织制成品制造(C1779)", "isParent": false, "parentcode": "C177"}, {"id": "C178", "pId": "C17", "singlehyname": "产业用纺织制成品制造", "name": "产业用纺织制成品制造(C178)", "isParent": true, "parentcode": "C17"}, {"id": "C1781", "pId": "C178", "singlehyname": "非织造布制造", "name": "非织造布制造(C1781)", "isParent": false, "parentcode": "C178"}, {"id": "C1782", "pId": "C178", "singlehyname": "绳、索、缆制造", "name": "绳、索、缆制造(C1782)", "isParent": false, "parentcode": "C178"}, {"id": "C1783", "pId": "C178", "singlehyname": "纺织带和帘子布制造", "name": "纺织带和帘子布制造(C1783)", "isParent": false, "parentcode": "C178"}, {"id": "C1784", "pId": "C178", "singlehyname": "篷、帆布制造", "name": "篷、帆布制造(C1784)", "isParent": false, "parentcode": "C178"}, {"id": "C1789", "pId": "C178", "singlehyname": "其他产业用纺织制成品制造", "name": "其他产业用纺织制成品制造(C1789)", "isParent": false, "parentcode": "C178"}, {"id": "C18", "pId": "C", "singlehyname": "纺织服装、服饰业", "name": "纺织服装、服饰业(C18)", "isParent": true, "parentcode": "C"}, {"id": "C181", "pId": "C18", "singlehyname": "机织服装制造", "name": "机织服装制造(C181)", "isParent": true, "parentcode": "C18"}, {"id": "C1811", "pId": "C181", "singlehyname": "运动机织服装制造", "name": "运动机织服装制造(C1811)", "isParent": false, "parentcode": "C181"}, {"id": "C1819", "pId": "C181", "singlehyname": "其他机织服装制造", "name": "其他机织服装制造(C1819)", "isParent": false, "parentcode": "C181"}, {"id": "C182", "pId": "C18", "singlehyname": "针织或钩针编织服装制造", "name": "针织或钩针编织服装制造(C182)", "isParent": true, "parentcode": "C18"}, {"id": "C1821", "pId": "C182", "singlehyname": "运动休闲针织服装制造", "name": "运动休闲针织服装制造(C1821)", "isParent": false, "parentcode": "C182"}, {"id": "C1829", "pId": "C182", "singlehyname": "其他针织或钩针编织服装制造", "name": "其他针织或钩针编织服装制造(C1829)", "isParent": false, "parentcode": "C182"}, {"id": "C183", "pId": "C18", "singlehyname": "服饰制造", "name": "服饰制造(C183)", "isParent": false, "parentcode": "C18"}, {"id": "C19", "pId": "C", "singlehyname": "皮革、毛皮、羽毛及其制品和制鞋业", "name": "皮革、毛皮、羽毛及其制品和制鞋业(C19)", "isParent": true, "parentcode": "C"}, {"id": "C191", "pId": "C19", "singlehyname": "皮革鞣制加工", "name": "皮革鞣制加工(C191)", "isParent": false, "parentcode": "C19"}, {"id": "C192", "pId": "C19", "singlehyname": "皮革制品制造", "name": "皮革制品制造(C192)", "isParent": true, "parentcode": "C19"}, {"id": "C1921", "pId": "C192", "singlehyname": "皮革服装制造", "name": "皮革服装制造(C1921)", "isParent": false, "parentcode": "C192"}, {"id": "C1922", "pId": "C192", "singlehyname": "皮箱、包(袋)制造", "name": "皮箱、包(袋)制造(C1922)", "isParent": false, "parentcode": "C192"}, {"id": "C1923", "pId": "C192", "singlehyname": "皮手套及皮装饰制品制造", "name": "皮手套及皮装饰制品制造(C1923)", "isParent": false, "parentcode": "C192"}, {"id": "C1929", "pId": "C192", "singlehyname": "其他皮革制品制造", "name": "其他皮革制品制造(C1929)", "isParent": false, "parentcode": "C192"}, {"id": "C193", "pId": "C19", "singlehyname": "毛皮鞣制及制品加工", "name": "毛皮鞣制及制品加工(C193)", "isParent": true, "parentcode": "C19"}, {"id": "C1931", "pId": "C193", "singlehyname": "毛皮鞣制加工", "name": "毛皮鞣制加工(C1931)", "isParent": false, "parentcode": "C193"}, {"id": "C1932", "pId": "C193", "singlehyname": "毛皮服装加工", "name": "毛皮服装加工(C1932)", "isParent": false, "parentcode": "C193"}, {"id": "C1939", "pId": "C193", "singlehyname": "其他毛皮制品加工", "name": "其他毛皮制品加工(C1939)", "isParent": false, "parentcode": "C193"}, {"id": "C194", "pId": "C19", "singlehyname": "羽毛(绒)加工及制品制造", "name": "羽毛(绒)加工及制品制造(C194)", "isParent": true, "parentcode": "C19"}, {"id": "C1941", "pId": "C194", "singlehyname": "羽毛(绒)加工", "name": "羽毛(绒)加工(C1941)", "isParent": false, "parentcode": "C194"}, {"id": "C1942", "pId": "C194", "singlehyname": "羽毛(绒)制品加工", "name": "羽毛(绒)制品加工(C1942)", "isParent": false, "parentcode": "C194"}, {"id": "C195", "pId": "C19", "singlehyname": "制鞋业", "name": "制鞋业(C195)", "isParent": true, "parentcode": "C19"}, {"id": "C1951", "pId": "C195", "singlehyname": "纺织面料鞋制造", "name": "纺织面料鞋制造(C1951)", "isParent": false, "parentcode": "C195"}, {"id": "C1952", "pId": "C195", "singlehyname": "皮鞋制造", "name": "皮鞋制造(C1952)", "isParent": false, "parentcode": "C195"}, {"id": "C1953", "pId": "C195", "singlehyname": "塑料鞋制造", "name": "塑料鞋制造(C1953)", "isParent": false, "parentcode": "C195"}, {"id": "C1954", "pId": "C195", "singlehyname": "橡胶鞋制造", "name": "橡胶鞋制造(C1954)", "isParent": false, "parentcode": "C195"}, {"id": "C1959", "pId": "C195", "singlehyname": "其他制鞋业", "name": "其他制鞋业(C1959)", "isParent": false, "parentcode": "C195"}, {"id": "C20", "pId": "C", "singlehyname": "木材加工和木、竹、藤、棕、草制品业", "name": "木材加工和木、竹、藤、棕、草制品业(C20)", "isParent": true, "parentcode": "C"}, {"id": "C201", "pId": "C20", "singlehyname": "木材加工", "name": "木材加工(C201)", "isParent": true, "parentcode": "C20"}, {"id": "C2011", "pId": "C201", "singlehyname": "锯材加工", "name": "锯材加工(C2011)", "isParent": false, "parentcode": "C201"}, {"id": "C2012", "pId": "C201", "singlehyname": "木片加工", "name": "木片加工(C2012)", "isParent": false, "parentcode": "C201"}, {"id": "C2013", "pId": "C201", "singlehyname": "单板加工", "name": "单板加工(C2013)", "isParent": false, "parentcode": "C201"}, {"id": "C2019", "pId": "C201", "singlehyname": "其他木材加工", "name": "其他木材加工(C2019)", "isParent": false, "parentcode": "C201"}, {"id": "C202", "pId": "C20", "singlehyname": "人造板制造", "name": "人造板制造(C202)", "isParent": true, "parentcode": "C20"}, {"id": "C2021", "pId": "C202", "singlehyname": "胶合板制造", "name": "胶合板制造(C2021)", "isParent": false, "parentcode": "C202"}, {"id": "C2022", "pId": "C202", "singlehyname": "纤维板制造", "name": "纤维板制造(C2022)", "isParent": false, "parentcode": "C202"}, {"id": "C2023", "pId": "C202", "singlehyname": "刨花板制造", "name": "刨花板制造(C2023)", "isParent": false, "parentcode": "C202"}, {"id": "C2029", "pId": "C202", "singlehyname": "其他人造板制造", "name": "其他人造板制造(C2029)", "isParent": false, "parentcode": "C202"}, {"id": "C203", "pId": "C20", "singlehyname": "木质制品制造", "name": "木质制品制造(C203)", "isParent": true, "parentcode": "C20"}, {"id": "C2031", "pId": "C203", "singlehyname": "建筑用木料及木材组件加工", "name": "建筑用木料及木材组件加工(C2031)", "isParent": false, "parentcode": "C203"}, {"id": "C2032", "pId": "C203", "singlehyname": "木门窗制造", "name": "木门窗制造(C2032)", "isParent": false, "parentcode": "C203"}, {"id": "C2033", "pId": "C203", "singlehyname": "木楼梯制造", "name": "木楼梯制造(C2033)", "isParent": false, "parentcode": "C203"}, {"id": "C2034", "pId": "C203", "singlehyname": "木地板制造", "name": "木地板制造(C2034)", "isParent": false, "parentcode": "C203"}, {"id": "C2035", "pId": "C203", "singlehyname": "木制容器制造", "name": "木制容器制造(C2035)", "isParent": false, "parentcode": "C203"}, {"id": "C2039", "pId": "C203", "singlehyname": "软木制品及其他木制品制造", "name": "软木制品及其他木制品制造(C2039)", "isParent": false, "parentcode": "C203"}, {"id": "C204", "pId": "C20", "singlehyname": "竹、藤、棕、草等制品制造", "name": "竹、藤、棕、草等制品制造(C204)", "isParent": true, "parentcode": "C20"}, {"id": "C2041", "pId": "C204", "singlehyname": "竹制品制造", "name": "竹制品制造(C2041)", "isParent": false, "parentcode": "C204"}, {"id": "C2042", "pId": "C204", "singlehyname": "藤制品制造", "name": "藤制品制造(C2042)", "isParent": false, "parentcode": "C204"}, {"id": "C2043", "pId": "C204", "singlehyname": "棕制品制造", "name": "棕制品制造(C2043)", "isParent": false, "parentcode": "C204"}, {"id": "C2049", "pId": "C204", "singlehyname": "草及其他制品制造", "name": "草及其他制品制造(C2049)", "isParent": false, "parentcode": "C204"}, {"id": "C21", "pId": "C", "singlehyname": "家具制造业", "name": "家具制造业(C21)", "isParent": true, "parentcode": "C"}, {"id": "C211", "pId": "C21", "singlehyname": "木质家具制造", "name": "木质家具制造(C211)", "isParent": false, "parentcode": "C21"}, {"id": "C212", "pId": "C21", "singlehyname": "竹、藤家具制造", "name": "竹、藤家具制造(C212)", "isParent": false, "parentcode": "C21"}, {"id": "C213", "pId": "C21", "singlehyname": "金属家具制造", "name": "金属家具制造(C213)", "isParent": false, "parentcode": "C21"}, {"id": "C214", "pId": "C21", "singlehyname": "塑料家具制造", "name": "塑料家具制造(C214)", "isParent": false, "parentcode": "C21"}, {"id": "C219", "pId": "C21", "singlehyname": "其他家具制造", "name": "其他家具制造(C219)", "isParent": false, "parentcode": "C21"}, {"id": "C22", "pId": "C", "singlehyname": "造纸和纸制品业", "name": "造纸和纸制品业(C22)", "isParent": true, "parentcode": "C"}, {"id": "C221", "pId": "C22", "singlehyname": "纸浆制造", "name": "纸浆制造(C221)", "isParent": true, "parentcode": "C22"}, {"id": "C2211", "pId": "C221", "singlehyname": "木竹浆制造", "name": "木竹浆制造(C2211)", "isParent": false, "parentcode": "C221"}, {"id": "C2212", "pId": "C221", "singlehyname": "非木竹浆制造", "name": "非木竹浆制造(C2212)", "isParent": false, "parentcode": "C221"}, {"id": "C222", "pId": "C22", "singlehyname": "造纸", "name": "造纸(C222)", "isParent": true, "parentcode": "C22"}, {"id": "C2221", "pId": "C222", "singlehyname": "机制纸及纸板制造", "name": "机制纸及纸板制造(C2221)", "isParent": false, "parentcode": "C222"}, {"id": "C2222", "pId": "C222", "singlehyname": "手工纸制造", "name": "手工纸制造(C2222)", "isParent": false, "parentcode": "C222"}, {"id": "C2223", "pId": "C222", "singlehyname": "加工纸制造", "name": "加工纸制造(C2223)", "isParent": false, "parentcode": "C222"}, {"id": "C223", "pId": "C22", "singlehyname": "纸制品制造", "name": "纸制品制造(C223)", "isParent": true, "parentcode": "C22"}, {"id": "C2231", "pId": "C223", "singlehyname": "纸和纸板容器制造", "name": "纸和纸板容器制造(C2231)", "isParent": false, "parentcode": "C223"}, {"id": "C2239", "pId": "C223", "singlehyname": "其他纸制品制造", "name": "其他纸制品制造(C2239)", "isParent": false, "parentcode": "C223"}, {"id": "C23", "pId": "C", "singlehyname": "印刷和记录媒介复制业", "name": "印刷和记录媒介复制业(C23)", "isParent": true, "parentcode": "C"}, {"id": "C231", "pId": "C23", "singlehyname": "印刷", "name": "印刷(C231)", "isParent": true, "parentcode": "C23"}, {"id": "C2311", "pId": "C231", "singlehyname": "书、报刊印刷", "name": "书、报刊印刷(C2311)", "isParent": false, "parentcode": "C231"}, {"id": "C2312", "pId": "C231", "singlehyname": "本册印制", "name": "本册印制(C2312)", "isParent": false, "parentcode": "C231"}, {"id": "C2319", "pId": "C231", "singlehyname": "包装装潢及其他印刷", "name": "包装装潢及其他印刷(C2319)", "isParent": false, "parentcode": "C231"}, {"id": "C232", "pId": "C23", "singlehyname": "装订及印刷相关服务", "name": "装订及印刷相关服务(C232)", "isParent": false, "parentcode": "C23"}, {"id": "C233", "pId": "C23", "singlehyname": "记录媒介复制", "name": "记录媒介复制(C233)", "isParent": false, "parentcode": "C23"}, {"id": "C24", "pId": "C", "singlehyname": "文教、工美、体育和娱乐用品制造业", "name": "文教、工美、体育和娱乐用品制造业(C24)", "isParent": true, "parentcode": "C"}, {"id": "C241", "pId": "C24", "singlehyname": "文教办公用品制造", "name": "文教办公用品制造(C241)", "isParent": true, "parentcode": "C24"}, {"id": "C2411", "pId": "C241", "singlehyname": "文具制造", "name": "文具制造(C2411)", "isParent": false, "parentcode": "C241"}, {"id": "C2412", "pId": "C241", "singlehyname": "笔的制造", "name": "笔的制造(C2412)", "isParent": false, "parentcode": "C241"}, {"id": "C2413", "pId": "C241", "singlehyname": "教学用模型及教具制造", "name": "教学用模型及教具制造(C2413)", "isParent": false, "parentcode": "C241"}, {"id": "C2414", "pId": "C241", "singlehyname": "墨水、墨汁制造", "name": "墨水、墨汁制造(C2414)", "isParent": false, "parentcode": "C241"}, {"id": "C2419", "pId": "C241", "singlehyname": "其他文教办公用品制造", "name": "其他文教办公用品制造(C2419)", "isParent": false, "parentcode": "C241"}, {"id": "C242", "pId": "C24", "singlehyname": "乐器制造", "name": "乐器制造(C242)", "isParent": true, "parentcode": "C24"}, {"id": "C2421", "pId": "C242", "singlehyname": "中乐器制造", "name": "中乐器制造(C2421)", "isParent": false, "parentcode": "C242"}, {"id": "C2422", "pId": "C242", "singlehyname": "西乐器制造", "name": "西乐器制造(C2422)", "isParent": false, "parentcode": "C242"}, {"id": "C2423", "pId": "C242", "singlehyname": "电子乐器制造", "name": "电子乐器制造(C2423)", "isParent": false, "parentcode": "C242"}, {"id": "C2429", "pId": "C242", "singlehyname": "其他乐器及零件制造", "name": "其他乐器及零件制造(C2429)", "isParent": false, "parentcode": "C242"}, {"id": "C243", "pId": "C24", "singlehyname": "工艺美术及礼仪用品制造", "name": "工艺美术及礼仪用品制造(C243)", "isParent": true, "parentcode": "C24"}, {"id": "C2431", "pId": "C243", "singlehyname": "雕塑工艺品制造", "name": "雕塑工艺品制造(C2431)", "isParent": false, "parentcode": "C243"}, {"id": "C2432", "pId": "C243", "singlehyname": "金属工艺品制造", "name": "金属工艺品制造(C2432)", "isParent": false, "parentcode": "C243"}, {"id": "C2433", "pId": "C243", "singlehyname": "漆器工艺品制造", "name": "漆器工艺品制造(C2433)", "isParent": false, "parentcode": "C243"}, {"id": "C2434", "pId": "C243", "singlehyname": "花画工艺品制造", "name": "花画工艺品制造(C2434)", "isParent": false, "parentcode": "C243"}, {"id": "C2435", "pId": "C243", "singlehyname": "天然植物纤维编织工艺品制造", "name": "天然植物纤维编织工艺品制造(C2435)", "isParent": false, "parentcode": "C243"}, {"id": "C2436", "pId": "C243", "singlehyname": "抽纱刺绣工艺品制造", "name": "抽纱刺绣工艺品制造(C2436)", "isParent": false, "parentcode": "C243"}, {"id": "C2437", "pId": "C243", "singlehyname": "地毯、挂毯制造", "name": "地毯、挂毯制造(C2437)", "isParent": false, "parentcode": "C243"}, {"id": "C2438", "pId": "C243", "singlehyname": "珠宝首饰及有关物品制造", "name": "珠宝首饰及有关物品制造(C2438)", "isParent": false, "parentcode": "C243"}, {"id": "C2439", "pId": "C243", "singlehyname": "其他工艺美术及礼仪用品制造", "name": "其他工艺美术及礼仪用品制造(C2439)", "isParent": false, "parentcode": "C243"}, {"id": "C244", "pId": "C24", "singlehyname": "体育用品制造", "name": "体育用品制造(C244)", "isParent": true, "parentcode": "C24"}, {"id": "C2441", "pId": "C244", "singlehyname": "球类制造", "name": "球类制造(C2441)", "isParent": false, "parentcode": "C244"}, {"id": "C2442", "pId": "C244", "singlehyname": "专项运动器材及配件制造", "name": "专项运动器材及配件制造(C2442)", "isParent": false, "parentcode": "C244"}, {"id": "C2443", "pId": "C244", "singlehyname": "健身器材制造", "name": "健身器材制造(C2443)", "isParent": false, "parentcode": "C244"}, {"id": "C2444", "pId": "C244", "singlehyname": "运动防护用具制造", "name": "运动防护用具制造(C2444)", "isParent": false, "parentcode": "C244"}, {"id": "C2449", "pId": "C244", "singlehyname": "其他体育用品制造", "name": "其他体育用品制造(C2449)", "isParent": false, "parentcode": "C244"}, {"id": "C245", "pId": "C24", "singlehyname": "玩具制造", "name": "玩具制造(C245)", "isParent": true, "parentcode": "C24"}, {"id": "C2451", "pId": "C245", "singlehyname": "电玩具制造", "name": "电玩具制造(C2451)", "isParent": false, "parentcode": "C245"}, {"id": "C2452", "pId": "C245", "singlehyname": "塑胶玩具制造", "name": "塑胶玩具制造(C2452)", "isParent": false, "parentcode": "C245"}, {"id": "C2453", "pId": "C245", "singlehyname": "金属玩具制造", "name": "金属玩具制造(C2453)", "isParent": false, "parentcode": "C245"}, {"id": "C2454", "pId": "C245", "singlehyname": "弹射玩具制造", "name": "弹射玩具制造(C2454)", "isParent": false, "parentcode": "C245"}, {"id": "C2455", "pId": "C245", "singlehyname": "娃娃玩具制造", "name": "娃娃玩具制造(C2455)", "isParent": false, "parentcode": "C245"}, {"id": "C2456", "pId": "C245", "singlehyname": "儿童乘骑玩耍的童车类产品制造", "name": "儿童乘骑玩耍的童车类产品制造(C2456)", "isParent": false, "parentcode": "C245"}, {"id": "C2459", "pId": "C245", "singlehyname": "其他玩具制造", "name": "其他玩具制造(C2459)", "isParent": false, "parentcode": "C245"}, {"id": "C246", "pId": "C24", "singlehyname": "游艺器材及娱乐用品制造", "name": "游艺器材及娱乐用品制造(C246)", "isParent": true, "parentcode": "C24"}, {"id": "C2461", "pId": "C246", "singlehyname": "露天游乐场所游乐设备制造", "name": "露天游乐场所游乐设备制造(C2461)", "isParent": false, "parentcode": "C246"}, {"id": "C2462", "pId": "C246", "singlehyname": "游艺用品及室内游艺器材制造", "name": "游艺用品及室内游艺器材制造(C2462)", "isParent": false, "parentcode": "C246"}, {"id": "C2469", "pId": "C246", "singlehyname": "其他娱乐用品制造", "name": "其他娱乐用品制造(C2469)", "isParent": false, "parentcode": "C246"}, {"id": "C25", "pId": "C", "singlehyname": "石油、煤炭及其他燃料加工业", "name": "石油、煤炭及其他燃料加工业(C25)", "isParent": true, "parentcode": "C"}, {"id": "C251", "pId": "C25", "singlehyname": "精炼石油产品制造", "name": "精炼石油产品制造(C251)", "isParent": true, "parentcode": "C25"}, {"id": "C2511", "pId": "C251", "singlehyname": "原油加工及石油制品制造", "name": "原油加工及石油制品制造(C2511)", "isParent": false, "parentcode": "C251"}, {"id": "C2519", "pId": "C251", "singlehyname": "其他原油制造", "name": "其他原油制造(C2519)", "isParent": false, "parentcode": "C251"}, {"id": "C252", "pId": "C25", "singlehyname": "煤炭加工", "name": "煤炭加工(C252)", "isParent": true, "parentcode": "C25"}, {"id": "C2521", "pId": "C252", "singlehyname": "炼焦", "name": "炼焦(C2521)", "isParent": false, "parentcode": "C252"}, {"id": "C2522", "pId": "C252", "singlehyname": "煤制合成气生产", "name": "煤制合成气生产(C2522)", "isParent": false, "parentcode": "C252"}, {"id": "C2523", "pId": "C252", "singlehyname": "煤制液体燃料生产", "name": "煤制液体燃料生产(C2523)", "isParent": false, "parentcode": "C252"}, {"id": "C2524", "pId": "C252", "singlehyname": "煤制品制造", "name": "煤制品制造(C2524)", "isParent": false, "parentcode": "C252"}, {"id": "C2529", "pId": "C252", "singlehyname": "其他煤炭加工", "name": "其他煤炭加工(C2529)", "isParent": false, "parentcode": "C252"}, {"id": "C253", "pId": "C25", "singlehyname": "核燃料加工", "name": "核燃料加工(C253)", "isParent": false, "parentcode": "C25"}, {"id": "C254", "pId": "C25", "singlehyname": "生物质燃料加工", "name": "生物质燃料加工(C254)", "isParent": true, "parentcode": "C25"}, {"id": "C2541", "pId": "C254", "singlehyname": "生物质液体燃料生产", "name": "生物质液体燃料生产(C2541)", "isParent": false, "parentcode": "C254"}, {"id": "C2542", "pId": "C254", "singlehyname": "生物质致密成型燃料加工", "name": "生物质致密成型燃料加工(C2542)", "isParent": false, "parentcode": "C254"}, {"id": "C26", "pId": "C", "singlehyname": "化学原料和化学制品制造业", "name": "化学原料和化学制品制造业(C26)", "isParent": true, "parentcode": "C"}, {"id": "C261", "pId": "C26", "singlehyname": "基础化学原料制造", "name": "基础化学原料制造(C261)", "isParent": true, "parentcode": "C26"}, {"id": "C2611", "pId": "C261", "singlehyname": "无机酸制造", "name": "无机酸制造(C2611)", "isParent": false, "parentcode": "C261"}, {"id": "C2612", "pId": "C261", "singlehyname": "无机碱制造", "name": "无机碱制造(C2612)", "isParent": false, "parentcode": "C261"}, {"id": "C2613", "pId": "C261", "singlehyname": "无机盐制造", "name": "无机盐制造(C2613)", "isParent": false, "parentcode": "C261"}, {"id": "C2614", "pId": "C261", "singlehyname": "有机化学原料制造", "name": "有机化学原料制造(C2614)", "isParent": false, "parentcode": "C261"}, {"id": "C2619", "pId": "C261", "singlehyname": "其他基础化学原料制造", "name": "其他基础化学原料制造(C2619)", "isParent": false, "parentcode": "C261"}, {"id": "C262", "pId": "C26", "singlehyname": "肥料制造", "name": "肥料制造(C262)", "isParent": true, "parentcode": "C26"}, {"id": "C2621", "pId": "C262", "singlehyname": "氮肥制造", "name": "氮肥制造(C2621)", "isParent": false, "parentcode": "C262"}, {"id": "C2622", "pId": "C262", "singlehyname": "磷肥制造", "name": "磷肥制造(C2622)", "isParent": false, "parentcode": "C262"}, {"id": "C2623", "pId": "C262", "singlehyname": "钾肥制造", "name": "钾肥制造(C2623)", "isParent": false, "parentcode": "C262"}, {"id": "C2624", "pId": "C262", "singlehyname": "复混肥料制造", "name": "复混肥料制造(C2624)", "isParent": false, "parentcode": "C262"}, {"id": "C2625", "pId": "C262", "singlehyname": "有机肥料及微生物肥料制造", "name": "有机肥料及微生物肥料制造(C2625)", "isParent": false, "parentcode": "C262"}, {"id": "C2629", "pId": "C262", "singlehyname": "其他肥料制造", "name": "其他肥料制造(C2629)", "isParent": false, "parentcode": "C262"}, {"id": "C263", "pId": "C26", "singlehyname": "农药制造", "name": "农药制造(C263)", "isParent": true, "parentcode": "C26"}, {"id": "C2631", "pId": "C263", "singlehyname": "化学农药制造", "name": "化学农药制造(C2631)", "isParent": false, "parentcode": "C263"}, {"id": "C2632", "pId": "C263", "singlehyname": "生物化学农药及微生物农药制造", "name": "生物化学农药及微生物农药制造(C2632)", "isParent": false, "parentcode": "C263"}, {"id": "C264", "pId": "C26", "singlehyname": "涂料、油墨、颜料及类似产品制造", "name": "涂料、油墨、颜料及类似产品制造(C264)", "isParent": true, "parentcode": "C26"}, {"id": "C2641", "pId": "C264", "singlehyname": "涂料制造", "name": "涂料制造(C2641)", "isParent": false, "parentcode": "C264"}, {"id": "C2642", "pId": "C264", "singlehyname": "油墨及类似产品制造", "name": "油墨及类似产品制造(C2642)", "isParent": false, "parentcode": "C264"}, {"id": "C2643", "pId": "C264", "singlehyname": "工业颜料制造", "name": "工业颜料制造(C2643)", "isParent": false, "parentcode": "C264"}, {"id": "C2644", "pId": "C264", "singlehyname": "工艺美术颜料制造", "name": "工艺美术颜料制造(C2644)", "isParent": false, "parentcode": "C264"}, {"id": "C2645", "pId": "C264", "singlehyname": "染料制造", "name": "染料制造(C2645)", "isParent": false, "parentcode": "C264"}, {"id": "C2646", "pId": "C264", "singlehyname": "密封用填料及类似品制造", "name": "密封用填料及类似品制造(C2646)", "isParent": false, "parentcode": "C264"}, {"id": "C265", "pId": "C26", "singlehyname": "合成材料制造", "name": "合成材料制造(C265)", "isParent": true, "parentcode": "C26"}, {"id": "C2651", "pId": "C265", "singlehyname": "初级形态塑料及合成树脂制造", "name": "初级形态塑料及合成树脂制造(C2651)", "isParent": false, "parentcode": "C265"}, {"id": "C2651-1", "pId": "C265", "singlehyname": "初级形态塑料及合成树脂制造-聚氯乙烯", "name": "初级形态塑料及合成树脂制造-聚氯乙烯(C2651-1)", "isParent": false, "parentcode": "C265"}, {"id": "C2652", "pId": "C265", "singlehyname": "合成橡胶制造", "name": "合成橡胶制造(C2652)", "isParent": false, "parentcode": "C265"}, {"id": "C2653", "pId": "C265", "singlehyname": "合成纤维单(聚合)体制造", "name": "合成纤维单(聚合)体制造(C2653)", "isParent": false, "parentcode": "C265"}, {"id": "C2659", "pId": "C265", "singlehyname": "其他合成材料制造", "name": "其他合成材料制造(C2659)", "isParent": false, "parentcode": "C265"}, {"id": "C266", "pId": "C26", "singlehyname": "专用化学产品制造", "name": "专用化学产品制造(C266)", "isParent": true, "parentcode": "C26"}, {"id": "C2661", "pId": "C266", "singlehyname": "化学试剂和助剂制造", "name": "化学试剂和助剂制造(C2661)", "isParent": false, "parentcode": "C266"}, {"id": "C2662", "pId": "C266", "singlehyname": "专项化学用品制造", "name": "专项化学用品制造(C2662)", "isParent": false, "parentcode": "C266"}, {"id": "C2663", "pId": "C266", "singlehyname": "林产化学产品制造", "name": "林产化学产品制造(C2663)", "isParent": false, "parentcode": "C266"}, {"id": "C2664", "pId": "C266", "singlehyname": "文化用信息化学品制造", "name": "文化用信息化学品制造(C2664)", "isParent": false, "parentcode": "C266"}, {"id": "C2665", "pId": "C266", "singlehyname": "医学生产用信息化学品制造", "name": "医学生产用信息化学品制造(C2665)", "isParent": false, "parentcode": "C266"}, {"id": "C2666", "pId": "C266", "singlehyname": "环境污染处理专用药剂材料制造", "name": "环境污染处理专用药剂材料制造(C2666)", "isParent": false, "parentcode": "C266"}, {"id": "C2667", "pId": "C266", "singlehyname": "动物胶制造", "name": "动物胶制造(C2667)", "isParent": false, "parentcode": "C266"}, {"id": "C2669", "pId": "C266", "singlehyname": "其他专用化学产品制造", "name": "其他专用化学产品制造(C2669)", "isParent": false, "parentcode": "C266"}, {"id": "C267", "pId": "C26", "singlehyname": "炸药、火工及焰火产品制造", "name": "炸药、火工及焰火产品制造(C267)", "isParent": true, "parentcode": "C26"}, {"id": "C2671", "pId": "C267", "singlehyname": "炸药及火工产品制造", "name": "炸药及火工产品制造(C2671)", "isParent": false, "parentcode": "C267"}, {"id": "C2672", "pId": "C267", "singlehyname": "焰火、鞭炮产品制造", "name": "焰火、鞭炮产品制造(C2672)", "isParent": false, "parentcode": "C267"}, {"id": "C268", "pId": "C26", "singlehyname": "日用化学产品制造", "name": "日用化学产品制造(C268)", "isParent": true, "parentcode": "C26"}, {"id": "C2681", "pId": "C268", "singlehyname": "肥皂及洗涤剂制造", "name": "肥皂及洗涤剂制造(C2681)", "isParent": false, "parentcode": "C268"}, {"id": "C2682", "pId": "C268", "singlehyname": "化妆品制造", "name": "化妆品制造(C2682)", "isParent": false, "parentcode": "C268"}, {"id": "C2683", "pId": "C268", "singlehyname": "口腔清洁用品制造", "name": "口腔清洁用品制造(C2683)", "isParent": false, "parentcode": "C268"}, {"id": "C2684", "pId": "C268", "singlehyname": "香料、香精制造", "name": "香料、香精制造(C2684)", "isParent": false, "parentcode": "C268"}, {"id": "C2689", "pId": "C268", "singlehyname": "其他日用化学产品制造", "name": "其他日用化学产品制造(C2689)", "isParent": false, "parentcode": "C268"}, {"id": "C27", "pId": "C", "singlehyname": "医药制造业", "name": "医药制造业(C27)", "isParent": true, "parentcode": "C"}, {"id": "C271", "pId": "C27", "singlehyname": "化学药品原料药制造", "name": "化学药品原料药制造(C271)", "isParent": false, "parentcode": "C27"}, {"id": "C272", "pId": "C27", "singlehyname": "化学药品制剂制造", "name": "化学药品制剂制造(C272)", "isParent": false, "parentcode": "C27"}, {"id": "C273", "pId": "C27", "singlehyname": "中药饮片加工", "name": "中药饮片加工(C273)", "isParent": false, "parentcode": "C27"}, {"id": "C274", "pId": "C27", "singlehyname": "中成药生产", "name": "中成药生产(C274)", "isParent": false, "parentcode": "C27"}, {"id": "C275", "pId": "C27", "singlehyname": "兽用药品制造", "name": "兽用药品制造(C275)", "isParent": false, "parentcode": "C27"}, {"id": "C275-1", "pId": "C27", "singlehyname": "兽用中药饮片加工", "name": "兽用中药饮片加工(C275-1)", "isParent": false, "parentcode": "C27"}, {"id": "C275-2", "pId": "C27", "singlehyname": "兽用中成药生产", "name": "兽用中成药生产(C275-2)", "isParent": false, "parentcode": "C27"}, {"id": "C275-3", "pId": "C27", "singlehyname": "兽用生物药品制品", "name": "兽用生物药品制品(C275-3)", "isParent": false, "parentcode": "C27"}, {"id": "C275-4", "pId": "C27", "singlehyname": "兽用基因工程制品和疫苗制造", "name": "兽用基因工程制品和疫苗制造(C275-4)", "isParent": false, "parentcode": "C27"}, {"id": "C275-5", "pId": "C27", "singlehyname": "兽用化学药品制剂", "name": "兽用化学药品制剂(C275-5)", "isParent": false, "parentcode": "C27"}, {"id": "C276", "pId": "C27", "singlehyname": "生物药品制品制造", "name": "生物药品制品制造(C276)", "isParent": true, "parentcode": "C27"}, {"id": "C2761", "pId": "C276", "singlehyname": "生物药品制造", "name": "生物药品制造(C2761)", "isParent": false, "parentcode": "C276"}, {"id": "C2762", "pId": "C276", "singlehyname": "基因工程药物和疫苗制造", "name": "基因工程药物和疫苗制造(C2762)", "isParent": false, "parentcode": "C276"}, {"id": "C277", "pId": "C27", "singlehyname": "卫生材料及医药用品制造", "name": "卫生材料及医药用品制造(C277)", "isParent": false, "parentcode": "C27"}, {"id": "C278", "pId": "C27", "singlehyname": "药用辅料及包装材料", "name": "药用辅料及包装材料(C278)", "isParent": false, "parentcode": "C27"}, {"id": "C28", "pId": "C", "singlehyname": "化学纤维制造业", "name": "化学纤维制造业(C28)", "isParent": true, "parentcode": "C"}, {"id": "C281", "pId": "C28", "singlehyname": "纤维素纤维原料及纤维制造", "name": "纤维素纤维原料及纤维制造(C281)", "isParent": true, "parentcode": "C28"}, {"id": "C2811", "pId": "C281", "singlehyname": "化纤浆粕制造", "name": "化纤浆粕制造(C2811)", "isParent": false, "parentcode": "C281"}, {"id": "C2812", "pId": "C281", "singlehyname": "人造纤维(纤维素纤维)制造", "name": "人造纤维(纤维素纤维)制造(C2812)", "isParent": false, "parentcode": "C281"}, {"id": "C282", "pId": "C28", "singlehyname": "合成纤维制造", "name": "合成纤维制造(C282)", "isParent": true, "parentcode": "C28"}, {"id": "C2821", "pId": "C282", "singlehyname": "锦纶纤维制造", "name": "锦纶纤维制造(C2821)", "isParent": false, "parentcode": "C282"}, {"id": "C2822", "pId": "C282", "singlehyname": "涤纶纤维制造", "name": "涤纶纤维制造(C2822)", "isParent": false, "parentcode": "C282"}, {"id": "C2823", "pId": "C282", "singlehyname": "腈纶纤维制造", "name": "腈纶纤维制造(C2823)", "isParent": false, "parentcode": "C282"}, {"id": "C2824", "pId": "C282", "singlehyname": "维纶纤维制造", "name": "维纶纤维制造(C2824)", "isParent": false, "parentcode": "C282"}, {"id": "C2825", "pId": "C282", "singlehyname": "丙纶纤维制造", "name": "丙纶纤维制造(C2825)", "isParent": false, "parentcode": "C282"}, {"id": "C2826", "pId": "C282", "singlehyname": "氨纶纤维制造", "name": "氨纶纤维制造(C2826)", "isParent": false, "parentcode": "C282"}, {"id": "C2829", "pId": "C282", "singlehyname": "其他合成纤维制造", "name": "其他合成纤维制造(C2829)", "isParent": false, "parentcode": "C282"}, {"id": "C283", "pId": "C28", "singlehyname": "生物基材料制造", "name": "生物基材料制造(C283)", "isParent": true, "parentcode": "C28"}, {"id": "C2831", "pId": "C283", "singlehyname": "生物基化学纤维制造", "name": "生物基化学纤维制造(C2831)", "isParent": false, "parentcode": "C283"}, {"id": "C2832", "pId": "C283", "singlehyname": "生物基、淀粉基新材料制造", "name": "生物基、淀粉基新材料制造(C2832)", "isParent": false, "parentcode": "C283"}, {"id": "C29", "pId": "C", "singlehyname": "橡胶和塑料制品业", "name": "橡胶和塑料制品业(C29)", "isParent": true, "parentcode": "C"}, {"id": "C291", "pId": "C29", "singlehyname": "橡胶制品业", "name": "橡胶制品业(C291)", "isParent": true, "parentcode": "C29"}, {"id": "C2911", "pId": "C291", "singlehyname": "轮胎制造", "name": "轮胎制造(C2911)", "isParent": false, "parentcode": "C291"}, {"id": "C2912", "pId": "C291", "singlehyname": "橡胶板、管、带制造", "name": "橡胶板、管、带制造(C2912)", "isParent": false, "parentcode": "C291"}, {"id": "C2913", "pId": "C291", "singlehyname": "橡胶零件制造", "name": "橡胶零件制造(C2913)", "isParent": false, "parentcode": "C291"}, {"id": "C2914", "pId": "C291", "singlehyname": "再生橡胶制造", "name": "再生橡胶制造(C2914)", "isParent": false, "parentcode": "C291"}, {"id": "C2915", "pId": "C291", "singlehyname": "日用及医用橡胶制品制造", "name": "日用及医用橡胶制品制造(C2915)", "isParent": false, "parentcode": "C291"}, {"id": "C2916", "pId": "C291", "singlehyname": "运动场地用塑胶制造", "name": "运动场地用塑胶制造(C2916)", "isParent": false, "parentcode": "C291"}, {"id": "C2919", "pId": "C291", "singlehyname": "其他橡胶制品制造", "name": "其他橡胶制品制造(C2919)", "isParent": false, "parentcode": "C291"}, {"id": "C292", "pId": "C29", "singlehyname": "塑料制品业", "name": "塑料制品业(C292)", "isParent": true, "parentcode": "C29"}, {"id": "C2921", "pId": "C292", "singlehyname": "塑料薄膜制造", "name": "塑料薄膜制造(C2921)", "isParent": false, "parentcode": "C292"}, {"id": "C2922", "pId": "C292", "singlehyname": "塑料板、管、型材制造", "name": "塑料板、管、型材制造(C2922)", "isParent": false, "parentcode": "C292"}, {"id": "C2923", "pId": "C292", "singlehyname": "塑料丝、绳及编织品制造", "name": "塑料丝、绳及编织品制造(C2923)", "isParent": false, "parentcode": "C292"}, {"id": "C2924", "pId": "C292", "singlehyname": "泡沫塑料制造", "name": "泡沫塑料制造(C2924)", "isParent": false, "parentcode": "C292"}, {"id": "C2925", "pId": "C292", "singlehyname": "塑料人造革、合成革制造", "name": "塑料人造革、合成革制造(C2925)", "isParent": false, "parentcode": "C292"}, {"id": "C2926", "pId": "C292", "singlehyname": "塑料包装箱及容器制造", "name": "塑料包装箱及容器制造(C2926)", "isParent": false, "parentcode": "C292"}, {"id": "C2927", "pId": "C292", "singlehyname": "日用塑料制品制造", "name": "日用塑料制品制造(C2927)", "isParent": false, "parentcode": "C292"}, {"id": "C2928", "pId": "C292", "singlehyname": "人造草坪制造", "name": "人造草坪制造(C2928)", "isParent": false, "parentcode": "C292"}, {"id": "C2929", "pId": "C292", "singlehyname": "塑料零件及其他塑料制品制造", "name": "塑料零件及其他塑料制品制造(C2929)", "isParent": false, "parentcode": "C292"}, {"id": "C30", "pId": "C", "singlehyname": "非金属矿物制品业", "name": "非金属矿物制品业(C30)", "isParent": true, "parentcode": "C"}, {"id": "C301", "pId": "C30", "singlehyname": "水泥、石灰和石膏制造", "name": "水泥、石灰和石膏制造(C301)", "isParent": true, "parentcode": "C30"}, {"id": "C3011", "pId": "C301", "singlehyname": "水泥制造", "name": "水泥制造(C3011)", "isParent": false, "parentcode": "C301"}, {"id": "C3012", "pId": "C301", "singlehyname": "石灰和石膏制造", "name": "石灰和石膏制造(C3012)", "isParent": false, "parentcode": "C301"}, {"id": "C302", "pId": "C30", "singlehyname": "石膏、水泥制品及类似制品制造", "name": "石膏、水泥制品及类似制品制造(C302)", "isParent": true, "parentcode": "C30"}, {"id": "C3021", "pId": "C302", "singlehyname": "水泥制品制造", "name": "水泥制品制造(C3021)", "isParent": false, "parentcode": "C302"}, {"id": "C3022", "pId": "C302", "singlehyname": "砼结构构件制造", "name": "砼结构构件制造(C3022)", "isParent": false, "parentcode": "C302"}, {"id": "C3023", "pId": "C302", "singlehyname": "石棉水泥制品制造", "name": "石棉水泥制品制造(C3023)", "isParent": false, "parentcode": "C302"}, {"id": "C3024", "pId": "C302", "singlehyname": "轻质建筑材料制造", "name": "轻质建筑材料制造(C3024)", "isParent": false, "parentcode": "C302"}, {"id": "C3029", "pId": "C302", "singlehyname": "其他水泥类似制品制造", "name": "其他水泥类似制品制造(C3029)", "isParent": false, "parentcode": "C302"}, {"id": "C303", "pId": "C30", "singlehyname": "砖瓦、石材等建筑材料制造", "name": "砖瓦、石材等建筑材料制造(C303)", "isParent": true, "parentcode": "C30"}, {"id": "C3031", "pId": "C303", "singlehyname": "粘土砖瓦及建筑砌块制造", "name": "粘土砖瓦及建筑砌块制造(C3031)", "isParent": false, "parentcode": "C303"}, {"id": "C3032", "pId": "C303", "singlehyname": "建筑用石加工", "name": "建筑用石加工(C3032)", "isParent": false, "parentcode": "C303"}, {"id": "C3033", "pId": "C303", "singlehyname": "防水建筑材料制造", "name": "防水建筑材料制造(C3033)", "isParent": false, "parentcode": "C303"}, {"id": "C3034", "pId": "C303", "singlehyname": "隔热和隔音材料制造", "name": "隔热和隔音材料制造(C3034)", "isParent": false, "parentcode": "C303"}, {"id": "C3039", "pId": "C303", "singlehyname": "其他建筑材料制造", "name": "其他建筑材料制造(C3039)", "isParent": false, "parentcode": "C303"}, {"id": "C304", "pId": "C30", "singlehyname": "玻璃制造", "name": "玻璃制造(C304)", "isParent": true, "parentcode": "C30"}, {"id": "C3041", "pId": "C304", "singlehyname": "平板玻璃制造", "name": "平板玻璃制造(C3041)", "isParent": false, "parentcode": "C304"}, {"id": "C3042", "pId": "C304", "singlehyname": "特种玻璃制造", "name": "特种玻璃制造(C3042)", "isParent": false, "parentcode": "C304"}, {"id": "C3049", "pId": "C304", "singlehyname": "其他玻璃制造", "name": "其他玻璃制造(C3049)", "isParent": false, "parentcode": "C304"}, {"id": "C305", "pId": "C30", "singlehyname": "玻璃制品制造", "name": "玻璃制品制造(C305)", "isParent": true, "parentcode": "C30"}, {"id": "C3051", "pId": "C305", "singlehyname": "技术玻璃制品制造", "name": "技术玻璃制品制造(C3051)", "isParent": false, "parentcode": "C305"}, {"id": "C3052", "pId": "C305", "singlehyname": "光学玻璃制造", "name": "光学玻璃制造(C3052)", "isParent": false, "parentcode": "C305"}, {"id": "C3053", "pId": "C305", "singlehyname": "玻璃仪器制造", "name": "玻璃仪器制造(C3053)", "isParent": false, "parentcode": "C305"}, {"id": "C3054", "pId": "C305", "singlehyname": "日用玻璃制品制造", "name": "日用玻璃制品制造(C3054)", "isParent": false, "parentcode": "C305"}, {"id": "C3055", "pId": "C305", "singlehyname": "玻璃包装容器制造", "name": "玻璃包装容器制造(C3055)", "isParent": false, "parentcode": "C305"}, {"id": "C3056", "pId": "C305", "singlehyname": "玻璃保温容器制造", "name": "玻璃保温容器制造(C3056)", "isParent": false, "parentcode": "C305"}, {"id": "C3057", "pId": "C305", "singlehyname": "制镜及类似品加工", "name": "制镜及类似品加工(C3057)", "isParent": false, "parentcode": "C305"}, {"id": "C3059", "pId": "C305", "singlehyname": "其他玻璃制品制造", "name": "其他玻璃制品制造(C3059)", "isParent": false, "parentcode": "C305"}, {"id": "C306", "pId": "C30", "singlehyname": "玻璃纤维和玻璃纤维增强塑料制品制造", "name": "玻璃纤维和玻璃纤维增强塑料制品制造(C306)", "isParent": true, "parentcode": "C30"}, {"id": "C3061", "pId": "C306", "singlehyname": "玻璃纤维及制品制造", "name": "玻璃纤维及制品制造(C3061)", "isParent": false, "parentcode": "C306"}, {"id": "C3062", "pId": "C306", "singlehyname": "玻璃纤维增强塑料制品制造", "name": "玻璃纤维增强塑料制品制造(C3062)", "isParent": false, "parentcode": "C306"}, {"id": "C307", "pId": "C30", "singlehyname": "陶瓷制品制造", "name": "陶瓷制品制造(C307)", "isParent": true, "parentcode": "C30"}, {"id": "C3071", "pId": "C307", "singlehyname": "建筑陶瓷制品制造", "name": "建筑陶瓷制品制造(C3071)", "isParent": false, "parentcode": "C307"}, {"id": "C3072", "pId": "C307", "singlehyname": "卫生陶瓷制品制造", "name": "卫生陶瓷制品制造(C3072)", "isParent": false, "parentcode": "C307"}, {"id": "C3073", "pId": "C307", "singlehyname": "特种陶瓷制品制造", "name": "特种陶瓷制品制造(C3073)", "isParent": false, "parentcode": "C307"}, {"id": "C3074", "pId": "C307", "singlehyname": "日用陶瓷制品制造", "name": "日用陶瓷制品制造(C3074)", "isParent": false, "parentcode": "C307"}, {"id": "C3075", "pId": "C307", "singlehyname": "陈设艺术陶瓷制造", "name": "陈设艺术陶瓷制造(C3075)", "isParent": false, "parentcode": "C307"}, {"id": "C3076", "pId": "C307", "singlehyname": "园艺陶瓷制造", "name": "园艺陶瓷制造(C3076)", "isParent": false, "parentcode": "C307"}, {"id": "C3079", "pId": "C307", "singlehyname": "其他陶瓷制品制造", "name": "其他陶瓷制品制造(C3079)", "isParent": false, "parentcode": "C307"}, {"id": "C308", "pId": "C30", "singlehyname": "耐火材料制品制造", "name": "耐火材料制品制造(C308)", "isParent": true, "parentcode": "C30"}, {"id": "C3081", "pId": "C308", "singlehyname": "石棉制品制造", "name": "石棉制品制造(C3081)", "isParent": false, "parentcode": "C308"}, {"id": "C3082", "pId": "C308", "singlehyname": "云母制品制造", "name": "云母制品制造(C3082)", "isParent": false, "parentcode": "C308"}, {"id": "C3089", "pId": "C308", "singlehyname": "耐火陶瓷制品及其他耐火材料制造", "name": "耐火陶瓷制品及其他耐火材料制造(C3089)", "isParent": false, "parentcode": "C308"}, {"id": "C309", "pId": "C30", "singlehyname": "石墨及其他非金属矿物制品制造", "name": "石墨及其他非金属矿物制品制造(C309)", "isParent": true, "parentcode": "C30"}, {"id": "C3091", "pId": "C309", "singlehyname": "石墨及碳素制品制造", "name": "石墨及碳素制品制造(C3091)", "isParent": false, "parentcode": "C309"}, {"id": "C3099", "pId": "C309", "singlehyname": "其他非金属矿物制品制造", "name": "其他非金属矿物制品制造(C3099)", "isParent": false, "parentcode": "C309"}, {"id": "C31", "pId": "C", "singlehyname": "黑色金属冶炼和压延加工业", "name": "黑色金属冶炼和压延加工业(C31)", "isParent": true, "parentcode": "C"}, {"id": "C311", "pId": "C31", "singlehyname": "炼铁", "name": "炼铁(C311)", "isParent": false, "parentcode": "C31"}, {"id": "C312", "pId": "C31", "singlehyname": "炼钢", "name": "炼钢(C312)", "isParent": false, "parentcode": "C31"}, {"id": "C313", "pId": "C31", "singlehyname": "钢压延加工", "name": "钢压延加工(C313)", "isParent": false, "parentcode": "C31"}, {"id": "C314", "pId": "C31", "singlehyname": "铁合金冶炼", "name": "铁合金冶炼(C314)", "isParent": true, "parentcode": "C31"}, {"id": "C314-1", "pId": "C314", "singlehyname": "铁合金", "name": "铁合金(C314-1)", "isParent": false, "parentcode": "C314"}, {"id": "C314-2", "pId": "C314", "singlehyname": "电解锰", "name": "电解锰(C314-2)", "isParent": false, "parentcode": "C314"}, {"id": "C32", "pId": "C", "singlehyname": "有色金属冶炼和压延加工业", "name": "有色金属冶炼和压延加工业(C32)", "isParent": true, "parentcode": "C"}, {"id": "C321", "pId": "C32", "singlehyname": "常用有色金属冶炼", "name": "常用有色金属冶炼(C321)", "isParent": true, "parentcode": "C32"}, {"id": "C3211", "pId": "C321", "singlehyname": "铜冶炼", "name": "铜冶炼(C3211)", "isParent": false, "parentcode": "C321"}, {"id": "C3212", "pId": "C321", "singlehyname": "铅锌冶炼", "name": "铅锌冶炼(C3212)", "isParent": false, "parentcode": "C321"}, {"id": "C3213", "pId": "C321", "singlehyname": "镍钴冶炼", "name": "镍钴冶炼(C3213)", "isParent": false, "parentcode": "C321"}, {"id": "C3214", "pId": "C321", "singlehyname": "锡冶炼", "name": "锡冶炼(C3214)", "isParent": false, "parentcode": "C321"}, {"id": "C3215", "pId": "C321", "singlehyname": "锑冶炼", "name": "锑冶炼(C3215)", "isParent": false, "parentcode": "C321"}, {"id": "C3216", "pId": "C321", "singlehyname": "铝冶炼", "name": "铝冶炼(C3216)", "isParent": false, "parentcode": "C321"}, {"id": "C3217", "pId": "C321", "singlehyname": "镁冶炼", "name": "镁冶炼(C3217)", "isParent": false, "parentcode": "C321"}, {"id": "C3218", "pId": "C321", "singlehyname": "硅冶炼", "name": "硅冶炼(C3218)", "isParent": false, "parentcode": "C321"}, {"id": "C3219", "pId": "C321", "singlehyname": "其他常用有色金属冶炼", "name": "其他常用有色金属冶炼(C3219)", "isParent": false, "parentcode": "C321"}, {"id": "C322", "pId": "C32", "singlehyname": "贵金属冶炼", "name": "贵金属冶炼(C322)", "isParent": true, "parentcode": "C32"}, {"id": "C3221", "pId": "C322", "singlehyname": "金冶炼", "name": "金冶炼(C3221)", "isParent": false, "parentcode": "C322"}, {"id": "C3222", "pId": "C322", "singlehyname": "银冶炼", "name": "银冶炼(C3222)", "isParent": false, "parentcode": "C322"}, {"id": "C3229", "pId": "C322", "singlehyname": "其他贵金属冶炼", "name": "其他贵金属冶炼(C3229)", "isParent": false, "parentcode": "C322"}, {"id": "C323", "pId": "C32", "singlehyname": "稀有稀土金属冶炼", "name": "稀有稀土金属冶炼(C323)", "isParent": true, "parentcode": "C32"}, {"id": "C3231", "pId": "C323", "singlehyname": "钨钼冶炼", "name": "钨钼冶炼(C3231)", "isParent": false, "parentcode": "C323"}, {"id": "C3232", "pId": "C323", "singlehyname": "稀土金属冶炼", "name": "稀土金属冶炼(C3232)", "isParent": false, "parentcode": "C323"}, {"id": "C3239", "pId": "C323", "singlehyname": "其他稀有金属冶炼", "name": "其他稀有金属冶炼(C3239)", "isParent": false, "parentcode": "C323"}, {"id": "C324", "pId": "C32", "singlehyname": "有色金属合金制造", "name": "有色金属合金制造(C324)", "isParent": false, "parentcode": "C32"}, {"id": "C325", "pId": "C32", "singlehyname": "有色金属压延加工", "name": "有色金属压延加工(C325)", "isParent": true, "parentcode": "C32"}, {"id": "C3251", "pId": "C325", "singlehyname": "铜压延加工", "name": "铜压延加工(C3251)", "isParent": false, "parentcode": "C325"}, {"id": "C3252", "pId": "C325", "singlehyname": "铝压延加工", "name": "铝压延加工(C3252)", "isParent": false, "parentcode": "C325"}, {"id": "C3253", "pId": "C325", "singlehyname": "贵金属压延加工", "name": "贵金属压延加工(C3253)", "isParent": false, "parentcode": "C325"}, {"id": "C3254", "pId": "C325", "singlehyname": "稀有稀土金属压延加工", "name": "稀有稀土金属压延加工(C3254)", "isParent": false, "parentcode": "C325"}, {"id": "C3259", "pId": "C325", "singlehyname": "其他有色金属压延加工", "name": "其他有色金属压延加工(C3259)", "isParent": false, "parentcode": "C325"}, {"id": "C33", "pId": "C", "singlehyname": "金属制品业", "name": "金属制品业(C33)", "isParent": true, "parentcode": "C"}, {"id": "C331", "pId": "C33", "singlehyname": "结构性金属制品制造", "name": "结构性金属制品制造(C331)", "isParent": true, "parentcode": "C33"}, {"id": "C3311", "pId": "C331", "singlehyname": "金属结构制造", "name": "金属结构制造(C3311)", "isParent": false, "parentcode": "C331"}, {"id": "C3312", "pId": "C331", "singlehyname": "金属门窗制造", "name": "金属门窗制造(C3312)", "isParent": false, "parentcode": "C331"}, {"id": "C332", "pId": "C33", "singlehyname": "金属工具制造", "name": "金属工具制造(C332)", "isParent": true, "parentcode": "C33"}, {"id": "C3321", "pId": "C332", "singlehyname": "切削工具制造", "name": "切削工具制造(C3321)", "isParent": false, "parentcode": "C332"}, {"id": "C3322", "pId": "C332", "singlehyname": "手工具制造", "name": "手工具制造(C3322)", "isParent": false, "parentcode": "C332"}, {"id": "C3323", "pId": "C332", "singlehyname": "农用及园林用金属工具制造", "name": "农用及园林用金属工具制造(C3323)", "isParent": false, "parentcode": "C332"}, {"id": "C3324", "pId": "C332", "singlehyname": "刀剪及类似日用金属工具制造", "name": "刀剪及类似日用金属工具制造(C3324)", "isParent": false, "parentcode": "C332"}, {"id": "C3329", "pId": "C332", "singlehyname": "其他金属工具制造", "name": "其他金属工具制造(C3329)", "isParent": false, "parentcode": "C332"}, {"id": "C333", "pId": "C33", "singlehyname": "集装箱及金属包装容器制造", "name": "集装箱及金属包装容器制造(C333)", "isParent": true, "parentcode": "C33"}, {"id": "C3331", "pId": "C333", "singlehyname": "集装箱制造", "name": "集装箱制造(C3331)", "isParent": false, "parentcode": "C333"}, {"id": "C3332", "pId": "C333", "singlehyname": "金属压力容器制造", "name": "金属压力容器制造(C3332)", "isParent": false, "parentcode": "C333"}, {"id": "C3333", "pId": "C333", "singlehyname": "金属包装容器及材料制造", "name": "金属包装容器及材料制造(C3333)", "isParent": false, "parentcode": "C333"}, {"id": "C334", "pId": "C33", "singlehyname": "金属丝绳及其制品制造", "name": "金属丝绳及其制品制造(C334)", "isParent": false, "parentcode": "C33"}, {"id": "C335", "pId": "C33", "singlehyname": "建筑、安全用金属制品制造", "name": "建筑、安全用金属制品制造(C335)", "isParent": true, "parentcode": "C33"}, {"id": "C3351", "pId": "C335", "singlehyname": "建筑、家具用金属配件制造", "name": "建筑、家具用金属配件制造(C3351)", "isParent": false, "parentcode": "C335"}, {"id": "C3352", "pId": "C335", "singlehyname": "建筑装饰及水暖管道零件制造", "name": "建筑装饰及水暖管道零件制造(C3352)", "isParent": false, "parentcode": "C335"}, {"id": "C3353", "pId": "C335", "singlehyname": "安全、消防用金属制品制造", "name": "安全、消防用金属制品制造(C3353)", "isParent": false, "parentcode": "C335"}, {"id": "C3359", "pId": "C335", "singlehyname": "其他建筑、安全用金属制品制造", "name": "其他建筑、安全用金属制品制造(C3359)", "isParent": false, "parentcode": "C335"}, {"id": "C336", "pId": "C33", "singlehyname": "金属表面处理及热处理加工", "name": "金属表面处理及热处理加工(C336)", "isParent": false, "parentcode": "C33"}, {"id": "C337", "pId": "C33", "singlehyname": "搪瓷制品制造", "name": "搪瓷制品制造(C337)", "isParent": true, "parentcode": "C33"}, {"id": "C3371", "pId": "C337", "singlehyname": "生产专用搪瓷制品制造", "name": "生产专用搪瓷制品制造(C3371)", "isParent": false, "parentcode": "C337"}, {"id": "C3372", "pId": "C337", "singlehyname": "建筑装饰搪瓷制品制造", "name": "建筑装饰搪瓷制品制造(C3372)", "isParent": false, "parentcode": "C337"}, {"id": "C3373", "pId": "C337", "singlehyname": "搪瓷卫生洁具制造", "name": "搪瓷卫生洁具制造(C3373)", "isParent": false, "parentcode": "C337"}, {"id": "C3379", "pId": "C337", "singlehyname": "搪瓷日用品及其他搪瓷制品制造", "name": "搪瓷日用品及其他搪瓷制品制造(C3379)", "isParent": false, "parentcode": "C337"}, {"id": "C338", "pId": "C33", "singlehyname": "金属制日用品制造", "name": "金属制日用品制造(C338)", "isParent": true, "parentcode": "C33"}, {"id": "C3381", "pId": "C338", "singlehyname": "金属制厨房用器具制造", "name": "金属制厨房用器具制造(C3381)", "isParent": false, "parentcode": "C338"}, {"id": "C3382", "pId": "C338", "singlehyname": "金属制餐具和器皿制造", "name": "金属制餐具和器皿制造(C3382)", "isParent": false, "parentcode": "C338"}, {"id": "C3383", "pId": "C338", "singlehyname": "金属制卫生器具制造", "name": "金属制卫生器具制造(C3383)", "isParent": false, "parentcode": "C338"}, {"id": "C3389", "pId": "C338", "singlehyname": "其他金属制日用品制造", "name": "其他金属制日用品制造(C3389)", "isParent": false, "parentcode": "C338"}, {"id": "C339", "pId": "C33", "singlehyname": "其他金属制品制造", "name": "其他金属制品制造(C339)", "isParent": true, "parentcode": "C33"}, {"id": "C3391", "pId": "C339", "singlehyname": "黑色金属铸造", "name": "黑色金属铸造(C3391)", "isParent": false, "parentcode": "C339"}, {"id": "C3392", "pId": "C339", "singlehyname": "有色金属铸造", "name": "有色金属铸造(C3392)", "isParent": false, "parentcode": "C339"}, {"id": "C3393", "pId": "C339", "singlehyname": "锻件及粉末冶金制品制造", "name": "锻件及粉末冶金制品制造(C3393)", "isParent": false, "parentcode": "C339"}, {"id": "C3394", "pId": "C339", "singlehyname": "交通及公共管理用金属标牌制造", "name": "交通及公共管理用金属标牌制造(C3394)", "isParent": false, "parentcode": "C339"}, {"id": "C3399", "pId": "C339", "singlehyname": "其他未列明金属制品制造", "name": "其他未列明金属制品制造(C3399)", "isParent": false, "parentcode": "C339"}, {"id": "C34", "pId": "C", "singlehyname": "通用设备制造业", "name": "通用设备制造业(C34)", "isParent": true, "parentcode": "C"}, {"id": "C341", "pId": "C34", "singlehyname": "锅炉及原动设备制造", "name": "锅炉及原动设备制造(C341)", "isParent": true, "parentcode": "C34"}, {"id": "C3411", "pId": "C341", "singlehyname": "锅炉及辅助设备制造", "name": "锅炉及辅助设备制造(C3411)", "isParent": false, "parentcode": "C341"}, {"id": "C3412", "pId": "C341", "singlehyname": "内燃机及配件制造", "name": "内燃机及配件制造(C3412)", "isParent": false, "parentcode": "C341"}, {"id": "C3413", "pId": "C341", "singlehyname": "汽轮机及辅机制造", "name": "汽轮机及辅机制造(C3413)", "isParent": false, "parentcode": "C341"}, {"id": "C3414", "pId": "C341", "singlehyname": "水轮机及辅机制造", "name": "水轮机及辅机制造(C3414)", "isParent": false, "parentcode": "C341"}, {"id": "C3415", "pId": "C341", "singlehyname": "风能原动设备制造", "name": "风能原动设备制造(C3415)", "isParent": false, "parentcode": "C341"}, {"id": "C3419", "pId": "C341", "singlehyname": "其他原动设备制造", "name": "其他原动设备制造(C3419)", "isParent": false, "parentcode": "C341"}, {"id": "C342", "pId": "C34", "singlehyname": "金属加工机械制造", "name": "金属加工机械制造(C342)", "isParent": true, "parentcode": "C34"}, {"id": "C3421", "pId": "C342", "singlehyname": "金属切削机床制造", "name": "金属切削机床制造(C3421)", "isParent": false, "parentcode": "C342"}, {"id": "C3422", "pId": "C342", "singlehyname": "金属成形机床制造", "name": "金属成形机床制造(C3422)", "isParent": false, "parentcode": "C342"}, {"id": "C3423", "pId": "C342", "singlehyname": "铸造机械制造", "name": "铸造机械制造(C3423)", "isParent": false, "parentcode": "C342"}, {"id": "C3424", "pId": "C342", "singlehyname": "金属切割及焊接设备制造", "name": "金属切割及焊接设备制造(C3424)", "isParent": false, "parentcode": "C342"}, {"id": "C3425", "pId": "C342", "singlehyname": "机床功能部件及附件制造", "name": "机床功能部件及附件制造(C3425)", "isParent": false, "parentcode": "C342"}, {"id": "C3429", "pId": "C342", "singlehyname": "其他金属加工机械制造", "name": "其他金属加工机械制造(C3429)", "isParent": false, "parentcode": "C342"}, {"id": "C343", "pId": "C34", "singlehyname": "物料搬运设备制造", "name": "物料搬运设备制造(C343)", "isParent": true, "parentcode": "C34"}, {"id": "C3431", "pId": "C343", "singlehyname": "轻小型起重设备制造", "name": "轻小型起重设备制造(C3431)", "isParent": false, "parentcode": "C343"}, {"id": "C3432", "pId": "C343", "singlehyname": "生产专用起重机制造", "name": "生产专用起重机制造(C3432)", "isParent": false, "parentcode": "C343"}, {"id": "C3433", "pId": "C343", "singlehyname": "生产专用车辆制造", "name": "生产专用车辆制造(C3433)", "isParent": false, "parentcode": "C343"}, {"id": "C3434", "pId": "C343", "singlehyname": "连续搬运设备制造", "name": "连续搬运设备制造(C3434)", "isParent": false, "parentcode": "C343"}, {"id": "C3435", "pId": "C343", "singlehyname": "电梯、自动扶梯及升降机制造", "name": "电梯、自动扶梯及升降机制造(C3435)", "isParent": false, "parentcode": "C343"}, {"id": "C3436", "pId": "C343", "singlehyname": "客运索道制造", "name": "客运索道制造(C3436)", "isParent": false, "parentcode": "C343"}, {"id": "C3437", "pId": "C343", "singlehyname": "机械式停车设备制造", "name": "机械式停车设备制造(C3437)", "isParent": false, "parentcode": "C343"}, {"id": "C3439", "pId": "C343", "singlehyname": "其他物料搬运设备制造", "name": "其他物料搬运设备制造(C3439)", "isParent": false, "parentcode": "C343"}, {"id": "C344", "pId": "C34", "singlehyname": "泵、阀门、压缩机及类似机械制造", "name": "泵、阀门、压缩机及类似机械制造(C344)", "isParent": true, "parentcode": "C34"}, {"id": "C3441", "pId": "C344", "singlehyname": "泵及真空设备制造", "name": "泵及真空设备制造(C3441)", "isParent": false, "parentcode": "C344"}, {"id": "C3442", "pId": "C344", "singlehyname": "气体压缩机械制造", "name": "气体压缩机械制造(C3442)", "isParent": false, "parentcode": "C344"}, {"id": "C3443", "pId": "C344", "singlehyname": "阀门和旋塞制造", "name": "阀门和旋塞制造(C3443)", "isParent": false, "parentcode": "C344"}, {"id": "C3444", "pId": "C344", "singlehyname": "液压动力机械及元件制造", "name": "液压动力机械及元件制造(C3444)", "isParent": false, "parentcode": "C344"}, {"id": "C3445", "pId": "C344", "singlehyname": "液力动力机械元件制造", "name": "液力动力机械元件制造(C3445)", "isParent": false, "parentcode": "C344"}, {"id": "C3446", "pId": "C344", "singlehyname": "气压动力机械及元件制造", "name": "气压动力机械及元件制造(C3446)", "isParent": false, "parentcode": "C344"}, {"id": "C345", "pId": "C34", "singlehyname": "轴承、齿轮和传动部件制造", "name": "轴承、齿轮和传动部件制造(C345)", "isParent": true, "parentcode": "C34"}, {"id": "C3451", "pId": "C345", "singlehyname": "滚动轴承制造", "name": "滚动轴承制造(C3451)", "isParent": false, "parentcode": "C345"}, {"id": "C3452", "pId": "C345", "singlehyname": "滑动轴承制造", "name": "滑动轴承制造(C3452)", "isParent": false, "parentcode": "C345"}, {"id": "C3453", "pId": "C345", "singlehyname": "齿轮及齿轮减、变速箱制造", "name": "齿轮及齿轮减、变速箱制造(C3453)", "isParent": false, "parentcode": "C345"}, {"id": "C3459", "pId": "C345", "singlehyname": "其他传动部件制造", "name": "其他传动部件制造(C3459)", "isParent": false, "parentcode": "C345"}, {"id": "C346", "pId": "C34", "singlehyname": "烘炉、风机、包装等设备制造", "name": "烘炉、风机、包装等设备制造(C346)", "isParent": true, "parentcode": "C34"}, {"id": "C3461", "pId": "C346", "singlehyname": "烘炉、熔炉及电炉制造", "name": "烘炉、熔炉及电炉制造(C3461)", "isParent": false, "parentcode": "C346"}, {"id": "C3462", "pId": "C346", "singlehyname": "风机、风扇制造", "name": "风机、风扇制造(C3462)", "isParent": false, "parentcode": "C346"}, {"id": "C3463", "pId": "C346", "singlehyname": "气体、液体分离及纯净设备制造", "name": "气体、液体分离及纯净设备制造(C3463)", "isParent": false, "parentcode": "C346"}, {"id": "C3464", "pId": "C346", "singlehyname": "制冷、空调设备制造", "name": "制冷、空调设备制造(C3464)", "isParent": false, "parentcode": "C346"}, {"id": "C3465", "pId": "C346", "singlehyname": "风动和电动工具制造", "name": "风动和电动工具制造(C3465)", "isParent": false, "parentcode": "C346"}, {"id": "C3466", "pId": "C346", "singlehyname": "喷枪及类似器具制造", "name": "喷枪及类似器具制造(C3466)", "isParent": false, "parentcode": "C346"}, {"id": "C3467", "pId": "C346", "singlehyname": "包装专用设备制造", "name": "包装专用设备制造(C3467)", "isParent": false, "parentcode": "C346"}, {"id": "C347", "pId": "C34", "singlehyname": "文化、办公用机械制造", "name": "文化、办公用机械制造(C347)", "isParent": true, "parentcode": "C34"}, {"id": "C3471", "pId": "C347", "singlehyname": "电影机械制造", "name": "电影机械制造(C3471)", "isParent": false, "parentcode": "C347"}, {"id": "C3472", "pId": "C347", "singlehyname": "幻灯及投影设备制造", "name": "幻灯及投影设备制造(C3472)", "isParent": false, "parentcode": "C347"}, {"id": "C3473", "pId": "C347", "singlehyname": "照相机及器材制造", "name": "照相机及器材制造(C3473)", "isParent": false, "parentcode": "C347"}, {"id": "C3474", "pId": "C347", "singlehyname": "复印和胶印设备制造", "name": "复印和胶印设备制造(C3474)", "isParent": false, "parentcode": "C347"}, {"id": "C3475", "pId": "C347", "singlehyname": "计算器及货币专用设备制造", "name": "计算器及货币专用设备制造(C3475)", "isParent": false, "parentcode": "C347"}, {"id": "C3479", "pId": "C347", "singlehyname": "其他文化、办公用机械制造", "name": "其他文化、办公用机械制造(C3479)", "isParent": false, "parentcode": "C347"}, {"id": "C348", "pId": "C34", "singlehyname": "通用零部件制造", "name": "通用零部件制造(C348)", "isParent": true, "parentcode": "C34"}, {"id": "C3481", "pId": "C348", "singlehyname": "金属密封件制造", "name": "金属密封件制造(C3481)", "isParent": false, "parentcode": "C348"}, {"id": "C3482", "pId": "C348", "singlehyname": "紧固件制造", "name": "紧固件制造(C3482)", "isParent": false, "parentcode": "C348"}, {"id": "C3483", "pId": "C348", "singlehyname": "弹簧制造", "name": "弹簧制造(C3483)", "isParent": false, "parentcode": "C348"}, {"id": "C3484", "pId": "C348", "singlehyname": "机械零部件加工", "name": "机械零部件加工(C3484)", "isParent": false, "parentcode": "C348"}, {"id": "C3489", "pId": "C348", "singlehyname": "其他通用零部件制造", "name": "其他通用零部件制造(C3489)", "isParent": false, "parentcode": "C348"}, {"id": "C349", "pId": "C34", "singlehyname": "其他通用设备制造业", "name": "其他通用设备制造业(C349)", "isParent": true, "parentcode": "C34"}, {"id": "C3491", "pId": "C349", "singlehyname": "工业机器人制造", "name": "工业机器人制造(C3491)", "isParent": false, "parentcode": "C349"}, {"id": "C3492", "pId": "C349", "singlehyname": "特殊作业机器人制造", "name": "特殊作业机器人制造(C3492)", "isParent": false, "parentcode": "C349"}, {"id": "C3493", "pId": "C349", "singlehyname": "增材制造装备制造", "name": "增材制造装备制造(C3493)", "isParent": false, "parentcode": "C349"}, {"id": "C3499", "pId": "C349", "singlehyname": "其他未列明通用设备制造业", "name": "其他未列明通用设备制造业(C3499)", "isParent": false, "parentcode": "C349"}, {"id": "C35", "pId": "C", "singlehyname": "专用设备制造业", "name": "专用设备制造业(C35)", "isParent": true, "parentcode": "C"}, {"id": "C351", "pId": "C35", "singlehyname": "采矿、冶金、建筑专用设备制造", "name": "采矿、冶金、建筑专用设备制造(C351)", "isParent": true, "parentcode": "C35"}, {"id": "C3511", "pId": "C351", "singlehyname": "矿山机械制造", "name": "矿山机械制造(C3511)", "isParent": false, "parentcode": "C351"}, {"id": "C3512", "pId": "C351", "singlehyname": "石油钻采专用设备制造", "name": "石油钻采专用设备制造(C3512)", "isParent": false, "parentcode": "C351"}, {"id": "C3513", "pId": "C351", "singlehyname": "深海石油钻探设备制造", "name": "深海石油钻探设备制造(C3513)", "isParent": false, "parentcode": "C351"}, {"id": "C3514", "pId": "C351", "singlehyname": "建筑工程用机械制造", "name": "建筑工程用机械制造(C3514)", "isParent": false, "parentcode": "C351"}, {"id": "C3515", "pId": "C351", "singlehyname": "建筑材料生产专用机械制造", "name": "建筑材料生产专用机械制造(C3515)", "isParent": false, "parentcode": "C351"}, {"id": "C3516", "pId": "C351", "singlehyname": "冶金专用设备制造", "name": "冶金专用设备制造(C3516)", "isParent": false, "parentcode": "C351"}, {"id": "C3517", "pId": "C351", "singlehyname": "隧道施工专用机械制造", "name": "隧道施工专用机械制造(C3517)", "isParent": false, "parentcode": "C351"}, {"id": "C352", "pId": "C35", "singlehyname": "化工、木材、非金属加工专用设备制造", "name": "化工、木材、非金属加工专用设备制造(C352)", "isParent": true, "parentcode": "C35"}, {"id": "C3521", "pId": "C352", "singlehyname": "炼油、化工生产专用设备制造", "name": "炼油、化工生产专用设备制造(C3521)", "isParent": false, "parentcode": "C352"}, {"id": "C3522", "pId": "C352", "singlehyname": "橡胶加工专用设备制造", "name": "橡胶加工专用设备制造(C3522)", "isParent": false, "parentcode": "C352"}, {"id": "C3523", "pId": "C352", "singlehyname": "塑料加工专用设备制造", "name": "塑料加工专用设备制造(C3523)", "isParent": false, "parentcode": "C352"}, {"id": "C3524", "pId": "C352", "singlehyname": "木竹材加工机械制造", "name": "木竹材加工机械制造(C3524)", "isParent": false, "parentcode": "C352"}, {"id": "C3525", "pId": "C352", "singlehyname": "模具制造", "name": "模具制造(C3525)", "isParent": false, "parentcode": "C352"}, {"id": "C3529", "pId": "C352", "singlehyname": "其他非金属加工专用设备制造", "name": "其他非金属加工专用设备制造(C3529)", "isParent": false, "parentcode": "C352"}, {"id": "C353", "pId": "C35", "singlehyname": "食品、饮料、烟草及饲料生产专用设备制造", "name": "食品、饮料、烟草及饲料生产专用设备制造(C353)", "isParent": true, "parentcode": "C35"}, {"id": "C3531", "pId": "C353", "singlehyname": "食品、酒、饮料及茶生产专用设备制造", "name": "食品、酒、饮料及茶生产专用设备制造(C3531)", "isParent": false, "parentcode": "C353"}, {"id": "C3532", "pId": "C353", "singlehyname": "农副食品加工专用设备制造", "name": "农副食品加工专用设备制造(C3532)", "isParent": false, "parentcode": "C353"}, {"id": "C3533", "pId": "C353", "singlehyname": "烟草生产专用设备制造", "name": "烟草生产专用设备制造(C3533)", "isParent": false, "parentcode": "C353"}, {"id": "C3534", "pId": "C353", "singlehyname": "饲料生产专用设备制造", "name": "饲料生产专用设备制造(C3534)", "isParent": false, "parentcode": "C353"}, {"id": "C354", "pId": "C35", "singlehyname": "印刷、制药、日化及日用品生产专用设备制造", "name": "印刷、制药、日化及日用品生产专用设备制造(C354)", "isParent": true, "parentcode": "C35"}, {"id": "C3541", "pId": "C354", "singlehyname": "制浆和造纸专用设备制造", "name": "制浆和造纸专用设备制造(C3541)", "isParent": false, "parentcode": "C354"}, {"id": "C3542", "pId": "C354", "singlehyname": "印刷专用设备制造", "name": "印刷专用设备制造(C3542)", "isParent": false, "parentcode": "C354"}, {"id": "C3543", "pId": "C354", "singlehyname": "日用化工专用设备制造", "name": "日用化工专用设备制造(C3543)", "isParent": false, "parentcode": "C354"}, {"id": "C3544", "pId": "C354", "singlehyname": "制药专用设备制造", "name": "制药专用设备制造(C3544)", "isParent": false, "parentcode": "C354"}, {"id": "C3545", "pId": "C354", "singlehyname": "照明器具生产专用设备制造", "name": "照明器具生产专用设备制造(C3545)", "isParent": false, "parentcode": "C354"}, {"id": "C3546", "pId": "C354", "singlehyname": "玻璃、陶瓷和搪瓷制品生产专用设备制造", "name": "玻璃、陶瓷和搪瓷制品生产专用设备制造(C3546)", "isParent": false, "parentcode": "C354"}, {"id": "C3549", "pId": "C354", "singlehyname": "其他日用品生产专用设备制造", "name": "其他日用品生产专用设备制造(C3549)", "isParent": false, "parentcode": "C354"}, {"id": "C355", "pId": "C35", "singlehyname": "纺织、服装和皮革加工专用设备制造", "name": "纺织、服装和皮革加工专用设备制造(C355)", "isParent": true, "parentcode": "C35"}, {"id": "C3551", "pId": "C355", "singlehyname": "纺织专用设备制造", "name": "纺织专用设备制造(C3551)", "isParent": false, "parentcode": "C355"}, {"id": "C3552", "pId": "C355", "singlehyname": "皮革、毛皮及其制品加工专用设备制造", "name": "皮革、毛皮及其制品加工专用设备制造(C3552)", "isParent": false, "parentcode": "C355"}, {"id": "C3553", "pId": "C355", "singlehyname": "缝制机械制造", "name": "缝制机械制造(C3553)", "isParent": false, "parentcode": "C355"}, {"id": "C3554", "pId": "C355", "singlehyname": "洗涤机械制造", "name": "洗涤机械制造(C3554)", "isParent": false, "parentcode": "C355"}, {"id": "C356", "pId": "C35", "singlehyname": "电子和电工机械专用设备制造", "name": "电子和电工机械专用设备制造(C356)", "isParent": true, "parentcode": "C35"}, {"id": "C3561", "pId": "C356", "singlehyname": "电工机械专用设备制造", "name": "电工机械专用设备制造(C3561)", "isParent": false, "parentcode": "C356"}, {"id": "C3562", "pId": "C356", "singlehyname": "半导体器件专用设备制造", "name": "半导体器件专用设备制造(C3562)", "isParent": false, "parentcode": "C356"}, {"id": "C3563", "pId": "C356", "singlehyname": "电子元器件与机电组件设备制造", "name": "电子元器件与机电组件设备制造(C3563)", "isParent": false, "parentcode": "C356"}, {"id": "C3569", "pId": "C356", "singlehyname": "其他电子专用设备制造", "name": "其他电子专用设备制造(C3569)", "isParent": false, "parentcode": "C356"}, {"id": "C357", "pId": "C35", "singlehyname": "农、林、牧、渔专用机械制造", "name": "农、林、牧、渔专用机械制造(C357)", "isParent": true, "parentcode": "C35"}, {"id": "C3571", "pId": "C357", "singlehyname": "拖拉机制造", "name": "拖拉机制造(C3571)", "isParent": false, "parentcode": "C357"}, {"id": "C3572", "pId": "C357", "singlehyname": "机械化农业及园艺机具制造", "name": "机械化农业及园艺机具制造(C3572)", "isParent": false, "parentcode": "C357"}, {"id": "C3573", "pId": "C357", "singlehyname": "营林及木竹采伐机械制造", "name": "营林及木竹采伐机械制造(C3573)", "isParent": false, "parentcode": "C357"}, {"id": "C3574", "pId": "C357", "singlehyname": "畜牧机械制造", "name": "畜牧机械制造(C3574)", "isParent": false, "parentcode": "C357"}, {"id": "C3575", "pId": "C357", "singlehyname": "渔业机械制造", "name": "渔业机械制造(C3575)", "isParent": false, "parentcode": "C357"}, {"id": "C3576", "pId": "C357", "singlehyname": "农林牧渔机械配件制造", "name": "农林牧渔机械配件制造(C3576)", "isParent": false, "parentcode": "C357"}, {"id": "C3577", "pId": "C357", "singlehyname": "棉花加工机械制造", "name": "棉花加工机械制造(C3577)", "isParent": false, "parentcode": "C357"}, {"id": "C3579", "pId": "C357", "singlehyname": "其他农、林、牧、渔业机械制造", "name": "其他农、林、牧、渔业机械制造(C3579)", "isParent": false, "parentcode": "C357"}, {"id": "C358", "pId": "C35", "singlehyname": "医疗仪器设备及器械制造", "name": "医疗仪器设备及器械制造(C358)", "isParent": true, "parentcode": "C35"}, {"id": "C3581", "pId": "C358", "singlehyname": "医疗诊断、监护及治疗设备制造", "name": "医疗诊断、监护及治疗设备制造(C3581)", "isParent": false, "parentcode": "C358"}, {"id": "C3582", "pId": "C358", "singlehyname": "口腔科用设备及器具制造", "name": "口腔科用设备及器具制造(C3582)", "isParent": false, "parentcode": "C358"}, {"id": "C3583", "pId": "C358", "singlehyname": "医疗实验室及医用消毒设备和器具制造", "name": "医疗实验室及医用消毒设备和器具制造(C3583)", "isParent": false, "parentcode": "C358"}, {"id": "C3584", "pId": "C358", "singlehyname": "医疗、外科及兽医用器械制造", "name": "医疗、外科及兽医用器械制造(C3584)", "isParent": false, "parentcode": "C358"}, {"id": "C3585", "pId": "C358", "singlehyname": "机械治疗及病房护理设备制造", "name": "机械治疗及病房护理设备制造(C3585)", "isParent": false, "parentcode": "C358"}, {"id": "C3586", "pId": "C358", "singlehyname": "康复辅具制造", "name": "康复辅具制造(C3586)", "isParent": false, "parentcode": "C358"}, {"id": "C3587", "pId": "C358", "singlehyname": "眼镜制造", "name": "眼镜制造(C3587)", "isParent": false, "parentcode": "C358"}, {"id": "C3589", "pId": "C358", "singlehyname": "其他医疗设备及器械制造", "name": "其他医疗设备及器械制造(C3589)", "isParent": false, "parentcode": "C358"}, {"id": "C359", "pId": "C35", "singlehyname": "环保、邮政、社会公共服务及其他专用设备制造", "name": "环保、邮政、社会公共服务及其他专用设备制造(C359)", "isParent": true, "parentcode": "C35"}, {"id": "C3591", "pId": "C359", "singlehyname": "环境保护专用设备制造", "name": "环境保护专用设备制造(C3591)", "isParent": false, "parentcode": "C359"}, {"id": "C3592", "pId": "C359", "singlehyname": "地质勘查专用设备制造", "name": "地质勘查专用设备制造(C3592)", "isParent": false, "parentcode": "C359"}, {"id": "C3593", "pId": "C359", "singlehyname": "邮政专用机械及器材制造", "name": "邮政专用机械及器材制造(C3593)", "isParent": false, "parentcode": "C359"}, {"id": "C3594", "pId": "C359", "singlehyname": "商业、饮食、服务专用设备制造", "name": "商业、饮食、服务专用设备制造(C3594)", "isParent": false, "parentcode": "C359"}, {"id": "C3595", "pId": "C359", "singlehyname": "社会公共安全设备及器材制造", "name": "社会公共安全设备及器材制造(C3595)", "isParent": false, "parentcode": "C359"}, {"id": "C3596", "pId": "C359", "singlehyname": "交通安全、管制及类似专用设备制造", "name": "交通安全、管制及类似专用设备制造(C3596)", "isParent": false, "parentcode": "C359"}, {"id": "C3597", "pId": "C359", "singlehyname": "水资源专用机械制造", "name": "水资源专用机械制造(C3597)", "isParent": false, "parentcode": "C359"}, {"id": "C3599", "pId": "C359", "singlehyname": "其他专用设备制造", "name": "其他专用设备制造(C3599)", "isParent": false, "parentcode": "C359"}, {"id": "C36", "pId": "C", "singlehyname": "汽车制造业", "name": "汽车制造业(C36)", "isParent": true, "parentcode": "C"}, {"id": "C361", "pId": "C36", "singlehyname": "汽车整车制造", "name": "汽车整车制造(C361)", "isParent": true, "parentcode": "C36"}, {"id": "C3611", "pId": "C361", "singlehyname": "汽柴油车整车制造", "name": "汽柴油车整车制造(C3611)", "isParent": false, "parentcode": "C361"}, {"id": "C3612", "pId": "C361", "singlehyname": "新能源车整车制造", "name": "新能源车整车制造(C3612)", "isParent": false, "parentcode": "C361"}, {"id": "C362", "pId": "C36", "singlehyname": "汽车用发动机制造", "name": "汽车用发动机制造(C362)", "isParent": false, "parentcode": "C36"}, {"id": "C363", "pId": "C36", "singlehyname": "改装汽车制造", "name": "改装汽车制造(C363)", "isParent": false, "parentcode": "C36"}, {"id": "C364", "pId": "C36", "singlehyname": "低速汽车制造", "name": "低速汽车制造(C364)", "isParent": false, "parentcode": "C36"}, {"id": "C365", "pId": "C36", "singlehyname": "电车制造", "name": "电车制造(C365)", "isParent": false, "parentcode": "C36"}, {"id": "C366", "pId": "C36", "singlehyname": "汽车车身、挂车制造", "name": "汽车车身、挂车制造(C366)", "isParent": false, "parentcode": "C36"}, {"id": "C367", "pId": "C36", "singlehyname": "汽车零部件及配件制造", "name": "汽车零部件及配件制造(C367)", "isParent": false, "parentcode": "C36"}, {"id": "C37", "pId": "C", "singlehyname": "铁路、船舶、航空航天和其他运输设备制造业", "name": "铁路、船舶、航空航天和其他运输设备制造业(C37)", "isParent": true, "parentcode": "C"}, {"id": "C371", "pId": "C37", "singlehyname": "铁路运输设备制造", "name": "铁路运输设备制造(C371)", "isParent": true, "parentcode": "C37"}, {"id": "C3711", "pId": "C371", "singlehyname": "高铁车组制造", "name": "高铁车组制造(C3711)", "isParent": false, "parentcode": "C371"}, {"id": "C3712", "pId": "C371", "singlehyname": "铁路机车车辆制造", "name": "铁路机车车辆制造(C3712)", "isParent": false, "parentcode": "C371"}, {"id": "C3713", "pId": "C371", "singlehyname": "窄轨机车车辆制造", "name": "窄轨机车车辆制造(C3713)", "isParent": false, "parentcode": "C371"}, {"id": "C3714", "pId": "C371", "singlehyname": "高铁设备、配件制造", "name": "高铁设备、配件制造(C3714)", "isParent": false, "parentcode": "C371"}, {"id": "C3715", "pId": "C371", "singlehyname": "铁路机车车辆配件制造", "name": "铁路机车车辆配件制造(C3715)", "isParent": false, "parentcode": "C371"}, {"id": "C3716", "pId": "C371", "singlehyname": "铁路专用设备及器材、配件制造", "name": "铁路专用设备及器材、配件制造(C3716)", "isParent": false, "parentcode": "C371"}, {"id": "C3719", "pId": "C371", "singlehyname": "其他铁路运输设备制造", "name": "其他铁路运输设备制造(C3719)", "isParent": false, "parentcode": "C371"}, {"id": "C372", "pId": "C37", "singlehyname": "城市轨道交通设备制造", "name": "城市轨道交通设备制造(C372)", "isParent": false, "parentcode": "C37"}, {"id": "C373", "pId": "C37", "singlehyname": "船舶及相关装置制造", "name": "船舶及相关装置制造(C373)", "isParent": true, "parentcode": "C37"}, {"id": "C3731", "pId": "C373", "singlehyname": "金属船舶制造", "name": "金属船舶制造(C3731)", "isParent": false, "parentcode": "C373"}, {"id": "C3732", "pId": "C373", "singlehyname": "非金属船舶制造", "name": "非金属船舶制造(C3732)", "isParent": false, "parentcode": "C373"}, {"id": "C3733", "pId": "C373", "singlehyname": "娱乐船和运动船制造", "name": "娱乐船和运动船制造(C3733)", "isParent": false, "parentcode": "C373"}, {"id": "C3734", "pId": "C373", "singlehyname": "船用配套设备制造", "name": "船用配套设备制造(C3734)", "isParent": false, "parentcode": "C373"}, {"id": "C3735", "pId": "C373", "singlehyname": "船舶改装", "name": "船舶改装(C3735)", "isParent": false, "parentcode": "C373"}, {"id": "C3736", "pId": "C373", "singlehyname": "船舶拆除", "name": "船舶拆除(C3736)", "isParent": false, "parentcode": "C373"}, {"id": "C3737", "pId": "C373", "singlehyname": "海洋工程装备制造", "name": "海洋工程装备制造(C3737)", "isParent": false, "parentcode": "C373"}, {"id": "C3739", "pId": "C373", "singlehyname": "航标器材及其他相关装置制造", "name": "航标器材及其他相关装置制造(C3739)", "isParent": false, "parentcode": "C373"}, {"id": "C374", "pId": "C37", "singlehyname": "航空、航天器及设备制造", "name": "航空、航天器及设备制造(C374)", "isParent": true, "parentcode": "C37"}, {"id": "C3741", "pId": "C374", "singlehyname": "飞机制造", "name": "飞机制造(C3741)", "isParent": false, "parentcode": "C374"}, {"id": "C3742", "pId": "C374", "singlehyname": "航天器及运载火箭制造", "name": "航天器及运载火箭制造(C3742)", "isParent": false, "parentcode": "C374"}, {"id": "C3743", "pId": "C374", "singlehyname": "航天相关设备制造", "name": "航天相关设备制造(C3743)", "isParent": false, "parentcode": "C374"}, {"id": "C3744", "pId": "C374", "singlehyname": "航空相关设备制造", "name": "航空相关设备制造(C3744)", "isParent": false, "parentcode": "C374"}, {"id": "C3749", "pId": "C374", "singlehyname": "其他航空航天器制造", "name": "其他航空航天器制造(C3749)", "isParent": false, "parentcode": "C374"}, {"id": "C375", "pId": "C37", "singlehyname": "摩托车制造", "name": "摩托车制造(C375)", "isParent": true, "parentcode": "C37"}, {"id": "C3751", "pId": "C375", "singlehyname": "摩托车整车制造", "name": "摩托车整车制造(C3751)", "isParent": false, "parentcode": "C375"}, {"id": "C3752", "pId": "C375", "singlehyname": "摩托车零部件及配件制造", "name": "摩托车零部件及配件制造(C3752)", "isParent": false, "parentcode": "C375"}, {"id": "C376", "pId": "C37", "singlehyname": "自行车和残疾人座车制造", "name": "自行车和残疾人座车制造(C376)", "isParent": true, "parentcode": "C37"}, {"id": "C3761", "pId": "C376", "singlehyname": "自行车制造", "name": "自行车制造(C3761)", "isParent": false, "parentcode": "C376"}, {"id": "C3762", "pId": "C376", "singlehyname": "残疾人座车制造", "name": "残疾人座车制造(C3762)", "isParent": false, "parentcode": "C376"}, {"id": "C377", "pId": "C37", "singlehyname": "助动车制造", "name": "助动车制造(C377)", "isParent": false, "parentcode": "C37"}, {"id": "C378", "pId": "C37", "singlehyname": "非公路休闲车及零配件制造", "name": "非公路休闲车及零配件制造(C378)", "isParent": false, "parentcode": "C37"}, {"id": "C379", "pId": "C37", "singlehyname": "潜水救捞及其他未列明运输设备制造", "name": "潜水救捞及其他未列明运输设备制造(C379)", "isParent": true, "parentcode": "C37"}, {"id": "C3791", "pId": "C379", "singlehyname": "潜水装备制造", "name": "潜水装备制造(C3791)", "isParent": false, "parentcode": "C379"}, {"id": "C3792", "pId": "C379", "singlehyname": "水下救捞装备制造", "name": "水下救捞装备制造(C3792)", "isParent": false, "parentcode": "C379"}, {"id": "C3799", "pId": "C379", "singlehyname": "其他未列明运输设备制造", "name": "其他未列明运输设备制造(C3799)", "isParent": false, "parentcode": "C379"}, {"id": "C38", "pId": "C", "singlehyname": "电气机械和器材制造业", "name": "电气机械和器材制造业(C38)", "isParent": true, "parentcode": "C"}, {"id": "C381", "pId": "C38", "singlehyname": "电机制造", "name": "电机制造(C381)", "isParent": true, "parentcode": "C38"}, {"id": "C3811", "pId": "C381", "singlehyname": "发电机及发电机组制造", "name": "发电机及发电机组制造(C3811)", "isParent": false, "parentcode": "C381"}, {"id": "C3812", "pId": "C381", "singlehyname": "电动机制造", "name": "电动机制造(C3812)", "isParent": false, "parentcode": "C381"}, {"id": "C3813", "pId": "C381", "singlehyname": "微特电机及组件制造", "name": "微特电机及组件制造(C3813)", "isParent": false, "parentcode": "C381"}, {"id": "C3819", "pId": "C381", "singlehyname": "其他电机制造", "name": "其他电机制造(C3819)", "isParent": false, "parentcode": "C381"}, {"id": "C382", "pId": "C38", "singlehyname": "输配电及控制设备制造", "name": "输配电及控制设备制造(C382)", "isParent": true, "parentcode": "C38"}, {"id": "C3821", "pId": "C382", "singlehyname": "变压器、整流器和电感器制造", "name": "变压器、整流器和电感器制造(C3821)", "isParent": false, "parentcode": "C382"}, {"id": "C3822", "pId": "C382", "singlehyname": "电容器及其配套设备制造", "name": "电容器及其配套设备制造(C3822)", "isParent": false, "parentcode": "C382"}, {"id": "C3823", "pId": "C382", "singlehyname": "配电开关控制设备制造", "name": "配电开关控制设备制造(C3823)", "isParent": false, "parentcode": "C382"}, {"id": "C3824", "pId": "C382", "singlehyname": "电力电子元器件制造", "name": "电力电子元器件制造(C3824)", "isParent": false, "parentcode": "C382"}, {"id": "C3825", "pId": "C382", "singlehyname": "光伏设备及元器件制造", "name": "光伏设备及元器件制造(C3825)", "isParent": false, "parentcode": "C382"}, {"id": "C3829", "pId": "C382", "singlehyname": "其他输配电及控制设备制造", "name": "其他输配电及控制设备制造(C3829)", "isParent": false, "parentcode": "C382"}, {"id": "C383", "pId": "C38", "singlehyname": "电线、电缆、光缆及电工器材制造", "name": "电线、电缆、光缆及电工器材制造(C383)", "isParent": true, "parentcode": "C38"}, {"id": "C3831", "pId": "C383", "singlehyname": "电线、电缆制造", "name": "电线、电缆制造(C3831)", "isParent": false, "parentcode": "C383"}, {"id": "C3832", "pId": "C383", "singlehyname": "光纤制造", "name": "光纤制造(C3832)", "isParent": false, "parentcode": "C383"}, {"id": "C3833", "pId": "C383", "singlehyname": "光缆制造", "name": "光缆制造(C3833)", "isParent": false, "parentcode": "C383"}, {"id": "C3834", "pId": "C383", "singlehyname": "绝缘制品制造", "name": "绝缘制品制造(C3834)", "isParent": false, "parentcode": "C383"}, {"id": "C3839", "pId": "C383", "singlehyname": "其他电工器材制造", "name": "其他电工器材制造(C3839)", "isParent": false, "parentcode": "C383"}, {"id": "C384", "pId": "C38", "singlehyname": "电池制造", "name": "电池制造(C384)", "isParent": true, "parentcode": "C38"}, {"id": "C3841", "pId": "C384", "singlehyname": "锂离子电池制造", "name": "锂离子电池制造(C3841)", "isParent": false, "parentcode": "C384"}, {"id": "C3842", "pId": "C384", "singlehyname": "镍氢电池制造", "name": "镍氢电池制造(C3842)", "isParent": false, "parentcode": "C384"}, {"id": "C3843", "pId": "C384", "singlehyname": "铅蓄电池制造", "name": "铅蓄电池制造(C3843)", "isParent": false, "parentcode": "C384"}, {"id": "C3844", "pId": "C384", "singlehyname": "锌锰电池制造", "name": "锌锰电池制造(C3844)", "isParent": false, "parentcode": "C384"}, {"id": "C3849", "pId": "C384", "singlehyname": "其他电池制造", "name": "其他电池制造(C3849)", "isParent": false, "parentcode": "C384"}, {"id": "C385", "pId": "C38", "singlehyname": "家用电力器具制造", "name": "家用电力器具制造(C385)", "isParent": true, "parentcode": "C38"}, {"id": "C3851", "pId": "C385", "singlehyname": "家用制冷电器具制造", "name": "家用制冷电器具制造(C3851)", "isParent": false, "parentcode": "C385"}, {"id": "C3852", "pId": "C385", "singlehyname": "家用空气调节器制造", "name": "家用空气调节器制造(C3852)", "isParent": false, "parentcode": "C385"}, {"id": "C3853", "pId": "C385", "singlehyname": "家用通风电器具制造", "name": "家用通风电器具制造(C3853)", "isParent": false, "parentcode": "C385"}, {"id": "C3854", "pId": "C385", "singlehyname": "家用厨房电器具制造", "name": "家用厨房电器具制造(C3854)", "isParent": false, "parentcode": "C385"}, {"id": "C3855", "pId": "C385", "singlehyname": "家用清洁卫生电器具制造", "name": "家用清洁卫生电器具制造(C3855)", "isParent": false, "parentcode": "C385"}, {"id": "C3856", "pId": "C385", "singlehyname": "家用美容、保健护理电器具制造", "name": "家用美容、保健护理电器具制造(C3856)", "isParent": false, "parentcode": "C385"}, {"id": "C3857", "pId": "C385", "singlehyname": "家用电力器具专用配件制造", "name": "家用电力器具专用配件制造(C3857)", "isParent": false, "parentcode": "C385"}, {"id": "C3859", "pId": "C385", "singlehyname": "其他家用电力器具制造", "name": "其他家用电力器具制造(C3859)", "isParent": false, "parentcode": "C385"}, {"id": "C386", "pId": "C38", "singlehyname": "非电力家用器具制造", "name": "非电力家用器具制造(C386)", "isParent": true, "parentcode": "C38"}, {"id": "C3861", "pId": "C386", "singlehyname": "燃气及类似能源家用器具制造", "name": "燃气及类似能源家用器具制造(C3861)", "isParent": false, "parentcode": "C386"}, {"id": "C3862", "pId": "C386", "singlehyname": "太阳能器具制造", "name": "太阳能器具制造(C3862)", "isParent": false, "parentcode": "C386"}, {"id": "C3869", "pId": "C386", "singlehyname": "其他非电力家用器具制造", "name": "其他非电力家用器具制造(C3869)", "isParent": false, "parentcode": "C386"}, {"id": "C387", "pId": "C38", "singlehyname": "照明器具制造", "name": "照明器具制造(C387)", "isParent": true, "parentcode": "C38"}, {"id": "C3871", "pId": "C387", "singlehyname": "电光源制造", "name": "电光源制造(C3871)", "isParent": false, "parentcode": "C387"}, {"id": "C3872", "pId": "C387", "singlehyname": "照明灯具制造", "name": "照明灯具制造(C3872)", "isParent": false, "parentcode": "C387"}, {"id": "C3873", "pId": "C387", "singlehyname": "舞台及场地用灯制造", "name": "舞台及场地用灯制造(C3873)", "isParent": false, "parentcode": "C387"}, {"id": "C3874", "pId": "C387", "singlehyname": "智能照明器具制造", "name": "智能照明器具制造(C3874)", "isParent": false, "parentcode": "C387"}, {"id": "C3879", "pId": "C387", "singlehyname": "灯用电器附件及其他照明器具制造", "name": "灯用电器附件及其他照明器具制造(C3879)", "isParent": false, "parentcode": "C387"}, {"id": "C389", "pId": "C38", "singlehyname": "其他电气机械及器材制造", "name": "其他电气机械及器材制造(C389)", "isParent": true, "parentcode": "C38"}, {"id": "C3891", "pId": "C389", "singlehyname": "电气信号设备装置制造", "name": "电气信号设备装置制造(C3891)", "isParent": false, "parentcode": "C389"}, {"id": "C3899", "pId": "C389", "singlehyname": "其他未列明电气机械及器材制造", "name": "其他未列明电气机械及器材制造(C3899)", "isParent": false, "parentcode": "C389"}, {"id": "C39", "pId": "C", "singlehyname": "计算机、通信和其他电子设备制造业", "name": "计算机、通信和其他电子设备制造业(C39)", "isParent": true, "parentcode": "C"}, {"id": "C391", "pId": "C39", "singlehyname": "计算机制造", "name": "计算机制造(C391)", "isParent": true, "parentcode": "C39"}, {"id": "C3911", "pId": "C391", "singlehyname": "计算机整机制造", "name": "计算机整机制造(C3911)", "isParent": false, "parentcode": "C391"}, {"id": "C3912", "pId": "C391", "singlehyname": "计算机零部件制造", "name": "计算机零部件制造(C3912)", "isParent": false, "parentcode": "C391"}, {"id": "C3913", "pId": "C391", "singlehyname": "计算机外围设备制造", "name": "计算机外围设备制造(C3913)", "isParent": false, "parentcode": "C391"}, {"id": "C3914", "pId": "C391", "singlehyname": "工业控制计算机及系统制造", "name": "工业控制计算机及系统制造(C3914)", "isParent": false, "parentcode": "C391"}, {"id": "C3915", "pId": "C391", "singlehyname": "信息安全设备制造", "name": "信息安全设备制造(C3915)", "isParent": false, "parentcode": "C391"}, {"id": "C3919", "pId": "C391", "singlehyname": "其他计算机制造", "name": "其他计算机制造(C3919)", "isParent": false, "parentcode": "C391"}, {"id": "C392", "pId": "C39", "singlehyname": "通信设备制造", "name": "通信设备制造(C392)", "isParent": true, "parentcode": "C39"}, {"id": "C3921", "pId": "C392", "singlehyname": "通信系统设备制造", "name": "通信系统设备制造(C3921)", "isParent": false, "parentcode": "C392"}, {"id": "C3922", "pId": "C392", "singlehyname": "通信终端设备制造", "name": "通信终端设备制造(C3922)", "isParent": false, "parentcode": "C392"}, {"id": "C393", "pId": "C39", "singlehyname": "广播电视设备制造", "name": "广播电视设备制造(C393)", "isParent": true, "parentcode": "C39"}, {"id": "C3931", "pId": "C393", "singlehyname": "广播电视节目制作及发射设备制造", "name": "广播电视节目制作及发射设备制造(C3931)", "isParent": false, "parentcode": "C393"}, {"id": "C3932", "pId": "C393", "singlehyname": "广播电视接收设备制造", "name": "广播电视接收设备制造(C3932)", "isParent": false, "parentcode": "C393"}, {"id": "C3933", "pId": "C393", "singlehyname": "广播电视专用配件制造", "name": "广播电视专用配件制造(C3933)", "isParent": false, "parentcode": "C393"}, {"id": "C3934", "pId": "C393", "singlehyname": "专业音响设备制造", "name": "专业音响设备制造(C3934)", "isParent": false, "parentcode": "C393"}, {"id": "C3939", "pId": "C393", "singlehyname": "应用电视设备及其他广播电视设备制造", "name": "应用电视设备及其他广播电视设备制造(C3939)", "isParent": false, "parentcode": "C393"}, {"id": "C394", "pId": "C39", "singlehyname": "雷达及配套设备制造", "name": "雷达及配套设备制造(C394)", "isParent": false, "parentcode": "C39"}, {"id": "C395", "pId": "C39", "singlehyname": "非专业视听设备制造", "name": "非专业视听设备制造(C395)", "isParent": true, "parentcode": "C39"}, {"id": "C3951", "pId": "C395", "singlehyname": "电视机制造", "name": "电视机制造(C3951)", "isParent": false, "parentcode": "C395"}, {"id": "C3952", "pId": "C395", "singlehyname": "音响设备制造", "name": "音响设备制造(C3952)", "isParent": false, "parentcode": "C395"}, {"id": "C3953", "pId": "C395", "singlehyname": "影视录放设备制造", "name": "影视录放设备制造(C3953)", "isParent": false, "parentcode": "C395"}, {"id": "C396", "pId": "C39", "singlehyname": "智能消费设备制造", "name": "智能消费设备制造(C396)", "isParent": true, "parentcode": "C39"}, {"id": "C3961", "pId": "C396", "singlehyname": "可穿戴智能设备制造", "name": "可穿戴智能设备制造(C3961)", "isParent": false, "parentcode": "C396"}, {"id": "C3962", "pId": "C396", "singlehyname": "智能车载设备制造", "name": "智能车载设备制造(C3962)", "isParent": false, "parentcode": "C396"}, {"id": "C3963", "pId": "C396", "singlehyname": "智能无人飞行器制造", "name": "智能无人飞行器制造(C3963)", "isParent": false, "parentcode": "C396"}, {"id": "C3964", "pId": "C396", "singlehyname": "服务消费机器人制造", "name": "服务消费机器人制造(C3964)", "isParent": false, "parentcode": "C396"}, {"id": "C3969", "pId": "C396", "singlehyname": "其他智能消费设备制造", "name": "其他智能消费设备制造(C3969)", "isParent": false, "parentcode": "C396"}, {"id": "C397", "pId": "C39", "singlehyname": "电子器件制造", "name": "电子器件制造(C397)", "isParent": true, "parentcode": "C39"}, {"id": "C3971", "pId": "C397", "singlehyname": "电子真空器件制造", "name": "电子真空器件制造(C3971)", "isParent": false, "parentcode": "C397"}, {"id": "C3972", "pId": "C397", "singlehyname": "半导体分立器件制造", "name": "半导体分立器件制造(C3972)", "isParent": false, "parentcode": "C397"}, {"id": "C3973", "pId": "C397", "singlehyname": "集成电路制造", "name": "集成电路制造(C3973)", "isParent": false, "parentcode": "C397"}, {"id": "C3974", "pId": "C397", "singlehyname": "显示器件制造", "name": "显示器件制造(C3974)", "isParent": false, "parentcode": "C397"}, {"id": "C3975", "pId": "C397", "singlehyname": "半导体照明器件制造", "name": "半导体照明器件制造(C3975)", "isParent": false, "parentcode": "C397"}, {"id": "C3976", "pId": "C397", "singlehyname": "光电子器件制造", "name": "光电子器件制造(C3976)", "isParent": false, "parentcode": "C397"}, {"id": "C3979", "pId": "C397", "singlehyname": "其他电子器件制造", "name": "其他电子器件制造(C3979)", "isParent": false, "parentcode": "C397"}, {"id": "C398", "pId": "C39", "singlehyname": "电子元件及电子专用材料制造", "name": "电子元件及电子专用材料制造(C398)", "isParent": true, "parentcode": "C39"}, {"id": "C3981", "pId": "C398", "singlehyname": "电阻电容电感元件制造", "name": "电阻电容电感元件制造(C3981)", "isParent": false, "parentcode": "C398"}, {"id": "C3982", "pId": "C398", "singlehyname": "电子电路制造", "name": "电子电路制造(C3982)", "isParent": false, "parentcode": "C398"}, {"id": "C3983", "pId": "C398", "singlehyname": "敏感元件及传感器制造", "name": "敏感元件及传感器制造(C3983)", "isParent": false, "parentcode": "C398"}, {"id": "C3984", "pId": "C398", "singlehyname": "电声器件及零件制造", "name": "电声器件及零件制造(C3984)", "isParent": false, "parentcode": "C398"}, {"id": "C3985", "pId": "C398", "singlehyname": "电子专用材料制造", "name": "电子专用材料制造(C3985)", "isParent": false, "parentcode": "C398"}, {"id": "C3989", "pId": "C398", "singlehyname": "其他电子元件制造", "name": "其他电子元件制造(C3989)", "isParent": false, "parentcode": "C398"}, {"id": "C399", "pId": "C39", "singlehyname": "其他电子设备制造", "name": "其他电子设备制造(C399)", "isParent": false, "parentcode": "C39"}, {"id": "C40", "pId": "C", "singlehyname": "仪器仪表制造业", "name": "仪器仪表制造业(C40)", "isParent": true, "parentcode": "C"}, {"id": "C401", "pId": "C40", "singlehyname": "通用仪器仪表制造", "name": "通用仪器仪表制造(C401)", "isParent": true, "parentcode": "C40"}, {"id": "C4011", "pId": "C401", "singlehyname": "工业自动控制系统装置制造", "name": "工业自动控制系统装置制造(C4011)", "isParent": false, "parentcode": "C401"}, {"id": "C4012", "pId": "C401", "singlehyname": "电工仪器仪表制造", "name": "电工仪器仪表制造(C4012)", "isParent": false, "parentcode": "C401"}, {"id": "C4013", "pId": "C401", "singlehyname": "绘图、计算及测量仪器制造", "name": "绘图、计算及测量仪器制造(C4013)", "isParent": false, "parentcode": "C401"}, {"id": "C4014", "pId": "C401", "singlehyname": "实验分析仪器制造", "name": "实验分析仪器制造(C4014)", "isParent": false, "parentcode": "C401"}, {"id": "C4015", "pId": "C401", "singlehyname": "试验机制造", "name": "试验机制造(C4015)", "isParent": false, "parentcode": "C401"}, {"id": "C4016", "pId": "C401", "singlehyname": "供应用仪器仪表制造", "name": "供应用仪器仪表制造(C4016)", "isParent": false, "parentcode": "C401"}, {"id": "C4019", "pId": "C401", "singlehyname": "其他通用仪器制造", "name": "其他通用仪器制造(C4019)", "isParent": false, "parentcode": "C401"}, {"id": "C402", "pId": "C40", "singlehyname": "专用仪器仪表制造", "name": "专用仪器仪表制造(C402)", "isParent": true, "parentcode": "C40"}, {"id": "C4021", "pId": "C402", "singlehyname": "环境监测专用仪器仪表制造", "name": "环境监测专用仪器仪表制造(C4021)", "isParent": false, "parentcode": "C402"}, {"id": "C4022", "pId": "C402", "singlehyname": "运输设备及生产用计数仪表制造", "name": "运输设备及生产用计数仪表制造(C4022)", "isParent": false, "parentcode": "C402"}, {"id": "C4023", "pId": "C402", "singlehyname": "导航、测绘、气象及海洋专用仪器制造", "name": "导航、测绘、气象及海洋专用仪器制造(C4023)", "isParent": false, "parentcode": "C402"}, {"id": "C4024", "pId": "C402", "singlehyname": "农林牧渔专用仪器仪表制造", "name": "农林牧渔专用仪器仪表制造(C4024)", "isParent": false, "parentcode": "C402"}, {"id": "C4025", "pId": "C402", "singlehyname": "地质勘探和地震专用仪器制造", "name": "地质勘探和地震专用仪器制造(C4025)", "isParent": false, "parentcode": "C402"}, {"id": "C4026", "pId": "C402", "singlehyname": "教学专用仪器制造", "name": "教学专用仪器制造(C4026)", "isParent": false, "parentcode": "C402"}, {"id": "C4027", "pId": "C402", "singlehyname": "核子及核辐射测量仪器制造", "name": "核子及核辐射测量仪器制造(C4027)", "isParent": false, "parentcode": "C402"}, {"id": "C4028", "pId": "C402", "singlehyname": "电子测量仪器制造", "name": "电子测量仪器制造(C4028)", "isParent": false, "parentcode": "C402"}, {"id": "C4029", "pId": "C402", "singlehyname": "其他专用仪器制造", "name": "其他专用仪器制造(C4029)", "isParent": false, "parentcode": "C402"}, {"id": "C403", "pId": "C40", "singlehyname": "钟表与计时仪器制造", "name": "钟表与计时仪器制造(C403)", "isParent": false, "parentcode": "C40"}, {"id": "C404", "pId": "C40", "singlehyname": "光学仪器制造", "name": "光学仪器制造(C404)", "isParent": false, "parentcode": "C40"}, {"id": "C405", "pId": "C40", "singlehyname": "衡器制造", "name": "衡器制造(C405)", "isParent": false, "parentcode": "C40"}, {"id": "C409", "pId": "C40", "singlehyname": "其他仪器仪表制造业", "name": "其他仪器仪表制造业(C409)", "isParent": false, "parentcode": "C40"}, {"id": "C41", "pId": "C", "singlehyname": "其他制造业", "name": "其他制造业(C41)", "isParent": true, "parentcode": "C"}, {"id": "C411", "pId": "C41", "singlehyname": "日用杂品制造", "name": "日用杂品制造(C411)", "isParent": true, "parentcode": "C41"}, {"id": "C4111", "pId": "C411", "singlehyname": "鬃毛加工、制刷及清扫工具制造", "name": "鬃毛加工、制刷及清扫工具制造(C4111)", "isParent": false, "parentcode": "C411"}, {"id": "C4119", "pId": "C411", "singlehyname": "其他日用杂品制造", "name": "其他日用杂品制造(C4119)", "isParent": false, "parentcode": "C411"}, {"id": "C412", "pId": "C41", "singlehyname": "核辐射加工", "name": "核辐射加工(C412)", "isParent": false, "parentcode": "C41"}, {"id": "C419", "pId": "C41", "singlehyname": "其他未列明制造业", "name": "其他未列明制造业(C419)", "isParent": false, "parentcode": "C41"}, {"id": "C42", "pId": "C", "singlehyname": "废弃资源综合利用业", "name": "废弃资源综合利用业(C42)", "isParent": true, "parentcode": "C"}, {"id": "C421", "pId": "C42", "singlehyname": "金属废料和碎屑加工处理", "name": "金属废料和碎屑加工处理(C421)", "isParent": false, "parentcode": "C42"}, {"id": "C422", "pId": "C42", "singlehyname": "非金属废料和碎屑加工处理", "name": "非金属废料和碎屑加工处理(C422)", "isParent": false, "parentcode": "C42"}, {"id": "C43", "pId": "C", "singlehyname": "金属制品、机械和设备修理业", "name": "金属制品、机械和设备修理业(C43)", "isParent": true, "parentcode": "C"}, {"id": "C431", "pId": "C43", "singlehyname": "金属制品修理", "name": "金属制品修理(C431)", "isParent": false, "parentcode": "C43"}, {"id": "C432", "pId": "C43", "singlehyname": "通用设备修理", "name": "通用设备修理(C432)", "isParent": false, "parentcode": "C43"}, {"id": "C433", "pId": "C43", "singlehyname": "专用设备修理", "name": "专用设备修理(C433)", "isParent": false, "parentcode": "C43"}, {"id": "C434", "pId": "C43", "singlehyname": "铁路、船舶、航空航天等运输设备修理", "name": "铁路、船舶、航空航天等运输设备修理(C434)", "isParent": true, "parentcode": "C43"}, {"id": "C4341", "pId": "C434", "singlehyname": "铁路运输设备修理", "name": "铁路运输设备修理(C4341)", "isParent": false, "parentcode": "C434"}, {"id": "C4342", "pId": "C434", "singlehyname": "船舶修理", "name": "船舶修理(C4342)", "isParent": false, "parentcode": "C434"}, {"id": "C4343", "pId": "C434", "singlehyname": "航空航天器修理", "name": "航空航天器修理(C4343)", "isParent": false, "parentcode": "C434"}, {"id": "C4349", "pId": "C434", "singlehyname": "其他运输设备修理", "name": "其他运输设备修理(C4349)", "isParent": false, "parentcode": "C434"}, {"id": "C435", "pId": "C43", "singlehyname": "电气设备修理", "name": "电气设备修理(C435)", "isParent": false, "parentcode": "C43"}, {"id": "C436", "pId": "C43", "singlehyname": "仪器仪表修理", "name": "仪器仪表修理(C436)", "isParent": false, "parentcode": "C43"}, {"id": "C439", "pId": "C43", "singlehyname": "其他机械和设备修理业", "name": "其他机械和设备修理业(C439)", "isParent": false, "parentcode": "C43"}, {"id": "D", "pId": "", "singlehyname": "电力、热力、燃气及水生产和供应业", "name": "电力、热力、燃气及水生产和供应业(D)", "isParent": true, "parentcode": ""}, {"id": "D44", "pId": "D", "singlehyname": "电力、热力生产和供应业", "name": "电力、热力生产和供应业(D44)", "isParent": true, "parentcode": "D"}, {"id": "D441", "pId": "D44", "singlehyname": "电力生产", "name": "电力生产(D441)", "isParent": true, "parentcode": "D44"}, {"id": "D4411", "pId": "D441", "singlehyname": "火力发电", "name": "火力发电(D4411)", "isParent": false, "parentcode": "D441"}, {"id": "D4412", "pId": "D441", "singlehyname": "热电联产", "name": "热电联产(D4412)", "isParent": false, "parentcode": "D441"}, {"id": "D4413", "pId": "D441", "singlehyname": "水力发电", "name": "水力发电(D4413)", "isParent": false, "parentcode": "D441"}, {"id": "D4414", "pId": "D441", "singlehyname": "核力发电", "name": "核力发电(D4414)", "isParent": false, "parentcode": "D441"}, {"id": "D4415", "pId": "D441", "singlehyname": "风力发电", "name": "风力发电(D4415)", "isParent": false, "parentcode": "D441"}, {"id": "D4416", "pId": "D441", "singlehyname": "太阳能发电", "name": "太阳能发电(D4416)", "isParent": false, "parentcode": "D441"}, {"id": "D4417", "pId": "D441", "singlehyname": "生物质能发电", "name": "生物质能发电(D4417)", "isParent": false, "parentcode": "D441"}, {"id": "D4417-1", "pId": "D441", "singlehyname": "生物质能发电-生活垃圾焚烧发电", "name": "生物质能发电-生活垃圾焚烧发电(D4417-1)", "isParent": false, "parentcode": "D441"}, {"id": "D4419", "pId": "D441", "singlehyname": "其他电力生产", "name": "其他电力生产(D4419)", "isParent": false, "parentcode": "D441"}, {"id": "D442", "pId": "D44", "singlehyname": "电力供应", "name": "电力供应(D442)", "isParent": false, "parentcode": "D44"}, {"id": "D443", "pId": "D44", "singlehyname": "热力生产和供应", "name": "热力生产和供应(D443)", "isParent": false, "parentcode": "D44"}, {"id": "D45", "pId": "D", "singlehyname": "燃气生产和供应业", "name": "燃气生产和供应业(D45)", "isParent": true, "parentcode": "D"}, {"id": "D451", "pId": "D45", "singlehyname": "燃气生产和供应业", "name": "燃气生产和供应业(D451)", "isParent": true, "parentcode": "D45"}, {"id": "D4511", "pId": "D451", "singlehyname": "天然气生产和供应业", "name": "天然气生产和供应业(D4511)", "isParent": false, "parentcode": "D451"}, {"id": "D4512", "pId": "D451", "singlehyname": "液化石油气生产和供应业", "name": "液化石油气生产和供应业(D4512)", "isParent": false, "parentcode": "D451"}, {"id": "D4513", "pId": "D451", "singlehyname": "煤气生产和供应业", "name": "煤气生产和供应业(D4513)", "isParent": false, "parentcode": "D451"}, {"id": "D452", "pId": "D45", "singlehyname": "生物质燃气生产和供应业", "name": "生物质燃气生产和供应业(D452)", "isParent": false, "parentcode": "D45"}, {"id": "D46", "pId": "D", "singlehyname": "水的生产和供应业", "name": "水的生产和供应业(D46)", "isParent": true, "parentcode": "D"}, {"id": "D461", "pId": "D46", "singlehyname": "自来水生产和供应", "name": "自来水生产和供应(D461)", "isParent": false, "parentcode": "D46"}, {"id": "D462", "pId": "D46", "singlehyname": "污水处理及其再生利用", "name": "污水处理及其再生利用(D462)", "isParent": false, "parentcode": "D46"}, {"id": "D463", "pId": "D46", "singlehyname": "海水淡化处理", "name": "海水淡化处理(D463)", "isParent": false, "parentcode": "D46"}, {"id": "D469", "pId": "D46", "singlehyname": "其他水的处理、利用与分配", "name": "其他水的处理、利用与分配(D469)", "isParent": false, "parentcode": "D46"}, {"id": "E", "pId": "", "singlehyname": "建筑业", "name": "建筑业(E)", "isParent": true, "parentcode": ""}, {"id": "E47", "pId": "E", "singlehyname": "房屋建筑业", "name": "房屋建筑业(E47)", "isParent": true, "parentcode": "E"}, {"id": "E4700", "pId": "E47", "singlehyname": "房屋建筑业", "name": "房屋建筑业(E4700)", "isParent": false, "parentcode": "E47"}, {"id": "E471", "pId": "E47", "singlehyname": "住宅房屋建筑", "name": "住宅房屋建筑(E471)", "isParent": false, "parentcode": "E47"}, {"id": "E472", "pId": "E47", "singlehyname": "体育场馆建筑", "name": "体育场馆建筑(E472)", "isParent": false, "parentcode": "E47"}, {"id": "E479", "pId": "E47", "singlehyname": "其他房屋建筑业", "name": "其他房屋建筑业(E479)", "isParent": false, "parentcode": "E47"}, {"id": "E48", "pId": "E", "singlehyname": "土木工程建筑业", "name": "土木工程建筑业(E48)", "isParent": true, "parentcode": "E"}, {"id": "E481", "pId": "E48", "singlehyname": "铁路、道路、隧道和桥梁工程建筑", "name": "铁路、道路、隧道和桥梁工程建筑(E481)", "isParent": true, "parentcode": "E48"}, {"id": "E4811", "pId": "E481", "singlehyname": "铁路工程建筑", "name": "铁路工程建筑(E4811)", "isParent": false, "parentcode": "E481"}, {"id": "E4812", "pId": "E481", "singlehyname": "公路工程建筑", "name": "公路工程建筑(E4812)", "isParent": false, "parentcode": "E481"}, {"id": "E4813", "pId": "E481", "singlehyname": "市政道路工程建筑", "name": "市政道路工程建筑(E4813)", "isParent": false, "parentcode": "E481"}, {"id": "E4814", "pId": "E481", "singlehyname": "城市轨道交通工程建筑", "name": "城市轨道交通工程建筑(E4814)", "isParent": false, "parentcode": "E481"}, {"id": "E4819", "pId": "E481", "singlehyname": "其他道路、隧道和桥梁工程建筑", "name": "其他道路、隧道和桥梁工程建筑(E4819)", "isParent": false, "parentcode": "E481"}, {"id": "E482", "pId": "E48", "singlehyname": "水利和水运工程建筑", "name": "水利和水运工程建筑(E482)", "isParent": true, "parentcode": "E48"}, {"id": "E4821", "pId": "E482", "singlehyname": "水源及供水设施工程建筑", "name": "水源及供水设施工程建筑(E4821)", "isParent": false, "parentcode": "E482"}, {"id": "E4822", "pId": "E482", "singlehyname": "河湖治理及防洪设施工程建筑", "name": "河湖治理及防洪设施工程建筑(E4822)", "isParent": false, "parentcode": "E482"}, {"id": "E4823", "pId": "E482", "singlehyname": "港口及航运设施工程建筑", "name": "港口及航运设施工程建筑(E4823)", "isParent": false, "parentcode": "E482"}, {"id": "E483", "pId": "E48", "singlehyname": "海洋工程建筑", "name": "海洋工程建筑(E483)", "isParent": true, "parentcode": "E48"}, {"id": "E4831", "pId": "E483", "singlehyname": "海洋油气资源开发利用工程建筑", "name": "海洋油气资源开发利用工程建筑(E4831)", "isParent": false, "parentcode": "E483"}, {"id": "E4832", "pId": "E483", "singlehyname": "海洋能源开发利用工程建筑", "name": "海洋能源开发利用工程建筑(E4832)", "isParent": false, "parentcode": "E483"}, {"id": "E4833", "pId": "E483", "singlehyname": "海底隧道工程建筑", "name": "海底隧道工程建筑(E4833)", "isParent": false, "parentcode": "E483"}, {"id": "E4834", "pId": "E483", "singlehyname": "海底设施铺设工程建筑", "name": "海底设施铺设工程建筑(E4834)", "isParent": false, "parentcode": "E483"}, {"id": "E4839", "pId": "E483", "singlehyname": "其他海洋工程建筑", "name": "其他海洋工程建筑(E4839)", "isParent": false, "parentcode": "E483"}, {"id": "E484", "pId": "E48", "singlehyname": "工矿工程建筑", "name": "工矿工程建筑(E484)", "isParent": false, "parentcode": "E48"}, {"id": "E485", "pId": "E48", "singlehyname": "架线和管道工程建筑", "name": "架线和管道工程建筑(E485)", "isParent": true, "parentcode": "E48"}, {"id": "E4851", "pId": "E485", "singlehyname": "架线及设备工程建筑", "name": "架线及设备工程建筑(E4851)", "isParent": false, "parentcode": "E485"}, {"id": "E4852", "pId": "E485", "singlehyname": "管道工程建筑", "name": "管道工程建筑(E4852)", "isParent": false, "parentcode": "E485"}, {"id": "E4853", "pId": "E485", "singlehyname": "地下综合管廊工程建筑", "name": "地下综合管廊工程建筑(E4853)", "isParent": false, "parentcode": "E485"}, {"id": "E486", "pId": "E48", "singlehyname": "节能环保工程施工", "name": "节能环保工程施工(E486)", "isParent": true, "parentcode": "E48"}, {"id": "E4861", "pId": "E486", "singlehyname": "节能工程施工", "name": "节能工程施工(E4861)", "isParent": false, "parentcode": "E486"}, {"id": "E4862", "pId": "E486", "singlehyname": "环保工程施工", "name": "环保工程施工(E4862)", "isParent": false, "parentcode": "E486"}, {"id": "E4863", "pId": "E486", "singlehyname": "生态保护工程施工", "name": "生态保护工程施工(E4863)", "isParent": false, "parentcode": "E486"}, {"id": "E487", "pId": "E48", "singlehyname": "电力工程施工", "name": "电力工程施工(E487)", "isParent": true, "parentcode": "E48"}, {"id": "E4871", "pId": "E487", "singlehyname": "火力发电工程施工", "name": "火力发电工程施工(E4871)", "isParent": false, "parentcode": "E487"}, {"id": "E4872", "pId": "E487", "singlehyname": "水力发电工程施工", "name": "水力发电工程施工(E4872)", "isParent": false, "parentcode": "E487"}, {"id": "E4873", "pId": "E487", "singlehyname": "核电工程施工", "name": "核电工程施工(E4873)", "isParent": false, "parentcode": "E487"}, {"id": "E4874", "pId": "E487", "singlehyname": "风能发电工程施工", "name": "风能发电工程施工(E4874)", "isParent": false, "parentcode": "E487"}, {"id": "E4875", "pId": "E487", "singlehyname": "太阳能发电工程施工", "name": "太阳能发电工程施工(E4875)", "isParent": false, "parentcode": "E487"}, {"id": "E4879", "pId": "E487", "singlehyname": "其他电力工程施工", "name": "其他电力工程施工(E4879)", "isParent": false, "parentcode": "E487"}, {"id": "E489", "pId": "E48", "singlehyname": "其他土木工程建筑", "name": "其他土木工程建筑(E489)", "isParent": true, "parentcode": "E48"}, {"id": "E4892", "pId": "E489", "singlehyname": "体育场地设施工程施工", "name": "体育场地设施工程施工(E4892)", "isParent": false, "parentcode": "E489"}, {"id": "E4893", "pId": "E489", "singlehyname": "游乐设施工程施工", "name": "游乐设施工程施工(E4893)", "isParent": false, "parentcode": "E489"}, {"id": "E4899", "pId": "E489", "singlehyname": "其他土木工程建筑施工", "name": "其他土木工程建筑施工(E4899)", "isParent": false, "parentcode": "E489"}, {"id": "E49", "pId": "E", "singlehyname": "建筑安装业", "name": "建筑安装业(E49)", "isParent": true, "parentcode": "E"}, {"id": "E491", "pId": "E49", "singlehyname": "电气安装", "name": "电气安装(E491)", "isParent": false, "parentcode": "E49"}, {"id": "E492", "pId": "E49", "singlehyname": "管道和设备安装", "name": "管道和设备安装(E492)", "isParent": false, "parentcode": "E49"}, {"id": "E499", "pId": "E49", "singlehyname": "其他建筑安装业", "name": "其他建筑安装业(E499)", "isParent": true, "parentcode": "E49"}, {"id": "E4991", "pId": "E499", "singlehyname": "体育场地设施安装", "name": "体育场地设施安装(E4991)", "isParent": false, "parentcode": "E499"}, {"id": "E4999", "pId": "E499", "singlehyname": "其他建筑安装", "name": "其他建筑安装(E4999)", "isParent": false, "parentcode": "E499"}, {"id": "E50", "pId": "E", "singlehyname": "建筑装饰、装修和其他建筑业", "name": "建筑装饰、装修和其他建筑业(E50)", "isParent": true, "parentcode": "E"}, {"id": "E501", "pId": "E50", "singlehyname": "建筑装饰和装修业", "name": "建筑装饰和装修业(E501)", "isParent": true, "parentcode": "E50"}, {"id": "E5011", "pId": "E501", "singlehyname": "公共建筑装饰和装修", "name": "公共建筑装饰和装修(E5011)", "isParent": false, "parentcode": "E501"}, {"id": "E5012", "pId": "E501", "singlehyname": "住宅装饰和装修", "name": "住宅装饰和装修(E5012)", "isParent": false, "parentcode": "E501"}, {"id": "E5013", "pId": "E501", "singlehyname": "建筑幕墙装饰和装修", "name": "建筑幕墙装饰和装修(E5013)", "isParent": false, "parentcode": "E501"}, {"id": "E502", "pId": "E50", "singlehyname": "建筑物拆除和场地准备活动", "name": "建筑物拆除和场地准备活动(E502)", "isParent": true, "parentcode": "E50"}, {"id": "E5021", "pId": "E502", "singlehyname": "建筑物拆除活动", "name": "建筑物拆除活动(E5021)", "isParent": false, "parentcode": "E502"}, {"id": "E5022", "pId": "E502", "singlehyname": "场地准备活动", "name": "场地准备活动(E5022)", "isParent": false, "parentcode": "E502"}, {"id": "E503", "pId": "E50", "singlehyname": "提供施工设备服务", "name": "提供施工设备服务(E503)", "isParent": false, "parentcode": "E50"}, {"id": "E509", "pId": "E50", "singlehyname": "其他未列明建筑业", "name": "其他未列明建筑业(E509)", "isParent": false, "parentcode": "E50"}, {"id": "F", "pId": "", "singlehyname": "批发和零售业", "name": "批发和零售业(F)", "isParent": true, "parentcode": ""}, {"id": "F51", "pId": "F", "singlehyname": "批发业", "name": "批发业(F51)", "isParent": true, "parentcode": "F"}, {"id": "F511", "pId": "F51", "singlehyname": "农、林、牧、渔产品批发", "name": "农、林、牧、渔产品批发(F511)", "isParent": true, "parentcode": "F51"}, {"id": "F5111", "pId": "F511", "singlehyname": "谷物、豆及薯类批发", "name": "谷物、豆及薯类批发(F5111)", "isParent": false, "parentcode": "F511"}, {"id": "F5112", "pId": "F511", "singlehyname": "种子批发", "name": "种子批发(F5112)", "isParent": false, "parentcode": "F511"}, {"id": "F5113", "pId": "F511", "singlehyname": "畜牧渔业饲料批发", "name": "畜牧渔业饲料批发(F5113)", "isParent": false, "parentcode": "F511"}, {"id": "F5114", "pId": "F511", "singlehyname": "棉、麻批发", "name": "棉、麻批发(F5114)", "isParent": false, "parentcode": "F511"}, {"id": "F5115", "pId": "F511", "singlehyname": "林业产品批发", "name": "林业产品批发(F5115)", "isParent": false, "parentcode": "F511"}, {"id": "F5116", "pId": "F511", "singlehyname": "牲畜批发", "name": "牲畜批发(F5116)", "isParent": false, "parentcode": "F511"}, {"id": "F5117", "pId": "F511", "singlehyname": "渔业产品批发", "name": "渔业产品批发(F5117)", "isParent": false, "parentcode": "F511"}, {"id": "F5119", "pId": "F511", "singlehyname": "其他农牧产品批发", "name": "其他农牧产品批发(F5119)", "isParent": false, "parentcode": "F511"}, {"id": "F512", "pId": "F51", "singlehyname": "食品、饮料及烟草制品批发", "name": "食品、饮料及烟草制品批发(F512)", "isParent": true, "parentcode": "F51"}, {"id": "F5121", "pId": "F512", "singlehyname": "米、面制品及食用油批发", "name": "米、面制品及食用油批发(F5121)", "isParent": false, "parentcode": "F512"}, {"id": "F5122", "pId": "F512", "singlehyname": "糕点、糖果及糖批发", "name": "糕点、糖果及糖批发(F5122)", "isParent": false, "parentcode": "F512"}, {"id": "F5123", "pId": "F512", "singlehyname": "果品、蔬菜批发", "name": "果品、蔬菜批发(F5123)", "isParent": false, "parentcode": "F512"}, {"id": "F5124", "pId": "F512", "singlehyname": "肉、禽、蛋、奶及水产品批发", "name": "肉、禽、蛋、奶及水产品批发(F5124)", "isParent": false, "parentcode": "F512"}, {"id": "F5125", "pId": "F512", "singlehyname": "盐及调味品批发", "name": "盐及调味品批发(F5125)", "isParent": false, "parentcode": "F512"}, {"id": "F5126", "pId": "F512", "singlehyname": "营养和保健品批发", "name": "营养和保健品批发(F5126)", "isParent": false, "parentcode": "F512"}, {"id": "F5127", "pId": "F512", "singlehyname": "酒、饮料及茶叶批发", "name": "酒、饮料及茶叶批发(F5127)", "isParent": false, "parentcode": "F512"}, {"id": "F5128", "pId": "F512", "singlehyname": "烟草制品批发", "name": "烟草制品批发(F5128)", "isParent": false, "parentcode": "F512"}, {"id": "F5129", "pId": "F512", "singlehyname": "其他食品批发", "name": "其他食品批发(F5129)", "isParent": false, "parentcode": "F512"}, {"id": "F513", "pId": "F51", "singlehyname": "纺织、服装及家庭用品批发", "name": "纺织、服装及家庭用品批发(F513)", "isParent": true, "parentcode": "F51"}, {"id": "F5131", "pId": "F513", "singlehyname": "纺织品、针织品及原料批发", "name": "纺织品、针织品及原料批发(F5131)", "isParent": false, "parentcode": "F513"}, {"id": "F5132", "pId": "F513", "singlehyname": "服装批发", "name": "服装批发(F5132)", "isParent": false, "parentcode": "F513"}, {"id": "F5133", "pId": "F513", "singlehyname": "鞋帽批发", "name": "鞋帽批发(F5133)", "isParent": false, "parentcode": "F513"}, {"id": "F5134", "pId": "F513", "singlehyname": "化妆品及卫生用品批发", "name": "化妆品及卫生用品批发(F5134)", "isParent": false, "parentcode": "F513"}, {"id": "F5135", "pId": "F513", "singlehyname": "厨具卫具及日用杂品批发", "name": "厨具卫具及日用杂品批发(F5135)", "isParent": false, "parentcode": "F513"}, {"id": "F5136", "pId": "F513", "singlehyname": "灯具、装饰物品批发", "name": "灯具、装饰物品批发(F5136)", "isParent": false, "parentcode": "F513"}, {"id": "F5137", "pId": "F513", "singlehyname": "家用视听设备批发", "name": "家用视听设备批发(F5137)", "isParent": false, "parentcode": "F513"}, {"id": "F5138", "pId": "F513", "singlehyname": "日用家电批发", "name": "日用家电批发(F5138)", "isParent": false, "parentcode": "F513"}, {"id": "F5139", "pId": "F513", "singlehyname": "其他家庭用品批发", "name": "其他家庭用品批发(F5139)", "isParent": false, "parentcode": "F513"}, {"id": "F514", "pId": "F51", "singlehyname": "文化、体育用品及器材批发", "name": "文化、体育用品及器材批发(F514)", "isParent": true, "parentcode": "F51"}, {"id": "F5141", "pId": "F514", "singlehyname": "文具用品批发", "name": "文具用品批发(F5141)", "isParent": false, "parentcode": "F514"}, {"id": "F5142", "pId": "F514", "singlehyname": "体育用品及器材批发", "name": "体育用品及器材批发(F5142)", "isParent": false, "parentcode": "F514"}, {"id": "F5143", "pId": "F514", "singlehyname": "图书批发", "name": "图书批发(F5143)", "isParent": false, "parentcode": "F514"}, {"id": "F5144", "pId": "F514", "singlehyname": "报刊批发", "name": "报刊批发(F5144)", "isParent": false, "parentcode": "F514"}, {"id": "F5145", "pId": "F514", "singlehyname": "音像制品、电子和数字出版物批发", "name": "音像制品、电子和数字出版物批发(F5145)", "isParent": false, "parentcode": "F514"}, {"id": "F5146", "pId": "F514", "singlehyname": "首饰、工艺品及收藏品批发", "name": "首饰、工艺品及收藏品批发(F5146)", "isParent": false, "parentcode": "F514"}, {"id": "F5147", "pId": "F514", "singlehyname": "乐器批发", "name": "乐器批发(F5147)", "isParent": false, "parentcode": "F514"}, {"id": "F5149", "pId": "F514", "singlehyname": "其他文化用品批发", "name": "其他文化用品批发(F5149)", "isParent": false, "parentcode": "F514"}, {"id": "F515", "pId": "F51", "singlehyname": "医药及医疗器材批发", "name": "医药及医疗器材批发(F515)", "isParent": true, "parentcode": "F51"}, {"id": "F5151", "pId": "F515", "singlehyname": "西药批发", "name": "西药批发(F5151)", "isParent": false, "parentcode": "F515"}, {"id": "F5152", "pId": "F515", "singlehyname": "中药批发", "name": "中药批发(F5152)", "isParent": false, "parentcode": "F515"}, {"id": "F5153", "pId": "F515", "singlehyname": "动物用药品批发", "name": "动物用药品批发(F5153)", "isParent": false, "parentcode": "F515"}, {"id": "F5154", "pId": "F515", "singlehyname": "医疗用品及器材批发", "name": "医疗用品及器材批发(F5154)", "isParent": false, "parentcode": "F515"}, {"id": "F516", "pId": "F51", "singlehyname": "矿产品、建材及化工产品批发", "name": "矿产品、建材及化工产品批发(F516)", "isParent": true, "parentcode": "F51"}, {"id": "F5161", "pId": "F516", "singlehyname": "煤炭及制品批发", "name": "煤炭及制品批发(F5161)", "isParent": false, "parentcode": "F516"}, {"id": "F5162", "pId": "F516", "singlehyname": "石油及制品批发", "name": "石油及制品批发(F5162)", "isParent": false, "parentcode": "F516"}, {"id": "F5163", "pId": "F516", "singlehyname": "非金属矿及制品批发", "name": "非金属矿及制品批发(F5163)", "isParent": false, "parentcode": "F516"}, {"id": "F5164", "pId": "F516", "singlehyname": "金属及金属矿批发", "name": "金属及金属矿批发(F5164)", "isParent": false, "parentcode": "F516"}, {"id": "F5165", "pId": "F516", "singlehyname": "建材批发", "name": "建材批发(F5165)", "isParent": false, "parentcode": "F516"}, {"id": "F5166", "pId": "F516", "singlehyname": "化肥批发", "name": "化肥批发(F5166)", "isParent": false, "parentcode": "F516"}, {"id": "F5167", "pId": "F516", "singlehyname": "农药批发", "name": "农药批发(F5167)", "isParent": false, "parentcode": "F516"}, {"id": "F5168", "pId": "F516", "singlehyname": "农用薄膜批发", "name": "农用薄膜批发(F5168)", "isParent": false, "parentcode": "F516"}, {"id": "F5169", "pId": "F516", "singlehyname": "其他化工产品批发", "name": "其他化工产品批发(F5169)", "isParent": false, "parentcode": "F516"}, {"id": "F517", "pId": "F51", "singlehyname": "机械设备、五金产品及电子产品批发", "name": "机械设备、五金产品及电子产品批发(F517)", "isParent": true, "parentcode": "F51"}, {"id": "F5171", "pId": "F517", "singlehyname": "农业机械批发", "name": "农业机械批发(F5171)", "isParent": false, "parentcode": "F517"}, {"id": "F5172", "pId": "F517", "singlehyname": "汽车及零配件批发", "name": "汽车及零配件批发(F5172)", "isParent": false, "parentcode": "F517"}, {"id": "F5173", "pId": "F517", "singlehyname": "摩托车及零配件批发", "name": "摩托车及零配件批发(F5173)", "isParent": false, "parentcode": "F517"}, {"id": "F5174", "pId": "F517", "singlehyname": "五金产品批发", "name": "五金产品批发(F5174)", "isParent": false, "parentcode": "F517"}, {"id": "F5175", "pId": "F517", "singlehyname": "电气设备批发", "name": "电气设备批发(F5175)", "isParent": false, "parentcode": "F517"}, {"id": "F5176", "pId": "F517", "singlehyname": "计算机、软件及辅助设备批发", "name": "计算机、软件及辅助设备批发(F5176)", "isParent": false, "parentcode": "F517"}, {"id": "F5177", "pId": "F517", "singlehyname": "通讯设备批发", "name": "通讯设备批发(F5177)", "isParent": false, "parentcode": "F517"}, {"id": "F5178", "pId": "F517", "singlehyname": "广播影视设备批发", "name": "广播影视设备批发(F5178)", "isParent": false, "parentcode": "F517"}, {"id": "F5179", "pId": "F517", "singlehyname": "其他机械设备及电子产品批发", "name": "其他机械设备及电子产品批发(F5179)", "isParent": false, "parentcode": "F517"}, {"id": "F518", "pId": "F51", "singlehyname": "贸易经纪与代理", "name": "贸易经纪与代理(F518)", "isParent": true, "parentcode": "F51"}, {"id": "F5181", "pId": "F518", "singlehyname": "贸易代理", "name": "贸易代理(F5181)", "isParent": false, "parentcode": "F518"}, {"id": "F5182", "pId": "F518", "singlehyname": "一般物品拍卖", "name": "一般物品拍卖(F5182)", "isParent": false, "parentcode": "F518"}, {"id": "F5183", "pId": "F518", "singlehyname": "艺术品、收藏品拍卖", "name": "艺术品、收藏品拍卖(F5183)", "isParent": false, "parentcode": "F518"}, {"id": "F5184", "pId": "F518", "singlehyname": "艺术品代理", "name": "艺术品代理(F5184)", "isParent": false, "parentcode": "F518"}, {"id": "F5189", "pId": "F518", "singlehyname": "其他贸易经纪与代理", "name": "其他贸易经纪与代理(F5189)", "isParent": false, "parentcode": "F518"}, {"id": "F519", "pId": "F51", "singlehyname": "其他批发业", "name": "其他批发业(F519)", "isParent": true, "parentcode": "F51"}, {"id": "F5191", "pId": "F519", "singlehyname": "再生物资回收与批发", "name": "再生物资回收与批发(F5191)", "isParent": false, "parentcode": "F519"}, {"id": "F5192", "pId": "F519", "singlehyname": "宠物食品用品批发", "name": "宠物食品用品批发(F5192)", "isParent": false, "parentcode": "F519"}, {"id": "F5193", "pId": "F519", "singlehyname": "互联网批发", "name": "互联网批发(F5193)", "isParent": false, "parentcode": "F519"}, {"id": "F5199", "pId": "F519", "singlehyname": "其他未列明批发业", "name": "其他未列明批发业(F5199)", "isParent": false, "parentcode": "F519"}, {"id": "F52", "pId": "F", "singlehyname": "零售业", "name": "零售业(F52)", "isParent": true, "parentcode": "F"}, {"id": "F521", "pId": "F52", "singlehyname": "综合零售", "name": "综合零售(F521)", "isParent": true, "parentcode": "F52"}, {"id": "F5211", "pId": "F521", "singlehyname": "百货零售", "name": "百货零售(F5211)", "isParent": false, "parentcode": "F521"}, {"id": "F5212", "pId": "F521", "singlehyname": "超级市场零售", "name": "超级市场零售(F5212)", "isParent": false, "parentcode": "F521"}, {"id": "F5213", "pId": "F521", "singlehyname": "便利店零售", "name": "便利店零售(F5213)", "isParent": false, "parentcode": "F521"}, {"id": "F5219", "pId": "F521", "singlehyname": "其他综合零售", "name": "其他综合零售(F5219)", "isParent": false, "parentcode": "F521"}, {"id": "F522", "pId": "F52", "singlehyname": "食品、饮料及烟草制品专门零售", "name": "食品、饮料及烟草制品专门零售(F522)", "isParent": true, "parentcode": "F52"}, {"id": "F5221", "pId": "F522", "singlehyname": "粮油零售", "name": "粮油零售(F5221)", "isParent": false, "parentcode": "F522"}, {"id": "F5222", "pId": "F522", "singlehyname": "糕点、面包零售", "name": "糕点、面包零售(F5222)", "isParent": false, "parentcode": "F522"}, {"id": "F5223", "pId": "F522", "singlehyname": "果品、蔬菜零售", "name": "果品、蔬菜零售(F5223)", "isParent": false, "parentcode": "F522"}, {"id": "F5224", "pId": "F522", "singlehyname": "肉、禽、蛋、奶及水产品零售", "name": "肉、禽、蛋、奶及水产品零售(F5224)", "isParent": false, "parentcode": "F522"}, {"id": "F5225", "pId": "F522", "singlehyname": "营养和保健品零售", "name": "营养和保健品零售(F5225)", "isParent": false, "parentcode": "F522"}, {"id": "F5226", "pId": "F522", "singlehyname": "酒、饮料及茶叶零售", "name": "酒、饮料及茶叶零售(F5226)", "isParent": false, "parentcode": "F522"}, {"id": "F5227", "pId": "F522", "singlehyname": "烟草制品零售", "name": "烟草制品零售(F5227)", "isParent": false, "parentcode": "F522"}, {"id": "F5229", "pId": "F522", "singlehyname": "其他食品零售", "name": "其他食品零售(F5229)", "isParent": false, "parentcode": "F522"}, {"id": "F523", "pId": "F52", "singlehyname": "纺织、服装及日用品专门零售", "name": "纺织、服装及日用品专门零售(F523)", "isParent": true, "parentcode": "F52"}, {"id": "F5231", "pId": "F523", "singlehyname": "纺织品及针织品零售", "name": "纺织品及针织品零售(F5231)", "isParent": false, "parentcode": "F523"}, {"id": "F5232", "pId": "F523", "singlehyname": "服装零售", "name": "服装零售(F5232)", "isParent": false, "parentcode": "F523"}, {"id": "F5233", "pId": "F523", "singlehyname": "鞋帽零售", "name": "鞋帽零售(F5233)", "isParent": false, "parentcode": "F523"}, {"id": "F5234", "pId": "F523", "singlehyname": "化妆品及卫生用品零售", "name": "化妆品及卫生用品零售(F5234)", "isParent": false, "parentcode": "F523"}, {"id": "F5235", "pId": "F523", "singlehyname": "厨具卫具及日用杂品零售", "name": "厨具卫具及日用杂品零售(F5235)", "isParent": false, "parentcode": "F523"}, {"id": "F5236", "pId": "F523", "singlehyname": "钟表、眼镜零售", "name": "钟表、眼镜零售(F5236)", "isParent": false, "parentcode": "F523"}, {"id": "F5237", "pId": "F523", "singlehyname": "箱包零售", "name": "箱包零售(F5237)", "isParent": false, "parentcode": "F523"}, {"id": "F5238", "pId": "F523", "singlehyname": "自行车等代步设备零售", "name": "自行车等代步设备零售(F5238)", "isParent": false, "parentcode": "F523"}, {"id": "F5239", "pId": "F523", "singlehyname": "其他日用品零售", "name": "其他日用品零售(F5239)", "isParent": false, "parentcode": "F523"}, {"id": "F524", "pId": "F52", "singlehyname": "文化、体育用品及器材专门零售", "name": "文化、体育用品及器材专门零售(F524)", "isParent": true, "parentcode": "F52"}, {"id": "F5241", "pId": "F524", "singlehyname": "文具用品零售", "name": "文具用品零售(F5241)", "isParent": false, "parentcode": "F524"}, {"id": "F5242", "pId": "F524", "singlehyname": "体育用品及器材零售", "name": "体育用品及器材零售(F5242)", "isParent": false, "parentcode": "F524"}, {"id": "F5243", "pId": "F524", "singlehyname": "图书、报刊零售", "name": "图书、报刊零售(F5243)", "isParent": false, "parentcode": "F524"}, {"id": "F5244", "pId": "F524", "singlehyname": "音像制品、电子和数字出版物零售", "name": "音像制品、电子和数字出版物零售(F5244)", "isParent": false, "parentcode": "F524"}, {"id": "F5245", "pId": "F524", "singlehyname": "珠宝首饰零售", "name": "珠宝首饰零售(F5245)", "isParent": false, "parentcode": "F524"}, {"id": "F5246", "pId": "F524", "singlehyname": "工艺美术品及收藏品零售", "name": "工艺美术品及收藏品零售(F5246)", "isParent": false, "parentcode": "F524"}, {"id": "F5247", "pId": "F524", "singlehyname": "乐器零售", "name": "乐器零售(F5247)", "isParent": false, "parentcode": "F524"}, {"id": "F5248", "pId": "F524", "singlehyname": "照相器材零售", "name": "照相器材零售(F5248)", "isParent": false, "parentcode": "F524"}, {"id": "F5249", "pId": "F524", "singlehyname": "其他文化用品零售", "name": "其他文化用品零售(F5249)", "isParent": false, "parentcode": "F524"}, {"id": "F525", "pId": "F52", "singlehyname": "医药及医疗器材专门零售", "name": "医药及医疗器材专门零售(F525)", "isParent": true, "parentcode": "F52"}, {"id": "F5251", "pId": "F525", "singlehyname": "西药零售", "name": "西药零售(F5251)", "isParent": false, "parentcode": "F525"}, {"id": "F5252", "pId": "F525", "singlehyname": "中药零售", "name": "中药零售(F5252)", "isParent": false, "parentcode": "F525"}, {"id": "F5253", "pId": "F525", "singlehyname": "动物用药品零售", "name": "动物用药品零售(F5253)", "isParent": false, "parentcode": "F525"}, {"id": "F5254", "pId": "F525", "singlehyname": "医疗用品及器材零售", "name": "医疗用品及器材零售(F5254)", "isParent": false, "parentcode": "F525"}, {"id": "F5255", "pId": "F525", "singlehyname": "保健辅助治疗器材零售", "name": "保健辅助治疗器材零售(F5255)", "isParent": false, "parentcode": "F525"}, {"id": "F526", "pId": "F52", "singlehyname": "汽车、摩托车、零配件和燃料及其他动力销售", "name": "汽车、摩托车、零配件和燃料及其他动力销售(F526)", "isParent": true, "parentcode": "F52"}, {"id": "F5261", "pId": "F526", "singlehyname": "汽车新车零售", "name": "汽车新车零售(F5261)", "isParent": false, "parentcode": "F526"}, {"id": "F5262", "pId": "F526", "singlehyname": "汽车旧车零售", "name": "汽车旧车零售(F5262)", "isParent": false, "parentcode": "F526"}, {"id": "F5263", "pId": "F526", "singlehyname": "汽车零配件零售", "name": "汽车零配件零售(F5263)", "isParent": false, "parentcode": "F526"}, {"id": "F5264", "pId": "F526", "singlehyname": "摩托车及零配件零售", "name": "摩托车及零配件零售(F5264)", "isParent": false, "parentcode": "F526"}, {"id": "F5265", "pId": "F526", "singlehyname": "机动车燃油零售", "name": "机动车燃油零售(F5265)", "isParent": false, "parentcode": "F526"}, {"id": "F5266", "pId": "F526", "singlehyname": "机动车燃气零售", "name": "机动车燃气零售(F5266)", "isParent": false, "parentcode": "F526"}, {"id": "F5267", "pId": "F526", "singlehyname": "机动车充电销售", "name": "机动车充电销售(F5267)", "isParent": false, "parentcode": "F526"}, {"id": "F527", "pId": "F52", "singlehyname": "家用电器及电子产品专门零售", "name": "家用电器及电子产品专门零售(F527)", "isParent": true, "parentcode": "F52"}, {"id": "F5271", "pId": "F527", "singlehyname": "家用视听设备零售", "name": "家用视听设备零售(F5271)", "isParent": false, "parentcode": "F527"}, {"id": "F5272", "pId": "F527", "singlehyname": "日用家电零售", "name": "日用家电零售(F5272)", "isParent": false, "parentcode": "F527"}, {"id": "F5273", "pId": "F527", "singlehyname": "计算机、软件及辅助设备零售", "name": "计算机、软件及辅助设备零售(F5273)", "isParent": false, "parentcode": "F527"}, {"id": "F5274", "pId": "F527", "singlehyname": "通信设备零售", "name": "通信设备零售(F5274)", "isParent": false, "parentcode": "F527"}, {"id": "F5279", "pId": "F527", "singlehyname": "其他电子产品零售", "name": "其他电子产品零售(F5279)", "isParent": false, "parentcode": "F527"}, {"id": "F528", "pId": "F52", "singlehyname": "五金、家具及室内装饰材料专门零售", "name": "五金、家具及室内装饰材料专门零售(F528)", "isParent": true, "parentcode": "F52"}, {"id": "F5281", "pId": "F528", "singlehyname": "五金零售", "name": "五金零售(F5281)", "isParent": false, "parentcode": "F528"}, {"id": "F5282", "pId": "F528", "singlehyname": "灯具零售", "name": "灯具零售(F5282)", "isParent": false, "parentcode": "F528"}, {"id": "F5283", "pId": "F528", "singlehyname": "家具零售", "name": "家具零售(F5283)", "isParent": false, "parentcode": "F528"}, {"id": "F5284", "pId": "F528", "singlehyname": "涂料零售", "name": "涂料零售(F5284)", "isParent": false, "parentcode": "F528"}, {"id": "F5285", "pId": "F528", "singlehyname": "卫生洁具零售", "name": "卫生洁具零售(F5285)", "isParent": false, "parentcode": "F528"}, {"id": "F5286", "pId": "F528", "singlehyname": "木质装饰材料零售", "name": "木质装饰材料零售(F5286)", "isParent": false, "parentcode": "F528"}, {"id": "F5287", "pId": "F528", "singlehyname": "陶瓷、石材装饰材料零售", "name": "陶瓷、石材装饰材料零售(F5287)", "isParent": false, "parentcode": "F528"}, {"id": "F5289", "pId": "F528", "singlehyname": "其他室内装饰材料零售", "name": "其他室内装饰材料零售(F5289)", "isParent": false, "parentcode": "F528"}, {"id": "F529", "pId": "F52", "singlehyname": "货摊、无店铺及其他零售业", "name": "货摊、无店铺及其他零售业(F529)", "isParent": true, "parentcode": "F52"}, {"id": "F5291", "pId": "F529", "singlehyname": "流动货摊零售", "name": "流动货摊零售(F5291)", "isParent": false, "parentcode": "F529"}, {"id": "F5292", "pId": "F529", "singlehyname": "互联网零售", "name": "互联网零售(F5292)", "isParent": false, "parentcode": "F529"}, {"id": "F5293", "pId": "F529", "singlehyname": "邮购及电视、电话零售", "name": "邮购及电视、电话零售(F5293)", "isParent": false, "parentcode": "F529"}, {"id": "F5294", "pId": "F529", "singlehyname": "自动售货机零售", "name": "自动售货机零售(F5294)", "isParent": false, "parentcode": "F529"}, {"id": "F5295", "pId": "F529", "singlehyname": "旧货零售", "name": "旧货零售(F5295)", "isParent": false, "parentcode": "F529"}, {"id": "F5296", "pId": "F529", "singlehyname": "生活用燃料零售", "name": "生活用燃料零售(F5296)", "isParent": false, "parentcode": "F529"}, {"id": "F5297", "pId": "F529", "singlehyname": "宠物食品用品零售", "name": "宠物食品用品零售(F5297)", "isParent": false, "parentcode": "F529"}, {"id": "F5299", "pId": "F529", "singlehyname": "其他未列明零售业", "name": "其他未列明零售业(F5299)", "isParent": false, "parentcode": "F529"}, {"id": "G", "pId": "", "singlehyname": "交通运输、仓储和邮政业", "name": "交通运输、仓储和邮政业(G)", "isParent": true, "parentcode": ""}, {"id": "G53", "pId": "G", "singlehyname": "铁路运输业", "name": "铁路运输业(G53)", "isParent": true, "parentcode": "G"}, {"id": "G531", "pId": "G53", "singlehyname": "铁路旅客运输", "name": "铁路旅客运输(G531)", "isParent": true, "parentcode": "G53"}, {"id": "G5311", "pId": "G531", "singlehyname": "高速铁路旅客运输", "name": "高速铁路旅客运输(G5311)", "isParent": false, "parentcode": "G531"}, {"id": "G5312", "pId": "G531", "singlehyname": "城际铁路旅客运输", "name": "城际铁路旅客运输(G5312)", "isParent": false, "parentcode": "G531"}, {"id": "G5313", "pId": "G531", "singlehyname": "普通铁路旅客运输", "name": "普通铁路旅客运输(G5313)", "isParent": false, "parentcode": "G531"}, {"id": "G532", "pId": "G53", "singlehyname": "铁路货物运输", "name": "铁路货物运输(G532)", "isParent": false, "parentcode": "G53"}, {"id": "G533", "pId": "G53", "singlehyname": "铁路运输辅助活动", "name": "铁路运输辅助活动(G533)", "isParent": true, "parentcode": "G53"}, {"id": "G5331", "pId": "G533", "singlehyname": "客运火车站", "name": "客运火车站(G5331)", "isParent": false, "parentcode": "G533"}, {"id": "G5332", "pId": "G533", "singlehyname": "货运火车站(场)", "name": "货运火车站(场)(G5332)", "isParent": false, "parentcode": "G533"}, {"id": "G5333", "pId": "G533", "singlehyname": "铁路运输维护活动", "name": "铁路运输维护活动(G5333)", "isParent": false, "parentcode": "G533"}, {"id": "G5339", "pId": "G533", "singlehyname": "其他铁路运输辅助活动", "name": "其他铁路运输辅助活动(G5339)", "isParent": false, "parentcode": "G533"}, {"id": "G54", "pId": "G", "singlehyname": "道路运输业", "name": "道路运输业(G54)", "isParent": true, "parentcode": "G"}, {"id": "G541", "pId": "G54", "singlehyname": "城市公共交通运输", "name": "城市公共交通运输(G541)", "isParent": true, "parentcode": "G54"}, {"id": "G5411", "pId": "G541", "singlehyname": "公共电汽车客运", "name": "公共电汽车客运(G5411)", "isParent": false, "parentcode": "G541"}, {"id": "G5412", "pId": "G541", "singlehyname": "城市轨道交通", "name": "城市轨道交通(G5412)", "isParent": false, "parentcode": "G541"}, {"id": "G5413", "pId": "G541", "singlehyname": "出租车客运", "name": "出租车客运(G5413)", "isParent": false, "parentcode": "G541"}, {"id": "G5414", "pId": "G541", "singlehyname": "公共自行车服务", "name": "公共自行车服务(G5414)", "isParent": false, "parentcode": "G541"}, {"id": "G5419", "pId": "G541", "singlehyname": "其他城市公共交通运输", "name": "其他城市公共交通运输(G5419)", "isParent": false, "parentcode": "G541"}, {"id": "G542", "pId": "G54", "singlehyname": "公路旅客运输", "name": "公路旅客运输(G542)", "isParent": true, "parentcode": "G54"}, {"id": "G5421", "pId": "G542", "singlehyname": "长途客运", "name": "长途客运(G5421)", "isParent": false, "parentcode": "G542"}, {"id": "G5422", "pId": "G542", "singlehyname": "旅游客运", "name": "旅游客运(G5422)", "isParent": false, "parentcode": "G542"}, {"id": "G5429", "pId": "G542", "singlehyname": "其他公路客运", "name": "其他公路客运(G5429)", "isParent": false, "parentcode": "G542"}, {"id": "G543", "pId": "G54", "singlehyname": "道路货物运输", "name": "道路货物运输(G543)", "isParent": true, "parentcode": "G54"}, {"id": "G5431", "pId": "G543", "singlehyname": "普通货物道路运输", "name": "普通货物道路运输(G5431)", "isParent": false, "parentcode": "G543"}, {"id": "G5432", "pId": "G543", "singlehyname": "冷藏车道路运输", "name": "冷藏车道路运输(G5432)", "isParent": false, "parentcode": "G543"}, {"id": "G5433", "pId": "G543", "singlehyname": "集装箱道路运输", "name": "集装箱道路运输(G5433)", "isParent": false, "parentcode": "G543"}, {"id": "G5434", "pId": "G543", "singlehyname": "大型货物道路运输", "name": "大型货物道路运输(G5434)", "isParent": false, "parentcode": "G543"}, {"id": "G5435", "pId": "G543", "singlehyname": "危险货物道路运输", "name": "危险货物道路运输(G5435)", "isParent": false, "parentcode": "G543"}, {"id": "G5436", "pId": "G543", "singlehyname": "邮件包裹道路运输", "name": "邮件包裹道路运输(G5436)", "isParent": false, "parentcode": "G543"}, {"id": "G5437", "pId": "G543", "singlehyname": "城市配送", "name": "城市配送(G5437)", "isParent": false, "parentcode": "G543"}, {"id": "G5438", "pId": "G543", "singlehyname": "搬家运输", "name": "搬家运输(G5438)", "isParent": false, "parentcode": "G543"}, {"id": "G5439", "pId": "G543", "singlehyname": "其他道路货物运输", "name": "其他道路货物运输(G5439)", "isParent": false, "parentcode": "G543"}, {"id": "G544", "pId": "G54", "singlehyname": "道路运输辅助活动", "name": "道路运输辅助活动(G544)", "isParent": true, "parentcode": "G54"}, {"id": "G5441", "pId": "G544", "singlehyname": "客运汽车站", "name": "客运汽车站(G5441)", "isParent": false, "parentcode": "G544"}, {"id": "G5442", "pId": "G544", "singlehyname": "货运枢纽(站)", "name": "货运枢纽(站)(G5442)", "isParent": false, "parentcode": "G544"}, {"id": "G5443", "pId": "G544", "singlehyname": "公路管理与养护", "name": "公路管理与养护(G5443)", "isParent": false, "parentcode": "G544"}, {"id": "G5449", "pId": "G544", "singlehyname": "其他道路运输辅助活动", "name": "其他道路运输辅助活动(G5449)", "isParent": false, "parentcode": "G544"}, {"id": "G55", "pId": "G", "singlehyname": "水上运输业", "name": "水上运输业(G55)", "isParent": true, "parentcode": "G"}, {"id": "G551", "pId": "G55", "singlehyname": "水上旅客运输", "name": "水上旅客运输(G551)", "isParent": true, "parentcode": "G55"}, {"id": "G5511", "pId": "G551", "singlehyname": "海上旅客运输", "name": "海上旅客运输(G5511)", "isParent": false, "parentcode": "G551"}, {"id": "G5512", "pId": "G551", "singlehyname": "内河旅客运输", "name": "内河旅客运输(G5512)", "isParent": false, "parentcode": "G551"}, {"id": "G5513", "pId": "G551", "singlehyname": "客运轮渡运输", "name": "客运轮渡运输(G5513)", "isParent": false, "parentcode": "G551"}, {"id": "G552", "pId": "G55", "singlehyname": "水上货物运输", "name": "水上货物运输(G552)", "isParent": true, "parentcode": "G55"}, {"id": "G5521", "pId": "G552", "singlehyname": "远洋货物运输", "name": "远洋货物运输(G5521)", "isParent": false, "parentcode": "G552"}, {"id": "G5522", "pId": "G552", "singlehyname": "沿海货物运输", "name": "沿海货物运输(G5522)", "isParent": false, "parentcode": "G552"}, {"id": "G5523", "pId": "G552", "singlehyname": "内河货物运输", "name": "内河货物运输(G5523)", "isParent": false, "parentcode": "G552"}, {"id": "G553", "pId": "G55", "singlehyname": "水上运输辅助活动", "name": "水上运输辅助活动(G553)", "isParent": true, "parentcode": "G55"}, {"id": "G5531", "pId": "G553", "singlehyname": "客运港口", "name": "客运港口(G5531)", "isParent": false, "parentcode": "G553"}, {"id": "G5532", "pId": "G553", "singlehyname": "货运港口", "name": "货运港口(G5532)", "isParent": false, "parentcode": "G553"}, {"id": "G5539", "pId": "G553", "singlehyname": "其他水上运输辅助活动", "name": "其他水上运输辅助活动(G5539)", "isParent": false, "parentcode": "G553"}, {"id": "G56", "pId": "G", "singlehyname": "航空运输业", "name": "航空运输业(G56)", "isParent": true, "parentcode": "G"}, {"id": "G561", "pId": "G56", "singlehyname": "航空客货运输", "name": "航空客货运输(G561)", "isParent": true, "parentcode": "G56"}, {"id": "G5611", "pId": "G561", "singlehyname": "航空旅客运输", "name": "航空旅客运输(G5611)", "isParent": false, "parentcode": "G561"}, {"id": "G5612", "pId": "G561", "singlehyname": "航空货物运输", "name": "航空货物运输(G5612)", "isParent": false, "parentcode": "G561"}, {"id": "G562", "pId": "G56", "singlehyname": "通用航空服务", "name": "通用航空服务(G562)", "isParent": true, "parentcode": "G56"}, {"id": "G5621", "pId": "G562", "singlehyname": "通用航空生产服务", "name": "通用航空生产服务(G5621)", "isParent": false, "parentcode": "G562"}, {"id": "G5622", "pId": "G562", "singlehyname": "观光游览航空服务", "name": "观光游览航空服务(G5622)", "isParent": false, "parentcode": "G562"}, {"id": "G5623", "pId": "G562", "singlehyname": "体育航空运动服务", "name": "体育航空运动服务(G5623)", "isParent": false, "parentcode": "G562"}, {"id": "G5629", "pId": "G562", "singlehyname": "其他通用航空服务", "name": "其他通用航空服务(G5629)", "isParent": false, "parentcode": "G562"}, {"id": "G563", "pId": "G56", "singlehyname": "航空运输辅助活动", "name": "航空运输辅助活动(G563)", "isParent": true, "parentcode": "G56"}, {"id": "G5631", "pId": "G563", "singlehyname": "机场", "name": "机场(G5631)", "isParent": false, "parentcode": "G563"}, {"id": "G5632", "pId": "G563", "singlehyname": "空中交通管理", "name": "空中交通管理(G5632)", "isParent": false, "parentcode": "G563"}, {"id": "G5639", "pId": "G563", "singlehyname": "其他航空运输辅助活动", "name": "其他航空运输辅助活动(G5639)", "isParent": false, "parentcode": "G563"}, {"id": "G57", "pId": "G", "singlehyname": "管道运输业", "name": "管道运输业(G57)", "isParent": true, "parentcode": "G"}, {"id": "G571", "pId": "G57", "singlehyname": "海底管道运输", "name": "海底管道运输(G571)", "isParent": false, "parentcode": "G57"}, {"id": "G572", "pId": "G57", "singlehyname": "陆地管道运输", "name": "陆地管道运输(G572)", "isParent": false, "parentcode": "G57"}, {"id": "G58", "pId": "G", "singlehyname": "多式联运和运输代理业", "name": "多式联运和运输代理业(G58)", "isParent": true, "parentcode": "G"}, {"id": "G581", "pId": "G58", "singlehyname": "多式联运", "name": "多式联运(G581)", "isParent": false, "parentcode": "G58"}, {"id": "G582", "pId": "G58", "singlehyname": "运输代理业", "name": "运输代理业(G582)", "isParent": true, "parentcode": "G58"}, {"id": "G5821", "pId": "G582", "singlehyname": "货物运输代理", "name": "货物运输代理(G5821)", "isParent": false, "parentcode": "G582"}, {"id": "G5822", "pId": "G582", "singlehyname": "旅客票务代理", "name": "旅客票务代理(G5822)", "isParent": false, "parentcode": "G582"}, {"id": "G5829", "pId": "G582", "singlehyname": "其他运输代理业", "name": "其他运输代理业(G5829)", "isParent": false, "parentcode": "G582"}, {"id": "G59", "pId": "G", "singlehyname": "装卸搬运和仓储业", "name": "装卸搬运和仓储业(G59)", "isParent": true, "parentcode": "G"}, {"id": "G591", "pId": "G59", "singlehyname": "装卸搬运", "name": "装卸搬运(G591)", "isParent": false, "parentcode": "G59"}, {"id": "G592", "pId": "G59", "singlehyname": "通用仓储", "name": "通用仓储(G592)", "isParent": false, "parentcode": "G59"}, {"id": "G593", "pId": "G59", "singlehyname": "低温仓储", "name": "低温仓储(G593)", "isParent": false, "parentcode": "G59"}, {"id": "G594", "pId": "G59", "singlehyname": "危险品仓储", "name": "危险品仓储(G594)", "isParent": true, "parentcode": "G59"}, {"id": "G5941", "pId": "G594", "singlehyname": "油气仓储", "name": "油气仓储(G5941)", "isParent": false, "parentcode": "G594"}, {"id": "G5942", "pId": "G594", "singlehyname": "危险化学品仓储", "name": "危险化学品仓储(G5942)", "isParent": false, "parentcode": "G594"}, {"id": "G5949", "pId": "G594", "singlehyname": "其他危险品仓储", "name": "其他危险品仓储(G5949)", "isParent": false, "parentcode": "G594"}, {"id": "G595", "pId": "G59", "singlehyname": "谷物、棉花等农产品仓储", "name": "谷物、棉花等农产品仓储(G595)", "isParent": true, "parentcode": "G59"}, {"id": "G5951", "pId": "G595", "singlehyname": "谷物仓储", "name": "谷物仓储(G5951)", "isParent": false, "parentcode": "G595"}, {"id": "G5952", "pId": "G595", "singlehyname": "棉花仓储", "name": "棉花仓储(G5952)", "isParent": false, "parentcode": "G595"}, {"id": "G5959", "pId": "G595", "singlehyname": "其他农产品仓储", "name": "其他农产品仓储(G5959)", "isParent": false, "parentcode": "G595"}, {"id": "G596", "pId": "G59", "singlehyname": "中药材仓储", "name": "中药材仓储(G596)", "isParent": false, "parentcode": "G59"}, {"id": "G599", "pId": "G59", "singlehyname": "其他仓储业", "name": "其他仓储业(G599)", "isParent": false, "parentcode": "G59"}, {"id": "G60", "pId": "G", "singlehyname": "邮政业", "name": "邮政业(G60)", "isParent": true, "parentcode": "G"}, {"id": "G601", "pId": "G60", "singlehyname": "邮政基本服务", "name": "邮政基本服务(G601)", "isParent": false, "parentcode": "G60"}, {"id": "G602", "pId": "G60", "singlehyname": "快递服务", "name": "快递服务(G602)", "isParent": false, "parentcode": "G60"}, {"id": "G609", "pId": "G60", "singlehyname": "其他寄递服务", "name": "其他寄递服务(G609)", "isParent": false, "parentcode": "G60"}, {"id": "H", "pId": "", "singlehyname": "住宿和餐饮业", "name": "住宿和餐饮业(H)", "isParent": true, "parentcode": ""}, {"id": "H61", "pId": "H", "singlehyname": "住宿业", "name": "住宿业(H61)", "isParent": true, "parentcode": "H"}, {"id": "H611", "pId": "H61", "singlehyname": "旅游饭店", "name": "旅游饭店(H611)", "isParent": false, "parentcode": "H61"}, {"id": "H612", "pId": "H61", "singlehyname": "一般旅馆", "name": "一般旅馆(H612)", "isParent": true, "parentcode": "H61"}, {"id": "H6121", "pId": "H612", "singlehyname": "经济型连锁酒店", "name": "经济型连锁酒店(H6121)", "isParent": false, "parentcode": "H612"}, {"id": "H6129", "pId": "H612", "singlehyname": "其他一般旅馆", "name": "其他一般旅馆(H6129)", "isParent": false, "parentcode": "H612"}, {"id": "H613", "pId": "H61", "singlehyname": "民宿服务", "name": "民宿服务(H613)", "isParent": false, "parentcode": "H61"}, {"id": "H614", "pId": "H61", "singlehyname": "露营地服务", "name": "露营地服务(H614)", "isParent": false, "parentcode": "H61"}, {"id": "H619", "pId": "H61", "singlehyname": "其他住宿业", "name": "其他住宿业(H619)", "isParent": false, "parentcode": "H61"}, {"id": "H62", "pId": "H", "singlehyname": "餐饮业", "name": "餐饮业(H62)", "isParent": true, "parentcode": "H"}, {"id": "H621", "pId": "H62", "singlehyname": "正餐服务", "name": "正餐服务(H621)", "isParent": false, "parentcode": "H62"}, {"id": "H622", "pId": "H62", "singlehyname": "快餐服务", "name": "快餐服务(H622)", "isParent": false, "parentcode": "H62"}, {"id": "H623", "pId": "H62", "singlehyname": "饮料及冷饮服务", "name": "饮料及冷饮服务(H623)", "isParent": true, "parentcode": "H62"}, {"id": "H6231", "pId": "H623", "singlehyname": "茶馆服务", "name": "茶馆服务(H6231)", "isParent": false, "parentcode": "H623"}, {"id": "H6232", "pId": "H623", "singlehyname": "咖啡馆服务", "name": "咖啡馆服务(H6232)", "isParent": false, "parentcode": "H623"}, {"id": "H6233", "pId": "H623", "singlehyname": "酒吧服务", "name": "酒吧服务(H6233)", "isParent": false, "parentcode": "H623"}, {"id": "H6239", "pId": "H623", "singlehyname": "其他饮料及冷饮服务", "name": "其他饮料及冷饮服务(H6239)", "isParent": false, "parentcode": "H623"}, {"id": "H624", "pId": "H62", "singlehyname": "餐饮配送及外卖送餐服务", "name": "餐饮配送及外卖送餐服务(H624)", "isParent": true, "parentcode": "H62"}, {"id": "H6241", "pId": "H624", "singlehyname": "餐饮配送服务", "name": "餐饮配送服务(H6241)", "isParent": false, "parentcode": "H624"}, {"id": "H6242", "pId": "H624", "singlehyname": "外卖送餐服务", "name": "外卖送餐服务(H6242)", "isParent": false, "parentcode": "H624"}, {"id": "H629", "pId": "H62", "singlehyname": "其他餐饮业", "name": "其他餐饮业(H629)", "isParent": true, "parentcode": "H62"}, {"id": "H6291", "pId": "H629", "singlehyname": "小吃服务", "name": "小吃服务(H6291)", "isParent": false, "parentcode": "H629"}, {"id": "H6299", "pId": "H629", "singlehyname": "其他未列明餐饮业", "name": "其他未列明餐饮业(H6299)", "isParent": false, "parentcode": "H629"}, {"id": "I", "pId": "", "singlehyname": "信息传输、软件和信息技术服务业", "name": "信息传输、软件和信息技术服务业(I)", "isParent": true, "parentcode": ""}, {"id": "I63", "pId": "I", "singlehyname": "电信、广播电视和卫星传输服务", "name": "电信、广播电视和卫星传输服务(I63)", "isParent": true, "parentcode": "I"}, {"id": "I631", "pId": "I63", "singlehyname": "电信", "name": "电信(I631)", "isParent": true, "parentcode": "I63"}, {"id": "I6311", "pId": "I631", "singlehyname": "固定电信服务", "name": "固定电信服务(I6311)", "isParent": false, "parentcode": "I631"}, {"id": "I6312", "pId": "I631", "singlehyname": "移动电信服务", "name": "移动电信服务(I6312)", "isParent": false, "parentcode": "I631"}, {"id": "I6319", "pId": "I631", "singlehyname": "其他电信服务", "name": "其他电信服务(I6319)", "isParent": false, "parentcode": "I631"}, {"id": "I632", "pId": "I63", "singlehyname": "广播电视传输服务", "name": "广播电视传输服务(I632)", "isParent": true, "parentcode": "I63"}, {"id": "I6321", "pId": "I632", "singlehyname": "有线广播电视传输服务", "name": "有线广播电视传输服务(I6321)", "isParent": false, "parentcode": "I632"}, {"id": "I6322", "pId": "I632", "singlehyname": "无线广播电视传输服务", "name": "无线广播电视传输服务(I6322)", "isParent": false, "parentcode": "I632"}, {"id": "I633", "pId": "I63", "singlehyname": "卫星传输服务", "name": "卫星传输服务(I633)", "isParent": true, "parentcode": "I63"}, {"id": "I6331", "pId": "I633", "singlehyname": "广播电视卫星传输服务", "name": "广播电视卫星传输服务(I6331)", "isParent": false, "parentcode": "I633"}, {"id": "I6339", "pId": "I633", "singlehyname": "其他卫星传输服务", "name": "其他卫星传输服务(I6339)", "isParent": false, "parentcode": "I633"}, {"id": "I64", "pId": "I", "singlehyname": "互联网和相关服务", "name": "互联网和相关服务(I64)", "isParent": true, "parentcode": "I"}, {"id": "I641", "pId": "I64", "singlehyname": "互联网接入及相关服务", "name": "互联网接入及相关服务(I641)", "isParent": false, "parentcode": "I64"}, {"id": "I642", "pId": "I64", "singlehyname": "互联网信息服务", "name": "互联网信息服务(I642)", "isParent": true, "parentcode": "I64"}, {"id": "I6421", "pId": "I642", "singlehyname": "互联网搜索服务", "name": "互联网搜索服务(I6421)", "isParent": false, "parentcode": "I642"}, {"id": "I6422", "pId": "I642", "singlehyname": "互联网游戏服务", "name": "互联网游戏服务(I6422)", "isParent": false, "parentcode": "I642"}, {"id": "I6429", "pId": "I642", "singlehyname": "互联网其他信息服务", "name": "互联网其他信息服务(I6429)", "isParent": false, "parentcode": "I642"}, {"id": "I643", "pId": "I64", "singlehyname": "互联网平台", "name": "互联网平台(I643)", "isParent": true, "parentcode": "I64"}, {"id": "I6431", "pId": "I643", "singlehyname": "互联网生产服务平台", "name": "互联网生产服务平台(I6431)", "isParent": false, "parentcode": "I643"}, {"id": "I6432", "pId": "I643", "singlehyname": "互联网生活服务平台", "name": "互联网生活服务平台(I6432)", "isParent": false, "parentcode": "I643"}, {"id": "I6433", "pId": "I643", "singlehyname": "互联网科技创新平台", "name": "互联网科技创新平台(I6433)", "isParent": false, "parentcode": "I643"}, {"id": "I6434", "pId": "I643", "singlehyname": "互联网公共服务平台", "name": "互联网公共服务平台(I6434)", "isParent": false, "parentcode": "I643"}, {"id": "I6439", "pId": "I643", "singlehyname": "其他互联网平台", "name": "其他互联网平台(I6439)", "isParent": false, "parentcode": "I643"}, {"id": "I644", "pId": "I64", "singlehyname": "互联网安全服务", "name": "互联网安全服务(I644)", "isParent": false, "parentcode": "I64"}, {"id": "I645", "pId": "I64", "singlehyname": "互联网数据服务", "name": "互联网数据服务(I645)", "isParent": false, "parentcode": "I64"}, {"id": "I649", "pId": "I64", "singlehyname": "其他互联网服务", "name": "其他互联网服务(I649)", "isParent": false, "parentcode": "I64"}, {"id": "I65", "pId": "I", "singlehyname": "软件和信息技术服务业", "name": "软件和信息技术服务业(I65)", "isParent": true, "parentcode": "I"}, {"id": "I651", "pId": "I65", "singlehyname": "软件开发", "name": "软件开发(I651)", "isParent": true, "parentcode": "I65"}, {"id": "I6511", "pId": "I651", "singlehyname": "基础软件开发", "name": "基础软件开发(I6511)", "isParent": false, "parentcode": "I651"}, {"id": "I6512", "pId": "I651", "singlehyname": "支撑软件开发", "name": "支撑软件开发(I6512)", "isParent": false, "parentcode": "I651"}, {"id": "I6513", "pId": "I651", "singlehyname": "应用软件开发", "name": "应用软件开发(I6513)", "isParent": false, "parentcode": "I651"}, {"id": "I6519", "pId": "I651", "singlehyname": "其他软件开发", "name": "其他软件开发(I6519)", "isParent": false, "parentcode": "I651"}, {"id": "I652", "pId": "I65", "singlehyname": "集成电路设计", "name": "集成电路设计(I652)", "isParent": false, "parentcode": "I65"}, {"id": "I653", "pId": "I65", "singlehyname": "信息系统集成和物联网技术服务", "name": "信息系统集成和物联网技术服务(I653)", "isParent": true, "parentcode": "I65"}, {"id": "I6531", "pId": "I653", "singlehyname": "信息系统集成服务", "name": "信息系统集成服务(I6531)", "isParent": false, "parentcode": "I653"}, {"id": "I6532", "pId": "I653", "singlehyname": "物联网技术服务", "name": "物联网技术服务(I6532)", "isParent": false, "parentcode": "I653"}, {"id": "I654", "pId": "I65", "singlehyname": "运行维护服务", "name": "运行维护服务(I654)", "isParent": false, "parentcode": "I65"}, {"id": "I655", "pId": "I65", "singlehyname": "信息处理和存储支持服务", "name": "信息处理和存储支持服务(I655)", "isParent": false, "parentcode": "I65"}, {"id": "I656", "pId": "I65", "singlehyname": "信息技术咨询服务", "name": "信息技术咨询服务(I656)", "isParent": false, "parentcode": "I65"}, {"id": "I657", "pId": "I65", "singlehyname": "数字内容服务", "name": "数字内容服务(I657)", "isParent": true, "parentcode": "I65"}, {"id": "I6571", "pId": "I657", "singlehyname": "地理遥感信息服务", "name": "地理遥感信息服务(I6571)", "isParent": false, "parentcode": "I657"}, {"id": "I6572", "pId": "I657", "singlehyname": "动漫、游戏数字内容服务", "name": "动漫、游戏数字内容服务(I6572)", "isParent": false, "parentcode": "I657"}, {"id": "I6579", "pId": "I657", "singlehyname": "其他数字内容服务", "name": "其他数字内容服务(I6579)", "isParent": false, "parentcode": "I657"}, {"id": "I659", "pId": "I65", "singlehyname": "其他信息技术服务业", "name": "其他信息技术服务业(I659)", "isParent": true, "parentcode": "I65"}, {"id": "I6591", "pId": "I659", "singlehyname": "呼叫中心", "name": "呼叫中心(I6591)", "isParent": false, "parentcode": "I659"}, {"id": "I6599", "pId": "I659", "singlehyname": "其他未列明信息技术服务业", "name": "其他未列明信息技术服务业(I6599)", "isParent": false, "parentcode": "I659"}, {"id": "J", "pId": "", "singlehyname": "金融业", "name": "金融业(J)", "isParent": true, "parentcode": ""}, {"id": "J66", "pId": "J", "singlehyname": "货币金融服务", "name": "货币金融服务(J66)", "isParent": true, "parentcode": "J"}, {"id": "J661", "pId": "J66", "singlehyname": "中央银行服务", "name": "中央银行服务(J661)", "isParent": false, "parentcode": "J66"}, {"id": "J662", "pId": "J66", "singlehyname": "货币银行服务", "name": "货币银行服务(J662)", "isParent": true, "parentcode": "J66"}, {"id": "J6621", "pId": "J662", "singlehyname": "商业银行服务", "name": "商业银行服务(J6621)", "isParent": false, "parentcode": "J662"}, {"id": "J6622", "pId": "J662", "singlehyname": "政策性银行服务", "name": "政策性银行服务(J6622)", "isParent": false, "parentcode": "J662"}, {"id": "J6623", "pId": "J662", "singlehyname": "信用合作社服务", "name": "信用合作社服务(J6623)", "isParent": false, "parentcode": "J662"}, {"id": "J6624", "pId": "J662", "singlehyname": "农村资金互助社服务", "name": "农村资金互助社服务(J6624)", "isParent": false, "parentcode": "J662"}, {"id": "J6629", "pId": "J662", "singlehyname": "其他货币银行服务", "name": "其他货币银行服务(J6629)", "isParent": false, "parentcode": "J662"}, {"id": "J663", "pId": "J66", "singlehyname": "非货币银行服务", "name": "非货币银行服务(J663)", "isParent": true, "parentcode": "J66"}, {"id": "J6631", "pId": "J663", "singlehyname": "融资租赁服务", "name": "融资租赁服务(J6631)", "isParent": false, "parentcode": "J663"}, {"id": "J6632", "pId": "J663", "singlehyname": "财务公司服务", "name": "财务公司服务(J6632)", "isParent": false, "parentcode": "J663"}, {"id": "J6633", "pId": "J663", "singlehyname": "典当", "name": "典当(J6633)", "isParent": false, "parentcode": "J663"}, {"id": "J6634", "pId": "J663", "singlehyname": "汽车金融公司服务", "name": "汽车金融公司服务(J6634)", "isParent": false, "parentcode": "J663"}, {"id": "J6635", "pId": "J663", "singlehyname": "小额贷款公司服务", "name": "小额贷款公司服务(J6635)", "isParent": false, "parentcode": "J663"}, {"id": "J6636", "pId": "J663", "singlehyname": "消费金融公司服务", "name": "消费金融公司服务(J6636)", "isParent": false, "parentcode": "J663"}, {"id": "J6637", "pId": "J663", "singlehyname": "网络借贷服务", "name": "网络借贷服务(J6637)", "isParent": false, "parentcode": "J663"}, {"id": "J6639", "pId": "J663", "singlehyname": "其他非货币银行服务", "name": "其他非货币银行服务(J6639)", "isParent": false, "parentcode": "J663"}, {"id": "J664", "pId": "J66", "singlehyname": "银行理财服务", "name": "银行理财服务(J664)", "isParent": false, "parentcode": "J66"}, {"id": "J665", "pId": "J66", "singlehyname": "银行监管服务", "name": "银行监管服务(J665)", "isParent": false, "parentcode": "J66"}, {"id": "J67", "pId": "J", "singlehyname": "资本市场服务", "name": "资本市场服务(J67)", "isParent": true, "parentcode": "J"}, {"id": "J671", "pId": "J67", "singlehyname": "证券市场服务", "name": "证券市场服务(J671)", "isParent": true, "parentcode": "J67"}, {"id": "J6711", "pId": "J671", "singlehyname": "证券市场管理服务", "name": "证券市场管理服务(J6711)", "isParent": false, "parentcode": "J671"}, {"id": "J6712", "pId": "J671", "singlehyname": "证券经纪交易服务", "name": "证券经纪交易服务(J6712)", "isParent": false, "parentcode": "J671"}, {"id": "J672", "pId": "J67", "singlehyname": "公开募集证券投资基金", "name": "公开募集证券投资基金(J672)", "isParent": true, "parentcode": "J67"}, {"id": "J673", "pId": "J67", "singlehyname": "非公开募集证券投资基金", "name": "非公开募集证券投资基金(J673)", "isParent": true, "parentcode": "J67"}, {"id": "J6731", "pId": "J673", "singlehyname": "创业投资基金", "name": "创业投资基金(J6731)", "isParent": false, "parentcode": "J673"}, {"id": "J6732", "pId": "J673", "singlehyname": "天使投资", "name": "天使投资(J6732)", "isParent": false, "parentcode": "J673"}, {"id": "J6739", "pId": "J673", "singlehyname": "其他非公开募集证券投资基金", "name": "其他非公开募集证券投资基金(J6739)", "isParent": false, "parentcode": "J673"}, {"id": "J674", "pId": "J67", "singlehyname": "期货市场服务", "name": "期货市场服务(J674)", "isParent": false, "parentcode": "J67"}, {"id": "J6741", "pId": "J672", "singlehyname": "期货市场管理服务", "name": "期货市场管理服务(J6741)", "isParent": false, "parentcode": "J672"}, {"id": "J6749", "pId": "J672", "singlehyname": "其他期货市场服务", "name": "其他期货市场服务(J6749)", "isParent": false, "parentcode": "J672"}, {"id": "J675", "pId": "J67", "singlehyname": "证券期货监管服务", "name": "证券期货监管服务(J675)", "isParent": false, "parentcode": "J67"}, {"id": "J676", "pId": "J67", "singlehyname": "资本投资服务", "name": "资本投资服务(J676)", "isParent": false, "parentcode": "J67"}, {"id": "J679", "pId": "J67", "singlehyname": "其他资本市场服务", "name": "其他资本市场服务(J679)", "isParent": false, "parentcode": "J67"}, {"id": "J68", "pId": "J", "singlehyname": "保险业", "name": "保险业(J68)", "isParent": true, "parentcode": "J"}, {"id": "J681", "pId": "J68", "singlehyname": "人身保险", "name": "人身保险(J681)", "isParent": true, "parentcode": "J68"}, {"id": "J6811", "pId": "J681", "singlehyname": "人寿保险", "name": "人寿保险(J6811)", "isParent": false, "parentcode": "J681"}, {"id": "J6812", "pId": "J681", "singlehyname": "年金保险", "name": "年金保险(J6812)", "isParent": false, "parentcode": "J681"}, {"id": "J6813", "pId": "J681", "singlehyname": "健康保险", "name": "健康保险(J6813)", "isParent": false, "parentcode": "J681"}, {"id": "J6814", "pId": "J681", "singlehyname": "意外伤害保险", "name": "意外伤害保险(J6814)", "isParent": false, "parentcode": "J681"}, {"id": "J682", "pId": "J68", "singlehyname": "财产保险", "name": "财产保险(J682)", "isParent": false, "parentcode": "J68"}, {"id": "J683", "pId": "J68", "singlehyname": "再保险", "name": "再保险(J683)", "isParent": false, "parentcode": "J68"}, {"id": "J684", "pId": "J68", "singlehyname": "商业养老金", "name": "商业养老金(J684)", "isParent": false, "parentcode": "J68"}, {"id": "J685", "pId": "J68", "singlehyname": "保险中介服务", "name": "保险中介服务(J685)", "isParent": true, "parentcode": "J68"}, {"id": "J6851", "pId": "J685", "singlehyname": "保险经纪服务", "name": "保险经纪服务(J6851)", "isParent": false, "parentcode": "J685"}, {"id": "J6852", "pId": "J685", "singlehyname": "保险代理服务", "name": "保险代理服务(J6852)", "isParent": false, "parentcode": "J685"}, {"id": "J6853", "pId": "J685", "singlehyname": "保险公估服务", "name": "保险公估服务(J6853)", "isParent": false, "parentcode": "J685"}, {"id": "J686", "pId": "J68", "singlehyname": "保险资产管理", "name": "保险资产管理(J686)", "isParent": false, "parentcode": "J68"}, {"id": "J687", "pId": "J68", "singlehyname": "保险监管服务", "name": "保险监管服务(J687)", "isParent": false, "parentcode": "J68"}, {"id": "J689", "pId": "J68", "singlehyname": "其他保险活动", "name": "其他保险活动(J689)", "isParent": false, "parentcode": "J68"}, {"id": "J69", "pId": "J", "singlehyname": "其他金融业", "name": "其他金融业(J69)", "isParent": true, "parentcode": "J"}, {"id": "J691", "pId": "J69", "singlehyname": "金融信托与管理服务", "name": "金融信托与管理服务(J691)", "isParent": true, "parentcode": "J69"}, {"id": "J6911", "pId": "J691", "singlehyname": "信托公司", "name": "信托公司(J6911)", "isParent": false, "parentcode": "J691"}, {"id": "J6919", "pId": "J691", "singlehyname": "其他金融信托与管理服务", "name": "其他金融信托与管理服务(J6919)", "isParent": false, "parentcode": "J691"}, {"id": "J692", "pId": "J69", "singlehyname": "控股公司服务", "name": "控股公司服务(J692)", "isParent": false, "parentcode": "J69"}, {"id": "J693", "pId": "J69", "singlehyname": "非金融机构支付服务", "name": "非金融机构支付服务(J693)", "isParent": false, "parentcode": "J69"}, {"id": "J694", "pId": "J69", "singlehyname": "金融信息服务", "name": "金融信息服务(J694)", "isParent": false, "parentcode": "J69"}, {"id": "J695", "pId": "J69", "singlehyname": "金融资产管理公司", "name": "金融资产管理公司(J695)", "isParent": false, "parentcode": "J69"}, {"id": "J699", "pId": "J69", "singlehyname": "其他未列明金融业", "name": "其他未列明金融业(J699)", "isParent": true, "parentcode": "J69"}, {"id": "J6991", "pId": "J699", "singlehyname": "货币经纪公司服务", "name": "货币经纪公司服务(J6991)", "isParent": false, "parentcode": "J699"}, {"id": "J6999", "pId": "J699", "singlehyname": "其他未包括金融业", "name": "其他未包括金融业(J6999)", "isParent": false, "parentcode": "J699"}, {"id": "K", "pId": "", "singlehyname": "房地产业", "name": "房地产业(K)", "isParent": true, "parentcode": ""}, {"id": "K70", "pId": "K", "singlehyname": "房地产业", "name": "房地产业(K70)", "isParent": true, "parentcode": "K"}, {"id": "K701", "pId": "K70", "singlehyname": "房地产开发经营", "name": "房地产开发经营(K701)", "isParent": false, "parentcode": "K70"}, {"id": "K702", "pId": "K70", "singlehyname": "物业管理", "name": "物业管理(K702)", "isParent": false, "parentcode": "K70"}, {"id": "K703", "pId": "K70", "singlehyname": "房地产中介服务", "name": "房地产中介服务(K703)", "isParent": false, "parentcode": "K70"}, {"id": "K704", "pId": "K70", "singlehyname": "房地产租赁经营", "name": "房地产租赁经营(K704)", "isParent": false, "parentcode": "K70"}, {"id": "K709", "pId": "K70", "singlehyname": "其他房地产业", "name": "其他房地产业(K709)", "isParent": false, "parentcode": "K70"}, {"id": "L", "pId": "", "singlehyname": "租赁和商务服务业", "name": "租赁和商务服务业(L)", "isParent": true, "parentcode": ""}, {"id": "L71", "pId": "L", "singlehyname": "租赁业", "name": "租赁业(L71)", "isParent": true, "parentcode": "L"}, {"id": "L711", "pId": "L71", "singlehyname": "机械设备租赁", "name": "机械设备租赁(L711)", "isParent": true, "parentcode": "L71"}, {"id": "L7111", "pId": "L711", "singlehyname": "汽车租赁", "name": "汽车租赁(L7111)", "isParent": false, "parentcode": "L711"}, {"id": "L7112", "pId": "L711", "singlehyname": "农业机械经营租赁", "name": "农业机械经营租赁(L7112)", "isParent": false, "parentcode": "L711"}, {"id": "L7113", "pId": "L711", "singlehyname": "建筑工程机械与设备经营租赁", "name": "建筑工程机械与设备经营租赁(L7113)", "isParent": false, "parentcode": "L711"}, {"id": "L7114", "pId": "L711", "singlehyname": "计算机及通讯设备经营租赁", "name": "计算机及通讯设备经营租赁(L7114)", "isParent": false, "parentcode": "L711"}, {"id": "L7115", "pId": "L711", "singlehyname": "医疗设备经营租赁", "name": "医疗设备经营租赁(L7115)", "isParent": false, "parentcode": "L711"}, {"id": "L7119", "pId": "L711", "singlehyname": "其他机械与设备经营租赁", "name": "其他机械与设备经营租赁(L7119)", "isParent": false, "parentcode": "L711"}, {"id": "L712", "pId": "L71", "singlehyname": "文化设备和用品出租", "name": "文化设备和用品出租(L712)", "isParent": true, "parentcode": "L71"}, {"id": "L7121", "pId": "L712", "singlehyname": "休闲娱乐用品设备出租", "name": "休闲娱乐用品设备出租(L7121)", "isParent": false, "parentcode": "L712"}, {"id": "L7122", "pId": "L712", "singlehyname": "体育用品设备出租", "name": "体育用品设备出租(L7122)", "isParent": false, "parentcode": "L712"}, {"id": "L7123", "pId": "L712", "singlehyname": "文化用品设备出租", "name": "文化用品设备出租(L7123)", "isParent": false, "parentcode": "L712"}, {"id": "L7124", "pId": "L712", "singlehyname": "图书出租", "name": "图书出租(L7124)", "isParent": false, "parentcode": "L712"}, {"id": "L7125", "pId": "L712", "singlehyname": "音像制品出租", "name": "音像制品出租(L7125)", "isParent": false, "parentcode": "L712"}, {"id": "L7129", "pId": "L712", "singlehyname": "其他文体设备和用品出租", "name": "其他文体设备和用品出租(L7129)", "isParent": false, "parentcode": "L712"}, {"id": "L72", "pId": "L", "singlehyname": "商务服务业", "name": "商务服务业(L72)", "isParent": true, "parentcode": "L"}, {"id": "L721", "pId": "L72", "singlehyname": "组织管理服务", "name": "组织管理服务(L721)", "isParent": true, "parentcode": "L72"}, {"id": "L7211", "pId": "L721", "singlehyname": "企业总部管理", "name": "企业总部管理(L7211)", "isParent": false, "parentcode": "L721"}, {"id": "L7212", "pId": "L721", "singlehyname": "投资与资产管理", "name": "投资与资产管理(L7212)", "isParent": false, "parentcode": "L721"}, {"id": "L7213", "pId": "L721", "singlehyname": "资源与产权交易服务", "name": "资源与产权交易服务(L7213)", "isParent": false, "parentcode": "L721"}, {"id": "L7214", "pId": "L721", "singlehyname": "单位后勤管理服务", "name": "单位后勤管理服务(L7214)", "isParent": false, "parentcode": "L721"}, {"id": "L7215", "pId": "L721", "singlehyname": "农村集体经济组织管理", "name": "农村集体经济组织管理(L7215)", "isParent": false, "parentcode": "L721"}, {"id": "L7219", "pId": "L721", "singlehyname": "其他组织管理服务", "name": "其他组织管理服务(L7219)", "isParent": false, "parentcode": "L721"}, {"id": "L722", "pId": "L72", "singlehyname": "综合管理服务", "name": "综合管理服务(L722)", "isParent": true, "parentcode": "L72"}, {"id": "L7221", "pId": "L722", "singlehyname": "园区管理服务", "name": "园区管理服务(L7221)", "isParent": false, "parentcode": "L722"}, {"id": "L7222", "pId": "L722", "singlehyname": "商业综合体管理服务", "name": "商业综合体管理服务(L7222)", "isParent": false, "parentcode": "L722"}, {"id": "L7223", "pId": "L722", "singlehyname": "市场管理服务", "name": "市场管理服务(L7223)", "isParent": false, "parentcode": "L722"}, {"id": "L7224", "pId": "L722", "singlehyname": "供应链管理服务", "name": "供应链管理服务(L7224)", "isParent": false, "parentcode": "L722"}, {"id": "L7229", "pId": "L722", "singlehyname": "其他综合管理服务", "name": "其他综合管理服务(L7229)", "isParent": false, "parentcode": "L722"}, {"id": "L723", "pId": "L72", "singlehyname": "法律服务", "name": "法律服务(L723)", "isParent": true, "parentcode": "L72"}, {"id": "L7231", "pId": "L723", "singlehyname": "律师及相关法律服务", "name": "律师及相关法律服务(L7231)", "isParent": false, "parentcode": "L723"}, {"id": "L7232", "pId": "L723", "singlehyname": "公证服务", "name": "公证服务(L7232)", "isParent": false, "parentcode": "L723"}, {"id": "L7239", "pId": "L723", "singlehyname": "其他法律服务", "name": "其他法律服务(L7239)", "isParent": false, "parentcode": "L723"}, {"id": "L724", "pId": "L72", "singlehyname": "咨询与调查", "name": "咨询与调查(L724)", "isParent": true, "parentcode": "L72"}, {"id": "L7241", "pId": "L724", "singlehyname": "会计、审计及税务服务", "name": "会计、审计及税务服务(L7241)", "isParent": false, "parentcode": "L724"}, {"id": "L7242", "pId": "L724", "singlehyname": "市场调查", "name": "市场调查(L7242)", "isParent": false, "parentcode": "L724"}, {"id": "L7243", "pId": "L724", "singlehyname": "社会经济咨询", "name": "社会经济咨询(L7243)", "isParent": false, "parentcode": "L724"}, {"id": "L7244", "pId": "L724", "singlehyname": "健康咨询", "name": "健康咨询(L7244)", "isParent": false, "parentcode": "L724"}, {"id": "L7245", "pId": "L724", "singlehyname": "环保咨询", "name": "环保咨询(L7245)", "isParent": false, "parentcode": "L724"}, {"id": "L7246", "pId": "L724", "singlehyname": "体育咨询", "name": "体育咨询(L7246)", "isParent": false, "parentcode": "L724"}, {"id": "L7249", "pId": "L723", "singlehyname": "其他专业咨询与调查", "name": "其他专业咨询与调查(L7249)", "isParent": false, "parentcode": "L723"}, {"id": "L725", "pId": "L72", "singlehyname": "广告业", "name": "广告业(L725)", "isParent": true, "parentcode": "L72"}, {"id": "L7251", "pId": "L725", "singlehyname": "互联网广告服务", "name": "互联网广告服务(L7251)", "isParent": false, "parentcode": "L725"}, {"id": "L7259", "pId": "L725", "singlehyname": "其他广告服务", "name": "其他广告服务(L7259)", "isParent": false, "parentcode": "L725"}, {"id": "L726", "pId": "L72", "singlehyname": "人力资源服务", "name": "人力资源服务(L726)", "isParent": true, "parentcode": "L72"}, {"id": "L7261", "pId": "L726", "singlehyname": "公共就业服务", "name": "公共就业服务(L7261)", "isParent": false, "parentcode": "L726"}, {"id": "L7262", "pId": "L726", "singlehyname": "职业中介服务", "name": "职业中介服务(L7262)", "isParent": false, "parentcode": "L726"}, {"id": "L7263", "pId": "L726", "singlehyname": "劳务派遣服务", "name": "劳务派遣服务(L7263)", "isParent": false, "parentcode": "L726"}, {"id": "L7264", "pId": "L726", "singlehyname": "创业指导服务", "name": "创业指导服务(L7264)", "isParent": false, "parentcode": "L726"}, {"id": "L7269", "pId": "L726", "singlehyname": "其他人力资源服务", "name": "其他人力资源服务(L7269)", "isParent": false, "parentcode": "L726"}, {"id": "L727", "pId": "L72", "singlehyname": "安全保护服务", "name": "安全保护服务(L727)", "isParent": true, "parentcode": "L72"}, {"id": "L7271", "pId": "L727", "singlehyname": "安全服务", "name": "安全服务(L7271)", "isParent": false, "parentcode": "L727"}, {"id": "L7272", "pId": "L727", "singlehyname": "安全系统监控服务", "name": "安全系统监控服务(L7272)", "isParent": false, "parentcode": "L727"}, {"id": "L7279", "pId": "L727", "singlehyname": "其他安全保护服务", "name": "其他安全保护服务(L7279)", "isParent": false, "parentcode": "L727"}, {"id": "L728", "pId": "L72", "singlehyname": "会议、展览及相关服务", "name": "会议、展览及相关服务(L728)", "isParent": true, "parentcode": "L72"}, {"id": "L7281", "pId": "L728", "singlehyname": "科技会展服务", "name": "科技会展服务(L7281)", "isParent": false, "parentcode": "L728"}, {"id": "L7282", "pId": "L728", "singlehyname": "旅游会展服务", "name": "旅游会展服务(L7282)", "isParent": false, "parentcode": "L728"}, {"id": "L7283", "pId": "L728", "singlehyname": "体育会展服务", "name": "体育会展服务(L7283)", "isParent": false, "parentcode": "L728"}, {"id": "L7284", "pId": "L728", "singlehyname": "文化会展服务", "name": "文化会展服务(L7284)", "isParent": false, "parentcode": "L728"}, {"id": "L729", "pId": "L72", "singlehyname": "其他商务服务业", "name": "其他商务服务业(L729)", "isParent": true, "parentcode": "L72"}, {"id": "L7291", "pId": "L729", "singlehyname": "旅行社及相关服务", "name": "旅行社及相关服务(L7291)", "isParent": false, "parentcode": "L729"}, {"id": "L7292", "pId": "L729", "singlehyname": "包装服务", "name": "包装服务(L7292)", "isParent": false, "parentcode": "L729"}, {"id": "L7293", "pId": "L729", "singlehyname": "办公服务", "name": "办公服务(L7293)", "isParent": false, "parentcode": "L729"}, {"id": "L7294", "pId": "L729", "singlehyname": "翻译服务", "name": "翻译服务(L7294)", "isParent": false, "parentcode": "L729"}, {"id": "L7295", "pId": "L729", "singlehyname": "信用服务", "name": "信用服务(L7295)", "isParent": false, "parentcode": "L729"}, {"id": "L7296", "pId": "L729", "singlehyname": "非融资担保服务", "name": "非融资担保服务(L7296)", "isParent": false, "parentcode": "L729"}, {"id": "L7297", "pId": "L729", "singlehyname": "商务代理代办服务", "name": "商务代理代办服务(L7297)", "isParent": false, "parentcode": "L729"}, {"id": "L7298", "pId": "L729", "singlehyname": "票务代理服务", "name": "票务代理服务(L7298)", "isParent": false, "parentcode": "L729"}, {"id": "L7299", "pId": "L729", "singlehyname": "其他未列明商务服务业", "name": "其他未列明商务服务业(L7299)", "isParent": false, "parentcode": "L729"}, {"id": "L73", "pId": "L", "singlehyname": "日用品出租", "name": "日用品出租(L73)", "isParent": false, "parentcode": "L"}, {"id": "M", "pId": "", "singlehyname": "科学研究和技术服务业", "name": "科学研究和技术服务业(M)", "isParent": true, "parentcode": ""}, {"id": "M73", "pId": "M", "singlehyname": "研究和试验发展", "name": "研究和试验发展(M73)", "isParent": true, "parentcode": "M"}, {"id": "M731", "pId": "M73", "singlehyname": "自然科学研究和试验发展", "name": "自然科学研究和试验发展(M731)", "isParent": false, "parentcode": "M73"}, {"id": "M732", "pId": "M73", "singlehyname": "工程和技术研究和试验发展", "name": "工程和技术研究和试验发展(M732)", "isParent": false, "parentcode": "M73"}, {"id": "M733", "pId": "M73", "singlehyname": "农业科学研究和试验发展", "name": "农业科学研究和试验发展(M733)", "isParent": false, "parentcode": "M73"}, {"id": "M734", "pId": "M73", "singlehyname": "医学研究和试验发展", "name": "医学研究和试验发展(M734)", "isParent": false, "parentcode": "M73"}, {"id": "M735", "pId": "M73", "singlehyname": "社会人文科学研究", "name": "社会人文科学研究(M735)", "isParent": false, "parentcode": "M73"}, {"id": "M74", "pId": "M", "singlehyname": "专业技术服务业", "name": "专业技术服务业(M74)", "isParent": true, "parentcode": "M"}, {"id": "M741", "pId": "M74", "singlehyname": "气象服务", "name": "气象服务(M741)", "isParent": false, "parentcode": "M74"}, {"id": "M742", "pId": "M74", "singlehyname": "地震服务", "name": "地震服务(M742)", "isParent": false, "parentcode": "M74"}, {"id": "M743", "pId": "M74", "singlehyname": "海洋服务", "name": "海洋服务(M743)", "isParent": true, "parentcode": "M74"}, {"id": "M7431", "pId": "M743", "singlehyname": "海洋气象服务", "name": "海洋气象服务(M7431)", "isParent": false, "parentcode": "M743"}, {"id": "M7432", "pId": "M743", "singlehyname": "海洋环境服务", "name": "海洋环境服务(M7432)", "isParent": false, "parentcode": "M743"}, {"id": "M7433", "pId": "M743", "singlehyname": "其他海洋服务", "name": "其他海洋服务(M7433)", "isParent": false, "parentcode": "M743"}, {"id": "M744", "pId": "M74", "singlehyname": "测绘地理信息服务", "name": "测绘地理信息服务(M744)", "isParent": true, "parentcode": "M74"}, {"id": "M7441", "pId": "M744", "singlehyname": "遥感测绘服务", "name": "遥感测绘服务(M7441)", "isParent": false, "parentcode": "M744"}, {"id": "M7449", "pId": "M744", "singlehyname": "其他测绘地理信息服务", "name": "其他测绘地理信息服务(M7449)", "isParent": false, "parentcode": "M744"}, {"id": "M745", "pId": "M74", "singlehyname": "质检技术服务", "name": "质检技术服务(M745)", "isParent": true, "parentcode": "M74"}, {"id": "M7451", "pId": "M745", "singlehyname": "检验检疫服务", "name": "检验检疫服务(M7451)", "isParent": false, "parentcode": "M745"}, {"id": "M7452", "pId": "M745", "singlehyname": "检测服务", "name": "检测服务(M7452)", "isParent": false, "parentcode": "M745"}, {"id": "M7453", "pId": "M745", "singlehyname": "计量服务", "name": "计量服务(M7453)", "isParent": false, "parentcode": "M745"}, {"id": "M7454", "pId": "M745", "singlehyname": "标准化服务", "name": "标准化服务(M7454)", "isParent": false, "parentcode": "M745"}, {"id": "M7455", "pId": "M745", "singlehyname": "认证认可服务", "name": "认证认可服务(M7455)", "isParent": false, "parentcode": "M745"}, {"id": "M7459", "pId": "M745", "singlehyname": "其他质检技术服务", "name": "其他质检技术服务(M7459)", "isParent": false, "parentcode": "M745"}, {"id": "M746", "pId": "M74", "singlehyname": "环境与生态监测检测服务", "name": "环境与生态监测检测服务(M746)", "isParent": true, "parentcode": "M74"}, {"id": "M7461", "pId": "M746", "singlehyname": "环境保护监测", "name": "环境保护监测(M7461)", "isParent": false, "parentcode": "M746"}, {"id": "M7462", "pId": "M746", "singlehyname": "生态资源监测", "name": "生态资源监测(M7462)", "isParent": false, "parentcode": "M746"}, {"id": "M7463", "pId": "M746", "singlehyname": "野生动物疫源疫病防控监测", "name": "野生动物疫源疫病防控监测(M7463)", "isParent": false, "parentcode": "M746"}, {"id": "M747", "pId": "M74", "singlehyname": "地质勘查", "name": "地质勘查(M747)", "isParent": true, "parentcode": "M74"}, {"id": "M7471", "pId": "M747", "singlehyname": "能源矿产地质勘查", "name": "能源矿产地质勘查(M7471)", "isParent": false, "parentcode": "M747"}, {"id": "M7472", "pId": "M747", "singlehyname": "固体矿产地质勘查", "name": "固体矿产地质勘查(M7472)", "isParent": false, "parentcode": "M747"}, {"id": "M7473", "pId": "M747", "singlehyname": "水、二氧化碳等矿产地质勘查", "name": "水、二氧化碳等矿产地质勘查(M7473)", "isParent": false, "parentcode": "M747"}, {"id": "M7474", "pId": "M747", "singlehyname": "基础地质勘查", "name": "基础地质勘查(M7474)", "isParent": false, "parentcode": "M747"}, {"id": "M7475", "pId": "M747", "singlehyname": "地质勘查技术服务", "name": "地质勘查技术服务(M7475)", "isParent": false, "parentcode": "M747"}, {"id": "M748", "pId": "M74", "singlehyname": "工程技术与设计服务", "name": "工程技术与设计服务(M748)", "isParent": true, "parentcode": "M74"}, {"id": "M7481", "pId": "M748", "singlehyname": "工程管理服务", "name": "工程管理服务(M7481)", "isParent": false, "parentcode": "M748"}, {"id": "M7482", "pId": "M748", "singlehyname": "工程监理服务", "name": "工程监理服务(M7482)", "isParent": false, "parentcode": "M748"}, {"id": "M7483", "pId": "M748", "singlehyname": "工程勘察活动", "name": "工程勘察活动(M7483)", "isParent": false, "parentcode": "M748"}, {"id": "M7484", "pId": "M748", "singlehyname": "工程设计活动", "name": "工程设计活动(M7484)", "isParent": false, "parentcode": "M748"}, {"id": "M7485", "pId": "M748", "singlehyname": "规划设计管理", "name": "规划设计管理(M7485)", "isParent": false, "parentcode": "M748"}, {"id": "M7486", "pId": "M748", "singlehyname": "土地规划服务", "name": "土地规划服务(M7486)", "isParent": false, "parentcode": "M748"}, {"id": "M749", "pId": "M74", "singlehyname": "工业与专业设计及其他专业技术服务", "name": "工业与专业设计及其他专业技术服务(M749)", "isParent": true, "parentcode": "M74"}, {"id": "M7491", "pId": "M749", "singlehyname": "工业设计服务", "name": "工业设计服务(M7491)", "isParent": false, "parentcode": "M749"}, {"id": "M7492", "pId": "M749", "singlehyname": "专业设计服务", "name": "专业设计服务(M7492)", "isParent": false, "parentcode": "M749"}, {"id": "M7493", "pId": "M749", "singlehyname": "兽医服务", "name": "兽医服务(M7493)", "isParent": false, "parentcode": "M749"}, {"id": "M7499", "pId": "M749", "singlehyname": "其他未列明专业技术服务业", "name": "其他未列明专业技术服务业(M7499)", "isParent": false, "parentcode": "M749"}, {"id": "M75", "pId": "M", "singlehyname": "科技推广和应用服务业", "name": "科技推广和应用服务业(M75)", "isParent": true, "parentcode": "M"}, {"id": "M751", "pId": "M75", "singlehyname": "技术推广服务", "name": "技术推广服务(M751)", "isParent": true, "parentcode": "M75"}, {"id": "M7511", "pId": "M751", "singlehyname": "农林牧渔技术推广服务", "name": "农林牧渔技术推广服务(M7511)", "isParent": false, "parentcode": "M751"}, {"id": "M7512", "pId": "M751", "singlehyname": "生物技术推广服务", "name": "生物技术推广服务(M7512)", "isParent": false, "parentcode": "M751"}, {"id": "M7513", "pId": "M751", "singlehyname": "新材料技术推广服务", "name": "新材料技术推广服务(M7513)", "isParent": false, "parentcode": "M751"}, {"id": "M7514", "pId": "M751", "singlehyname": "节能技术推广服务", "name": "节能技术推广服务(M7514)", "isParent": false, "parentcode": "M751"}, {"id": "M7515", "pId": "M751", "singlehyname": "新能源技术推广服务", "name": "新能源技术推广服务(M7515)", "isParent": false, "parentcode": "M751"}, {"id": "M7516", "pId": "M751", "singlehyname": "环保技术推广服务", "name": "环保技术推广服务(M7516)", "isParent": false, "parentcode": "M751"}, {"id": "M7517", "pId": "M751", "singlehyname": "三维(3D)打印技术推广服务", "name": "三维(3D)打印技术推广服务(M7517)", "isParent": false, "parentcode": "M751"}, {"id": "M7519", "pId": "M751", "singlehyname": "其他技术推广服务", "name": "其他技术推广服务(M7519)", "isParent": false, "parentcode": "M751"}, {"id": "M752", "pId": "M75", "singlehyname": "知识产权服务", "name": "知识产权服务(M752)", "isParent": false, "parentcode": "M75"}, {"id": "M753", "pId": "M75", "singlehyname": "科技中介服务", "name": "科技中介服务(M753)", "isParent": false, "parentcode": "M75"}, {"id": "M754", "pId": "M75", "singlehyname": "创业空间服务", "name": "创业空间服务(M754)", "isParent": false, "parentcode": "M75"}, {"id": "M759", "pId": "M75", "singlehyname": "其他科技推广服务业", "name": "其他科技推广服务业(M759)", "isParent": false, "parentcode": "M75"}, {"id": "N", "pId": "", "singlehyname": "水利、环境和公共设施管理业", "name": "水利、环境和公共设施管理业(N)", "isParent": true, "parentcode": ""}, {"id": "N76", "pId": "N", "singlehyname": "水利管理业", "name": "水利管理业(N76)", "isParent": true, "parentcode": "N"}, {"id": "N761", "pId": "N76", "singlehyname": "防洪除涝设施管理", "name": "防洪除涝设施管理(N761)", "isParent": false, "parentcode": "N76"}, {"id": "N762", "pId": "N76", "singlehyname": "水资源管理", "name": "水资源管理(N762)", "isParent": false, "parentcode": "N76"}, {"id": "N763", "pId": "N76", "singlehyname": "天然水收集与分配", "name": "天然水收集与分配(N763)", "isParent": false, "parentcode": "N76"}, {"id": "N764", "pId": "N76", "singlehyname": "水文服务", "name": "水文服务(N764)", "isParent": false, "parentcode": "N76"}, {"id": "N769", "pId": "N76", "singlehyname": "其他水利管理业", "name": "其他水利管理业(N769)", "isParent": false, "parentcode": "N76"}, {"id": "N77", "pId": "N", "singlehyname": "生态保护和环境治理业", "name": "生态保护和环境治理业(N77)", "isParent": true, "parentcode": "N"}, {"id": "N771", "pId": "N77", "singlehyname": "生态保护", "name": "生态保护(N771)", "isParent": true, "parentcode": "N77"}, {"id": "N7711", "pId": "N771", "singlehyname": "自然生态系统保护管理", "name": "自然生态系统保护管理(N7711)", "isParent": false, "parentcode": "N771"}, {"id": "N7712", "pId": "N771", "singlehyname": "自然遗迹保护管理", "name": "自然遗迹保护管理(N7712)", "isParent": false, "parentcode": "N771"}, {"id": "N7713", "pId": "N771", "singlehyname": "野生动物保护", "name": "野生动物保护(N7713)", "isParent": false, "parentcode": "N771"}, {"id": "N7714", "pId": "N771", "singlehyname": "野生植物保护", "name": "野生植物保护(N7714)", "isParent": false, "parentcode": "N771"}, {"id": "N7715", "pId": "N771", "singlehyname": "动物园、水族馆管理服务", "name": "动物园、水族馆管理服务(N7715)", "isParent": false, "parentcode": "N771"}, {"id": "N7716", "pId": "N771", "singlehyname": "植物园管理服务", "name": "植物园管理服务(N7716)", "isParent": false, "parentcode": "N771"}, {"id": "N7719", "pId": "N771", "singlehyname": "其他自然保护", "name": "其他自然保护(N7719)", "isParent": false, "parentcode": "N771"}, {"id": "N772", "pId": "N77", "singlehyname": "环境治理业", "name": "环境治理业(N772)", "isParent": true, "parentcode": "N77"}, {"id": "N7721", "pId": "N772", "singlehyname": "水污染治理", "name": "水污染治理(N7721)", "isParent": false, "parentcode": "N772"}, {"id": "N7722", "pId": "N772", "singlehyname": "大气污染治理", "name": "大气污染治理(N7722)", "isParent": false, "parentcode": "N772"}, {"id": "N7723", "pId": "N772", "singlehyname": "固体废物治理", "name": "固体废物治理(N7723)", "isParent": false, "parentcode": "N772"}, {"id": "N7723-1", "pId": "N772", "singlehyname": "固体废物治理-生活垃圾焚烧", "name": "固体废物治理-生活垃圾焚烧(N7723-1)", "isParent": false, "parentcode": "N772"}, {"id": "N7724", "pId": "N772", "singlehyname": "危险废物治理", "name": "危险废物治理(N7724)", "isParent": false, "parentcode": "N772"}, {"id": "N7724-1", "pId": "N772", "singlehyname": "危险废物治理-焚烧", "name": "危险废物治理-焚烧(N7724-1)", "isParent": false, "parentcode": "N772"}, {"id": "N7725", "pId": "N772", "singlehyname": "放射性废物治理", "name": "放射性废物治理(N7725)", "isParent": false, "parentcode": "N772"}, {"id": "N7726", "pId": "N772", "singlehyname": "土壤污染治理与修复服务", "name": "土壤污染治理与修复服务(N7726)", "isParent": false, "parentcode": "N772"}, {"id": "N7727", "pId": "N772", "singlehyname": "噪声与振动控制服务", "name": "噪声与振动控制服务(N7727)", "isParent": false, "parentcode": "N772"}, {"id": "N7729", "pId": "N772", "singlehyname": "其他污染治理", "name": "其他污染治理(N7729)", "isParent": false, "parentcode": "N772"}, {"id": "N78", "pId": "N", "singlehyname": "公共设施管理业", "name": "公共设施管理业(N78)", "isParent": true, "parentcode": "N"}, {"id": "N781", "pId": "N78", "singlehyname": "市政设施管理", "name": "市政设施管理(N781)", "isParent": false, "parentcode": "N78"}, {"id": "N782", "pId": "N78", "singlehyname": "环境卫生管理", "name": "环境卫生管理(N782)", "isParent": false, "parentcode": "N78"}, {"id": "N782-1", "pId": "N78", "singlehyname": "环境卫生管理-垃圾焚烧", "name": "环境卫生管理-垃圾焚烧(N782-1)", "isParent": false, "parentcode": "N78"}, {"id": "N783", "pId": "N78", "singlehyname": "城乡市容管理", "name": "城乡市容管理(N783)", "isParent": false, "parentcode": "N78"}, {"id": "N784", "pId": "N78", "singlehyname": "绿化管理", "name": "绿化管理(N784)", "isParent": false, "parentcode": "N78"}, {"id": "N785", "pId": "N78", "singlehyname": "城市公园管理", "name": "城市公园管理(N785)", "isParent": false, "parentcode": "N78"}, {"id": "N786", "pId": "N78", "singlehyname": "游览景区管理", "name": "游览景区管理(N786)", "isParent": true, "parentcode": "N78"}, {"id": "N7861", "pId": "N786", "singlehyname": "名胜风景区管理", "name": "名胜风景区管理(N7861)", "isParent": false, "parentcode": "N786"}, {"id": "N7862", "pId": "N786", "singlehyname": "森林公园管理", "name": "森林公园管理(N7862)", "isParent": false, "parentcode": "N786"}, {"id": "N7869", "pId": "N786", "singlehyname": "其他游览景区管理", "name": "其他游览景区管理(N7869)", "isParent": false, "parentcode": "N786"}, {"id": "N79", "pId": "N", "singlehyname": "土地管理业", "name": "土地管理业(N79)", "isParent": true, "parentcode": "N"}, {"id": "N791", "pId": "N79", "singlehyname": "土地整治服务", "name": "土地整治服务(N791)", "isParent": false, "parentcode": "N79"}, {"id": "N792", "pId": "N79", "singlehyname": "土地调查评估服务", "name": "土地调查评估服务(N792)", "isParent": false, "parentcode": "N79"}, {"id": "N793", "pId": "N79", "singlehyname": "土地登记服务", "name": "土地登记服务(N793)", "isParent": false, "parentcode": "N79"}, {"id": "N794", "pId": "N79", "singlehyname": "土地登记代理服务", "name": "土地登记代理服务(N794)", "isParent": false, "parentcode": "N79"}, {"id": "N799", "pId": "N79", "singlehyname": "其他土地管理服务", "name": "其他土地管理服务(N799)", "isParent": false, "parentcode": "N79"}, {"id": "O", "pId": "", "singlehyname": "居民服务、修理和其他服务业", "name": "居民服务、修理和其他服务业(O)", "isParent": true, "parentcode": ""}, {"id": "O80", "pId": "O", "singlehyname": "居民服务业", "name": "居民服务业(O80)", "isParent": true, "parentcode": "O"}, {"id": "O801", "pId": "O80", "singlehyname": "家庭服务", "name": "家庭服务(O801)", "isParent": false, "parentcode": "O80"}, {"id": "O802", "pId": "O80", "singlehyname": "托儿所服务", "name": "托儿所服务(O802)", "isParent": false, "parentcode": "O80"}, {"id": "O803", "pId": "O80", "singlehyname": "洗染服务", "name": "洗染服务(O803)", "isParent": false, "parentcode": "O80"}, {"id": "O804", "pId": "O80", "singlehyname": "理发及美容服务", "name": "理发及美容服务(O804)", "isParent": false, "parentcode": "O80"}, {"id": "O805", "pId": "O80", "singlehyname": "洗浴和保健养生服务", "name": "洗浴和保健养生服务(O805)", "isParent": true, "parentcode": "O80"}, {"id": "O8051", "pId": "O805", "singlehyname": "洗浴服务", "name": "洗浴服务(O8051)", "isParent": false, "parentcode": "O805"}, {"id": "O8052", "pId": "O805", "singlehyname": "足浴服务", "name": "足浴服务(O8052)", "isParent": false, "parentcode": "O805"}, {"id": "O8053", "pId": "O805", "singlehyname": "养生保健服务", "name": "养生保健服务(O8053)", "isParent": false, "parentcode": "O805"}, {"id": "O806", "pId": "O80", "singlehyname": "摄影扩印服务", "name": "摄影扩印服务(O806)", "isParent": false, "parentcode": "O80"}, {"id": "O807", "pId": "O80", "singlehyname": "婚姻服务", "name": "婚姻服务(O807)", "isParent": false, "parentcode": "O80"}, {"id": "O808", "pId": "O80", "singlehyname": "殡葬服务", "name": "殡葬服务(O808)", "isParent": false, "parentcode": "O80"}, {"id": "O809", "pId": "O80", "singlehyname": "其他居民服务业", "name": "其他居民服务业(O809)", "isParent": false, "parentcode": "O80"}, {"id": "O81", "pId": "O", "singlehyname": "机动车、电子产品和日用产品修理业", "name": "机动车、电子产品和日用产品修理业(O81)", "isParent": true, "parentcode": "O"}, {"id": "O811", "pId": "O81", "singlehyname": "汽车、摩托车等修理与维护", "name": "汽车、摩托车等修理与维护(O811)", "isParent": true, "parentcode": "O81"}, {"id": "O8111", "pId": "O811", "singlehyname": "汽车修理与维护", "name": "汽车修理与维护(O8111)", "isParent": false, "parentcode": "O811"}, {"id": "O8112", "pId": "O811", "singlehyname": "大型车辆装备修理与维护", "name": "大型车辆装备修理与维护(O8112)", "isParent": false, "parentcode": "O811"}, {"id": "O8113", "pId": "O811", "singlehyname": "摩托车修理与维护", "name": "摩托车修理与维护(O8113)", "isParent": false, "parentcode": "O811"}, {"id": "O8114", "pId": "O811", "singlehyname": "助动车等修理与维护", "name": "助动车等修理与维护(O8114)", "isParent": false, "parentcode": "O811"}, {"id": "O812", "pId": "O81", "singlehyname": "计算机和办公设备维修", "name": "计算机和办公设备维修(O812)", "isParent": true, "parentcode": "O81"}, {"id": "O8121", "pId": "O812", "singlehyname": "计算机和辅助设备修理", "name": "计算机和辅助设备修理(O8121)", "isParent": false, "parentcode": "O812"}, {"id": "O8122", "pId": "O812", "singlehyname": "通讯设备修理", "name": "通讯设备修理(O8122)", "isParent": false, "parentcode": "O812"}, {"id": "O8129", "pId": "O812", "singlehyname": "其他办公设备维修", "name": "其他办公设备维修(O8129)", "isParent": false, "parentcode": "O812"}, {"id": "O813", "pId": "O81", "singlehyname": "家用电器修理", "name": "家用电器修理(O813)", "isParent": true, "parentcode": "O81"}, {"id": "O8131", "pId": "O813", "singlehyname": "家用电子产品修理", "name": "家用电子产品修理(O8131)", "isParent": false, "parentcode": "O813"}, {"id": "O8132", "pId": "O813", "singlehyname": "日用电器修理", "name": "日用电器修理(O8132)", "isParent": false, "parentcode": "O813"}, {"id": "O819", "pId": "O81", "singlehyname": "其他日用产品修理业", "name": "其他日用产品修理业(O819)", "isParent": true, "parentcode": "O81"}, {"id": "O8191", "pId": "O819", "singlehyname": "自行车修理", "name": "自行车修理(O8191)", "isParent": false, "parentcode": "O819"}, {"id": "O8192", "pId": "O819", "singlehyname": "鞋和皮革修理", "name": "鞋和皮革修理(O8192)", "isParent": false, "parentcode": "O819"}, {"id": "O8193", "pId": "O819", "singlehyname": "家具和相关物品修理", "name": "家具和相关物品修理(O8193)", "isParent": false, "parentcode": "O819"}, {"id": "O8199", "pId": "O819", "singlehyname": "其他未列明日用产品修理业", "name": "其他未列明日用产品修理业(O8199)", "isParent": false, "parentcode": "O819"}, {"id": "O82", "pId": "O", "singlehyname": "其他服务业", "name": "其他服务业(O82)", "isParent": true, "parentcode": "O"}, {"id": "O821", "pId": "O82", "singlehyname": "清洁服务", "name": "清洁服务(O821)", "isParent": true, "parentcode": "O82"}, {"id": "O8211", "pId": "O821", "singlehyname": "建筑物清洁服务", "name": "建筑物清洁服务(O8211)", "isParent": false, "parentcode": "O821"}, {"id": "O8219", "pId": "O821", "singlehyname": "其他清洁服务", "name": "其他清洁服务(O8219)", "isParent": false, "parentcode": "O821"}, {"id": "O822", "pId": "O82", "singlehyname": "宠物服务", "name": "宠物服务(O822)", "isParent": true, "parentcode": "O82"}, {"id": "O8221", "pId": "O822", "singlehyname": "宠物饲养", "name": "宠物饲养(O8221)", "isParent": false, "parentcode": "O822"}, {"id": "O8222", "pId": "O822", "singlehyname": "宠物医院服务", "name": "宠物医院服务(O8222)", "isParent": false, "parentcode": "O822"}, {"id": "O8223", "pId": "O822", "singlehyname": "宠物美容服务", "name": "宠物美容服务(O8223)", "isParent": false, "parentcode": "O822"}, {"id": "O8224", "pId": "O822", "singlehyname": "宠物寄托收养服务", "name": "宠物寄托收养服务(O8224)", "isParent": false, "parentcode": "O822"}, {"id": "O8229", "pId": "O822", "singlehyname": "其他宠物服务", "name": "其他宠物服务(O8229)", "isParent": false, "parentcode": "O822"}, {"id": "O829", "pId": "O82", "singlehyname": "其他未列明服务业", "name": "其他未列明服务业(O829)", "isParent": false, "parentcode": "O82"}, {"id": "P", "pId": "", "singlehyname": "教育", "name": "教育(P)", "isParent": true, "parentcode": ""}, {"id": "P83", "pId": "P", "singlehyname": "教育", "name": "教育(P83)", "isParent": true, "parentcode": "P"}, {"id": "P831", "pId": "P83", "singlehyname": "学前教育", "name": "学前教育(P831)", "isParent": false, "parentcode": "P83"}, {"id": "P832", "pId": "P83", "singlehyname": "初等教育", "name": "初等教育(P832)", "isParent": true, "parentcode": "P83"}, {"id": "P8321", "pId": "P832", "singlehyname": "普通小学教育", "name": "普通小学教育(P8321)", "isParent": false, "parentcode": "P832"}, {"id": "P8322", "pId": "P832", "singlehyname": "成人小学教育", "name": "成人小学教育(P8322)", "isParent": false, "parentcode": "P832"}, {"id": "P833", "pId": "P83", "singlehyname": "中等教育", "name": "中等教育(P833)", "isParent": true, "parentcode": "P83"}, {"id": "P8331", "pId": "P833", "singlehyname": "普通初中教育", "name": "普通初中教育(P8331)", "isParent": false, "parentcode": "P833"}, {"id": "P8332", "pId": "P833", "singlehyname": "职业初中教育", "name": "职业初中教育(P8332)", "isParent": false, "parentcode": "P833"}, {"id": "P8333", "pId": "P833", "singlehyname": "成人初中教育", "name": "成人初中教育(P8333)", "isParent": false, "parentcode": "P833"}, {"id": "P8334", "pId": "P833", "singlehyname": "普通高中教育", "name": "普通高中教育(P8334)", "isParent": false, "parentcode": "P833"}, {"id": "P8335", "pId": "P833", "singlehyname": "成人高中教育", "name": "成人高中教育(P8335)", "isParent": false, "parentcode": "P833"}, {"id": "P8336", "pId": "P833", "singlehyname": "中等职业学校教育", "name": "中等职业学校教育(P8336)", "isParent": false, "parentcode": "P833"}, {"id": "P834", "pId": "P83", "singlehyname": "高等教育", "name": "高等教育(P834)", "isParent": true, "parentcode": "P83"}, {"id": "P8341", "pId": "P834", "singlehyname": "普通高等教育", "name": "普通高等教育(P8341)", "isParent": false, "parentcode": "P834"}, {"id": "P8342", "pId": "P834", "singlehyname": "成人高等教育", "name": "成人高等教育(P8342)", "isParent": false, "parentcode": "P834"}, {"id": "P835", "pId": "P83", "singlehyname": "特殊教育", "name": "特殊教育(P835)", "isParent": false, "parentcode": "P83"}, {"id": "P839", "pId": "P83", "singlehyname": "技能培训、教育辅助及其他教育", "name": "技能培训、教育辅助及其他教育(P839)", "isParent": true, "parentcode": "P83"}, {"id": "P8391", "pId": "P839", "singlehyname": "职业技能培训", "name": "职业技能培训(P8391)", "isParent": false, "parentcode": "P839"}, {"id": "P8392", "pId": "P839", "singlehyname": "体校及体育培训", "name": "体校及体育培训(P8392)", "isParent": false, "parentcode": "P839"}, {"id": "P8393", "pId": "P839", "singlehyname": "文化艺术培训", "name": "文化艺术培训(P8393)", "isParent": false, "parentcode": "P839"}, {"id": "P8394", "pId": "P839", "singlehyname": "教育辅助服务", "name": "教育辅助服务(P8394)", "isParent": false, "parentcode": "P839"}, {"id": "P8399", "pId": "P839", "singlehyname": "其他未列明教育", "name": "其他未列明教育(P8399)", "isParent": false, "parentcode": "P839"}, {"id": "Q", "pId": "", "singlehyname": "卫生和社会工作", "name": "卫生和社会工作(Q)", "isParent": true, "parentcode": ""}, {"id": "Q84", "pId": "Q", "singlehyname": "卫生", "name": "卫生(Q84)", "isParent": true, "parentcode": "Q"}, {"id": "Q841", "pId": "Q84", "singlehyname": "医院", "name": "医院(Q841)", "isParent": true, "parentcode": "Q84"}, {"id": "Q8411", "pId": "Q841", "singlehyname": "综合医院", "name": "综合医院(Q8411)", "isParent": false, "parentcode": "Q841"}, {"id": "Q8412", "pId": "Q841", "singlehyname": "中医医院", "name": "中医医院(Q8412)", "isParent": false, "parentcode": "Q841"}, {"id": "Q8413", "pId": "Q841", "singlehyname": "中西医结合医院", "name": "中西医结合医院(Q8413)", "isParent": false, "parentcode": "Q841"}, {"id": "Q8414", "pId": "Q841", "singlehyname": "民族医院", "name": "民族医院(Q8414)", "isParent": false, "parentcode": "Q841"}, {"id": "Q8415", "pId": "Q841", "singlehyname": "专科医院", "name": "专科医院(Q8415)", "isParent": false, "parentcode": "Q841"}, {"id": "Q8416", "pId": "Q841", "singlehyname": "疗养院", "name": "疗养院(Q8416)", "isParent": false, "parentcode": "Q841"}, {"id": "Q842", "pId": "Q84", "singlehyname": "基层医疗卫生服务", "name": "基层医疗卫生服务(Q842)", "isParent": true, "parentcode": "Q84"}, {"id": "Q8421", "pId": "Q842", "singlehyname": "社区卫生服务中心(站)", "name": "社区卫生服务中心(站)(Q8421)", "isParent": false, "parentcode": "Q842"}, {"id": "Q8422", "pId": "Q842", "singlehyname": "街道卫生院", "name": "街道卫生院(Q8422)", "isParent": false, "parentcode": "Q842"}, {"id": "Q8423", "pId": "Q842", "singlehyname": "乡镇卫生院", "name": "乡镇卫生院(Q8423)", "isParent": false, "parentcode": "Q842"}, {"id": "Q8424", "pId": "Q842", "singlehyname": "村卫生室", "name": "村卫生室(Q8424)", "isParent": false, "parentcode": "Q842"}, {"id": "Q8425", "pId": "Q842", "singlehyname": "门诊部(所)", "name": "门诊部(所)(Q8425)", "isParent": false, "parentcode": "Q842"}, {"id": "Q843", "pId": "Q84", "singlehyname": "专业公共卫生服务", "name": "专业公共卫生服务(Q843)", "isParent": true, "parentcode": "Q84"}, {"id": "Q8431", "pId": "Q843", "singlehyname": "疾病预防控制中心", "name": "疾病预防控制中心(Q8431)", "isParent": false, "parentcode": "Q843"}, {"id": "Q8432", "pId": "Q843", "singlehyname": "专科疾病防治院(所、站)", "name": "专科疾病防治院(所、站)(Q8432)", "isParent": false, "parentcode": "Q843"}, {"id": "Q8433", "pId": "Q843", "singlehyname": "妇幼保健院(所、站)", "name": "妇幼保健院(所、站)(Q8433)", "isParent": false, "parentcode": "Q843"}, {"id": "Q8434", "pId": "Q843", "singlehyname": "急救中心(站)服务", "name": "急救中心(站)服务(Q8434)", "isParent": false, "parentcode": "Q843"}, {"id": "Q8435", "pId": "Q843", "singlehyname": "采供血机构服务", "name": "采供血机构服务(Q8435)", "isParent": false, "parentcode": "Q843"}, {"id": "Q8436", "pId": "Q843", "singlehyname": "计划生育技术服务活动", "name": "计划生育技术服务活动(Q8436)", "isParent": false, "parentcode": "Q843"}, {"id": "Q849", "pId": "Q84", "singlehyname": "其他卫生活动", "name": "其他卫生活动(Q849)", "isParent": true, "parentcode": "Q84"}, {"id": "Q8491", "pId": "Q849", "singlehyname": "健康体检服务", "name": "健康体检服务(Q8491)", "isParent": false, "parentcode": "Q849"}, {"id": "Q8492", "pId": "Q849", "singlehyname": "临床检验服务", "name": "临床检验服务(Q8492)", "isParent": false, "parentcode": "Q849"}, {"id": "Q8499", "pId": "Q849", "singlehyname": "其他未列明卫生服务", "name": "其他未列明卫生服务(Q8499)", "isParent": false, "parentcode": "Q849"}, {"id": "Q85", "pId": "Q", "singlehyname": "社会工作", "name": "社会工作(Q85)", "isParent": true, "parentcode": "Q"}, {"id": "Q851", "pId": "Q85", "singlehyname": "提供住宿社会工作", "name": "提供住宿社会工作(Q851)", "isParent": true, "parentcode": "Q85"}, {"id": "Q8511", "pId": "Q851", "singlehyname": "干部休养所", "name": "干部休养所(Q8511)", "isParent": false, "parentcode": "Q851"}, {"id": "Q8512", "pId": "Q851", "singlehyname": "护理机构服务", "name": "护理机构服务(Q8512)", "isParent": false, "parentcode": "Q851"}, {"id": "Q8513", "pId": "Q851", "singlehyname": "精神康复服务", "name": "精神康复服务(Q8513)", "isParent": false, "parentcode": "Q851"}, {"id": "Q8514", "pId": "Q851", "singlehyname": "老年人、残疾人养护服务", "name": "老年人、残疾人养护服务(Q8514)", "isParent": false, "parentcode": "Q851"}, {"id": "Q8515", "pId": "Q851", "singlehyname": "临终关怀服务", "name": "临终关怀服务(Q8515)", "isParent": false, "parentcode": "Q851"}, {"id": "Q8516", "pId": "Q851", "singlehyname": "孤残儿童收养和庇护服务", "name": "孤残儿童收养和庇护服务(Q8516)", "isParent": false, "parentcode": "Q851"}, {"id": "Q8519", "pId": "Q851", "singlehyname": "其他提供住宿社会救助", "name": "其他提供住宿社会救助(Q8519)", "isParent": false, "parentcode": "Q851"}, {"id": "Q852", "pId": "Q85", "singlehyname": "不提供住宿社会工作", "name": "不提供住宿社会工作(Q852)", "isParent": true, "parentcode": "Q85"}, {"id": "Q8521", "pId": "Q852", "singlehyname": "社会看护与帮助服务", "name": "社会看护与帮助服务(Q8521)", "isParent": false, "parentcode": "Q852"}, {"id": "Q8522", "pId": "Q852", "singlehyname": "康复辅具适配服务", "name": "康复辅具适配服务(Q8522)", "isParent": false, "parentcode": "Q852"}, {"id": "Q8529", "pId": "Q852", "singlehyname": "其他不提供住宿社会工作", "name": "其他不提供住宿社会工作(Q8529)", "isParent": false, "parentcode": "Q852"}, {"id": "R", "pId": "", "singlehyname": "文化、体育和娱乐业", "name": "文化、体育和娱乐业(R)", "isParent": true, "parentcode": ""}, {"id": "R86", "pId": "R", "singlehyname": "新闻和出版业", "name": "新闻和出版业(R86)", "isParent": true, "parentcode": "R"}, {"id": "R861", "pId": "R86", "singlehyname": "新闻业", "name": "新闻业(R861)", "isParent": false, "parentcode": "R86"}, {"id": "R862", "pId": "R86", "singlehyname": "出版业", "name": "出版业(R862)", "isParent": true, "parentcode": "R86"}, {"id": "R8621", "pId": "R862", "singlehyname": "图书出版", "name": "图书出版(R8621)", "isParent": false, "parentcode": "R862"}, {"id": "R8622", "pId": "R862", "singlehyname": "报纸出版", "name": "报纸出版(R8622)", "isParent": false, "parentcode": "R862"}, {"id": "R8623", "pId": "R862", "singlehyname": "期刊出版", "name": "期刊出版(R8623)", "isParent": false, "parentcode": "R862"}, {"id": "R8624", "pId": "R862", "singlehyname": "音像制品出版", "name": "音像制品出版(R8624)", "isParent": false, "parentcode": "R862"}, {"id": "R8625", "pId": "R862", "singlehyname": "电子出版物出版", "name": "电子出版物出版(R8625)", "isParent": false, "parentcode": "R862"}, {"id": "R8626", "pId": "R862", "singlehyname": "数字出版", "name": "数字出版(R8626)", "isParent": false, "parentcode": "R862"}, {"id": "R8629", "pId": "R862", "singlehyname": "其他出版业", "name": "其他出版业(R8629)", "isParent": false, "parentcode": "R862"}, {"id": "R87", "pId": "R", "singlehyname": "广播、电视、电影和录音制作业", "name": "广播、电视、电影和录音制作业(R87)", "isParent": true, "parentcode": "R"}, {"id": "R871", "pId": "R87", "singlehyname": "广播", "name": "广播(R871)", "isParent": false, "parentcode": "R87"}, {"id": "R872", "pId": "R87", "singlehyname": "电视", "name": "电视(R872)", "isParent": false, "parentcode": "R87"}, {"id": "R873", "pId": "R87", "singlehyname": "影视节目制作", "name": "影视节目制作(R873)", "isParent": false, "parentcode": "R87"}, {"id": "R874", "pId": "R87", "singlehyname": "园林绿化工程施工", "name": "园林绿化工程施工(R874)", "isParent": false, "parentcode": "R87"}, {"id": "R875", "pId": "R87", "singlehyname": "电影和广播电视节目发行", "name": "电影和广播电视节目发行(R875)", "isParent": false, "parentcode": "R87"}, {"id": "R876", "pId": "R87", "singlehyname": "电影放映", "name": "电影放映(R876)", "isParent": false, "parentcode": "R87"}, {"id": "R877", "pId": "R87", "singlehyname": "录音制作", "name": "录音制作(R877)", "isParent": false, "parentcode": "R87"}, {"id": "R88", "pId": "R", "singlehyname": "文化艺术业", "name": "文化艺术业(R88)", "isParent": true, "parentcode": "R"}, {"id": "R881", "pId": "R88", "singlehyname": "文艺创作与表演", "name": "文艺创作与表演(R881)", "isParent": false, "parentcode": "R88"}, {"id": "R882", "pId": "R88", "singlehyname": "艺术表演场馆", "name": "艺术表演场馆(R882)", "isParent": false, "parentcode": "R88"}, {"id": "R883", "pId": "R88", "singlehyname": "图书馆与档案馆", "name": "图书馆与档案馆(R883)", "isParent": true, "parentcode": "R88"}, {"id": "R8831", "pId": "R883", "singlehyname": "图书馆", "name": "图书馆(R8831)", "isParent": false, "parentcode": "R883"}, {"id": "R8832", "pId": "R883", "singlehyname": "档案馆", "name": "档案馆(R8832)", "isParent": false, "parentcode": "R883"}, {"id": "R884", "pId": "R88", "singlehyname": "文物及非物质文化遗产保护", "name": "文物及非物质文化遗产保护(R884)", "isParent": false, "parentcode": "R88"}, {"id": "R884", "pId": "R88", "singlehyname": "广播电视集成播控", "name": "广播电视集成播控(R884)", "isParent": false, "parentcode": "R88"}, {"id": "R885", "pId": "R88", "singlehyname": "博物馆", "name": "博物馆(R885)", "isParent": false, "parentcode": "R88"}, {"id": "R886", "pId": "R88", "singlehyname": "烈士陵园、纪念馆", "name": "烈士陵园、纪念馆(R886)", "isParent": false, "parentcode": "R88"}, {"id": "R887", "pId": "R88", "singlehyname": "群众文体活动", "name": "群众文体活动(R887)", "isParent": false, "parentcode": "R88"}, {"id": "R889", "pId": "R88", "singlehyname": "其他文化艺术业", "name": "其他文化艺术业(R889)", "isParent": false, "parentcode": "R88"}, {"id": "R89", "pId": "R", "singlehyname": "体育", "name": "体育(R89)", "isParent": true, "parentcode": "R"}, {"id": "R891", "pId": "R89", "singlehyname": "体育组织", "name": "体育组织(R891)", "isParent": true, "parentcode": "R89"}, {"id": "R8911", "pId": "R891", "singlehyname": "体育竞赛组织", "name": "体育竞赛组织(R8911)", "isParent": false, "parentcode": "R891"}, {"id": "R8912", "pId": "R891", "singlehyname": "体育保障组织", "name": "体育保障组织(R8912)", "isParent": false, "parentcode": "R891"}, {"id": "R8919", "pId": "R891", "singlehyname": "其他体育组织", "name": "其他体育组织(R8919)", "isParent": false, "parentcode": "R891"}, {"id": "R892", "pId": "R89", "singlehyname": "体育场地设施管理", "name": "体育场地设施管理(R892)", "isParent": true, "parentcode": "R89"}, {"id": "R8921", "pId": "R892", "singlehyname": "体育场馆管理", "name": "体育场馆管理(R8921)", "isParent": false, "parentcode": "R892"}, {"id": "R8929", "pId": "R892", "singlehyname": "其他体育场地设施管理", "name": "其他体育场地设施管理(R8929)", "isParent": false, "parentcode": "R892"}, {"id": "R893", "pId": "R89", "singlehyname": "健身休闲活动", "name": "健身休闲活动(R893)", "isParent": false, "parentcode": "R89"}, {"id": "R899", "pId": "R89", "singlehyname": "其他文化艺术业", "name": "其他文化艺术业(R899)", "isParent": true, "parentcode": "R89"}, {"id": "R8991", "pId": "R899", "singlehyname": "体育中介代理服务", "name": "体育中介代理服务(R8991)", "isParent": false, "parentcode": "R899"}, {"id": "R8992", "pId": "R899", "singlehyname": "体育健康服务", "name": "体育健康服务(R8992)", "isParent": false, "parentcode": "R899"}, {"id": "R8999", "pId": "R899", "singlehyname": "其他未列明体育", "name": "其他未列明体育(R8999)", "isParent": false, "parentcode": "R899"}, {"id": "R90", "pId": "R", "singlehyname": "娱乐业", "name": "娱乐业(R90)", "isParent": true, "parentcode": "R"}, {"id": "R901", "pId": "R90", "singlehyname": "室内娱乐活动", "name": "室内娱乐活动(R901)", "isParent": true, "parentcode": "R90"}, {"id": "R9011", "pId": "R901", "singlehyname": "歌舞厅娱乐活动", "name": "歌舞厅娱乐活动(R9011)", "isParent": false, "parentcode": "R901"}, {"id": "R9012", "pId": "R901", "singlehyname": "电子游艺厅娱乐活动", "name": "电子游艺厅娱乐活动(R9012)", "isParent": false, "parentcode": "R901"}, {"id": "R9013", "pId": "R901", "singlehyname": "网吧活动", "name": "网吧活动(R9013)", "isParent": false, "parentcode": "R901"}, {"id": "R9019", "pId": "R901", "singlehyname": "其他室内娱乐活动", "name": "其他室内娱乐活动(R9019)", "isParent": false, "parentcode": "R901"}, {"id": "R902", "pId": "R90", "singlehyname": "游乐园", "name": "游乐园(R902)", "isParent": false, "parentcode": "R90"}, {"id": "R903", "pId": "R90", "singlehyname": "休闲观光活动", "name": "休闲观光活动(R903)", "isParent": false, "parentcode": "R90"}, {"id": "R904", "pId": "R90", "singlehyname": "彩票活动", "name": "彩票活动(R904)", "isParent": true, "parentcode": "R90"}, {"id": "R9041", "pId": "R904", "singlehyname": "体育彩票服务", "name": "体育彩票服务(R9041)", "isParent": false, "parentcode": "R904"}, {"id": "R9042", "pId": "R904", "singlehyname": "体育彩票服务", "name": "体育彩票服务(R9042)", "isParent": false, "parentcode": "R904"}, {"id": "R9049", "pId": "R904", "singlehyname": "其他彩票服务", "name": "其他彩票服务(R9049)", "isParent": false, "parentcode": "R904"}, {"id": "R905", "pId": "R90", "singlehyname": "文化娱乐体育活动和经纪代理服务", "name": "文化娱乐体育活动和经纪代理服务(R905)", "isParent": true, "parentcode": "R90"}, {"id": "R9051", "pId": "R905", "singlehyname": "文化活动服务", "name": "文化活动服务(R9051)", "isParent": false, "parentcode": "R905"}, {"id": "R9052", "pId": "R905", "singlehyname": "体育表演服务", "name": "体育表演服务(R9052)", "isParent": false, "parentcode": "R905"}, {"id": "R9053", "pId": "R905", "singlehyname": "文化娱乐经纪人", "name": "文化娱乐经纪人(R9053)", "isParent": false, "parentcode": "R905"}, {"id": "R9054", "pId": "R905", "singlehyname": "体育经纪人", "name": "体育经纪人(R9054)", "isParent": false, "parentcode": "R905"}, {"id": "R9059", "pId": "R905", "singlehyname": "其他文化艺术经纪代理", "name": "其他文化艺术经纪代理(R9059)", "isParent": false, "parentcode": "R905"}, {"id": "R9090", "pId": "R90", "singlehyname": "其他娱乐业", "name": "其他娱乐业(R9090)", "isParent": false, "parentcode": "R90"}, {"id": "S", "pId": "", "singlehyname": "公共管理、社会保障和社会组织", "name": "公共管理、社会保障和社会组织(S)", "isParent": true, "parentcode": ""}, {"id": "S91", "pId": "S", "singlehyname": "中国共产党机关", "name": "中国共产党机关(S91)", "isParent": true, "parentcode": "S"}, {"id": "S9100", "pId": "S91", "singlehyname": "中国共产党机关", "name": "中国共产党机关(S9100)", "isParent": false, "parentcode": "S91"}, {"id": "S92", "pId": "S", "singlehyname": "国家机构", "name": "国家机构(S92)", "isParent": true, "parentcode": "S"}, {"id": "S921", "pId": "S92", "singlehyname": "国家权力机构", "name": "国家权力机构(S921)", "isParent": false, "parentcode": "S92"}, {"id": "S922", "pId": "S92", "singlehyname": "国家行政机构", "name": "国家行政机构(S922)", "isParent": true, "parentcode": "S92"}, {"id": "S9221", "pId": "S922", "singlehyname": "综合事务管理机构", "name": "综合事务管理机构(S9221)", "isParent": false, "parentcode": "S922"}, {"id": "S9222", "pId": "S922", "singlehyname": "对外事务管理机构", "name": "对外事务管理机构(S9222)", "isParent": false, "parentcode": "S922"}, {"id": "S9223", "pId": "S922", "singlehyname": "公共安全管理机构", "name": "公共安全管理机构(S9223)", "isParent": false, "parentcode": "S922"}, {"id": "S9224", "pId": "S922", "singlehyname": "社会事务管理机构", "name": "社会事务管理机构(S9224)", "isParent": false, "parentcode": "S922"}, {"id": "S9225", "pId": "S922", "singlehyname": "经济事务管理机构", "name": "经济事务管理机构(S9225)", "isParent": false, "parentcode": "S922"}, {"id": "S9226", "pId": "S922", "singlehyname": "行政监督检查机构", "name": "行政监督检查机构(S9226)", "isParent": false, "parentcode": "S922"}, {"id": "S923", "pId": "S92", "singlehyname": "人民法院和人民检察院", "name": "人民法院和人民检察院(S923)", "isParent": true, "parentcode": "S92"}, {"id": "S9231", "pId": "S923", "singlehyname": "人民法院", "name": "人民法院(S9231)", "isParent": false, "parentcode": "S923"}, {"id": "S9232", "pId": "S923", "singlehyname": "人民检察院", "name": "人民检察院(S9232)", "isParent": false, "parentcode": "S923"}, {"id": "S929", "pId": "S92", "singlehyname": "其他国家机构", "name": "其他国家机构(S929)", "isParent": true, "parentcode": "S92"}, {"id": "S9291", "pId": "S929", "singlehyname": "消防管理机构", "name": "消防管理机构(S9291)", "isParent": false, "parentcode": "S929"}, {"id": "S9299", "pId": "S929", "singlehyname": "其他未列明的国家机构", "name": "其他未列明的国家机构(S9299)", "isParent": false, "parentcode": "S929"}, {"id": "S93", "pId": "S", "singlehyname": "人民政协、民主党派", "name": "人民政协、民主党派(S93)", "isParent": true, "parentcode": "S"}, {"id": "S931", "pId": "S93", "singlehyname": "人民政协", "name": "人民政协(S931)", "isParent": false, "parentcode": "S93"}, {"id": "S932", "pId": "S93", "singlehyname": "民主党派", "name": "民主党派(S932)", "isParent": false, "parentcode": "S93"}, {"id": "S94", "pId": "S", "singlehyname": "社会保障", "name": "社会保障(S94)", "isParent": true, "parentcode": "S"}, {"id": "S941", "pId": "S94", "singlehyname": "基本保险", "name": "基本保险(S941)", "isParent": true, "parentcode": "S94"}, {"id": "S9411", "pId": "S941", "singlehyname": "基本养老保险", "name": "基本养老保险(S9411)", "isParent": false, "parentcode": "S941"}, {"id": "S9412", "pId": "S941", "singlehyname": "基本医疗保险", "name": "基本医疗保险(S9412)", "isParent": false, "parentcode": "S941"}, {"id": "S9413", "pId": "S941", "singlehyname": "失业保险", "name": "失业保险(S9413)", "isParent": false, "parentcode": "S941"}, {"id": "S9414", "pId": "S941", "singlehyname": "工伤保险", "name": "工伤保险(S9414)", "isParent": false, "parentcode": "S941"}, {"id": "S9415", "pId": "S941", "singlehyname": "生育保险", "name": "生育保险(S9415)", "isParent": false, "parentcode": "S941"}, {"id": "S9419", "pId": "S941", "singlehyname": "其他基本保险", "name": "其他基本保险(S9419)", "isParent": false, "parentcode": "S941"}, {"id": "S9420", "pId": "S94", "singlehyname": "补充保险", "name": "补充保险(S9420)", "isParent": false, "parentcode": "S94"}, {"id": "S9490", "pId": "S94", "singlehyname": "其他社会保障", "name": "其他社会保障(S9490)", "isParent": false, "parentcode": "S94"}, {"id": "S95", "pId": "S", "singlehyname": "群众团体、社会团体和其他成员组织", "name": "群众团体、社会团体和其他成员组织(S95)", "isParent": true, "parentcode": "S"}, {"id": "S951", "pId": "S95", "singlehyname": "群众团体", "name": "群众团体(S951)", "isParent": true, "parentcode": "S95"}, {"id": "S9511", "pId": "S951", "singlehyname": "工会", "name": "工会(S9511)", "isParent": false, "parentcode": "S951"}, {"id": "S9512", "pId": "S951", "singlehyname": "妇联", "name": "妇联(S9512)", "isParent": false, "parentcode": "S951"}, {"id": "S9513", "pId": "S951", "singlehyname": "共青团", "name": "共青团(S9513)", "isParent": false, "parentcode": "S951"}, {"id": "S9519", "pId": "S951", "singlehyname": "其他群众团体", "name": "其他群众团体(S9519)", "isParent": false, "parentcode": "S951"}, {"id": "S952", "pId": "S95", "singlehyname": "社会团体", "name": "社会团体(S952)", "isParent": true, "parentcode": "S95"}, {"id": "S9521", "pId": "S952", "singlehyname": "专业性团体", "name": "专业性团体(S9521)", "isParent": false, "parentcode": "S952"}, {"id": "S9522", "pId": "S952", "singlehyname": "行业性团体", "name": "行业性团体(S9522)", "isParent": false, "parentcode": "S952"}, {"id": "S9529", "pId": "S952", "singlehyname": "其他社会团体", "name": "其他社会团体(S9529)", "isParent": false, "parentcode": "S952"}, {"id": "S953", "pId": "S95", "singlehyname": "基金会", "name": "基金会(S953)", "isParent": false, "parentcode": "S95"}, {"id": "S954", "pId": "S95", "singlehyname": "宗教组织", "name": "宗教组织(S954)", "isParent": true, "parentcode": "S95"}, {"id": "S9541", "pId": "S954", "singlehyname": "宗教团体服务", "name": "宗教团体服务(S9541)", "isParent": false, "parentcode": "S954"}, {"id": "S9542", "pId": "S954", "singlehyname": "宗教活动场所服务", "name": "宗教活动场所服务(S9542)", "isParent": false, "parentcode": "S954"}, {"id": "S96", "pId": "S", "singlehyname": "基层群众自治组织及其他组织", "name": "基层群众自治组织及其他组织(S96)", "isParent": true, "parentcode": "S"}, {"id": "S961", "pId": "S96", "singlehyname": "社区居民自治组织", "name": "社区居民自治组织(S961)", "isParent": false, "parentcode": "S96"}, {"id": "S962", "pId": "S96", "singlehyname": "村民自治组织", "name": "村民自治组织(S962)", "isParent": false, "parentcode": "S96"}, {"id": "T", "pId": "", "singlehyname": "国际组织", "name": "国际组织(T)", "isParent": true, "parentcode": ""}, {"id": "T97", "pId": "T", "singlehyname": "国际组织", "name": "国际组织(T97)", "isParent": true, "parentcode": "T"}, {"id": "T9700", "pId": "T97", "singlehyname": "国际组织", "name": "国际组织(T9700)", "isParent": false, "parentcode": "T97"}, {"id": "TY", "pId": "", "singlehyname": "通用工序", "name": "通用工序(TY)", "isParent": true, "parentcode": ""}, {"id": "TY01", "pId": "TY", "singlehyname": "锅炉", "name": "锅炉(TY01)", "isParent": false, "parentcode": "TY"}, {"id": "TY02", "pId": "TY", "singlehyname": "工业炉窑", "name": "工业炉窑(TY02)", "isParent": false, "parentcode": "TY"}, {"id": "TY03", "pId": "TY", "singlehyname": "表面处理", "name": "表面处理(TY03)", "isParent": false, "parentcode": "TY"}, {"id": "TY04", "pId": "TY", "singlehyname": "水处理通用工序", "name": "水处理通用工序(TY04)", "isParent": false, "parentcode": "TY"}]