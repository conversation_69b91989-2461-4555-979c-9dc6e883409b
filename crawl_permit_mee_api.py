import asyncio
from typing import Dict, <PERSON><PERSON>
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware

from filekit import check_pdftotext
from crawler import AsyncPermitMeeClient

"""
# 启动命令

uvicorn crawl_permit_mee_api:app --host 0.0.0.0 --port 17017 --workers 1

"""

check_pdftotext()

app = FastAPI()

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局字典，用于存储每个请求组合对应的锁
request_locks: Dict[Tuple[str, str, str], asyncio.Lock] = {}

# 健康检查
@app.get("/")
def hello():
    return JSONResponse({"status": "OK"}, status_code=200)


@app.head("/")
def hello_head():
    return JSONResponse({"status": "OK"}, status_code=200)


@app.get("/health")
def health_check():
    return JSONResponse({"status": "OK"}, status_code=200)


@app.head("/health")
def health_check_head():
    return JSONResponse({"status": "OK"}, status_code=200)


@app.post("/crawl/permit/mee/datalist")
async def crawl_permit_mee_datalist(request: Request):
    data = await request.json()
    username = data.get("username")
    password = data.get("password")
    client = AsyncPermitMeeClient(username, password)
    response = {
        "code": 200,
        "message": "success",
        "data": [],
    }

    try:
        data = await client.fetch_datalist_from_html()
        response["data"] = data

        if len(response["data"]) == 0:
            response["code"] = 404
            response["message"] = "未找到相关排污许可证数据"
    except Exception as e:
        response["code"] = 500
        response["message"] = str(e)
    finally:
        await client.session.close()

    return JSONResponse(response, status_code=response["code"])


@app.post("/crawl/permit/mee/chunk")
async def crawl_permit_mee_filelist(request: Request):
    data = await request.json()
    username = data.get("username")
    password = data.get("password")
    data_id = data.get("data_id")
    chunk_type_list = data.get("chunk_type_list", [])
    content_type = data.get("content_type")

    # 创建一个唯一标识符，用于识别相同的请求组合
    request_key = (username, password, data_id)

    # 如果该组合没有对应的锁，则创建一个
    if request_key not in request_locks:
        request_locks[request_key] = asyncio.Lock()

    # 获取锁
    lock = request_locks[request_key]

    response = {
        "code": 200,
        "message": "success",
        "data": [],
    }

    client = None
    lock_acquired = False

    try:
        # 尝试获取锁，如果锁已被其他请求获取，则等待
        try:
            # 设置超时时间为180秒，避免无限等待
            await asyncio.wait_for(lock.acquire(), timeout=180)
            lock_acquired = True

            client = AsyncPermitMeeClient(username, password)
            if content_type == "web":
                data = await client.crawl_persist(data_id)
            elif content_type == "file":
                chunk_type_list = [i.replace("环评", "") for i in chunk_type_list]
                data = await client.crawl_from_file_persist(data_id)

            for chunk_type in chunk_type_list:
                # response["data"].extend([
                #     chunk.__dict__
                #     for chunk in data if any(chunk_type in x for x in chunk.chunk_type)
                # ])
                for chunk in data:
                    if content_type == "file":
                        if any(chunk_type in x for x in chunk.chunk_type):
                            chunk.chunk_type = [chunk_type]
                            response["data"].append(chunk.__dict__)
                    else:
                        if any(chunk_type==x for x in chunk.chunk_type):
                            chunk.chunk_type = [chunk_type]
                            response["data"].append(chunk.__dict__)
        except asyncio.TimeoutError:
            response["code"] = 408
            response["message"] = "请求超时，请稍后重试"

    except Exception as e:
        response["code"] = 500
        response["message"] = str(e)
    finally:
        if client:
            await client.session.close()

        # 释放锁
        if lock_acquired:
            lock.release()

        # 如果没有其他请求在等待这个锁，可以清理它
        if request_key in request_locks and not request_locks[request_key].locked():
            request_locks.pop(request_key, None)

    if len(response["data"]) == 0:
        response["code"] = 404
        response["message"] = "未找到相关文本块"

    return JSONResponse(response, status_code=response["code"])


@app.post("/crawl/permit/mee/png/oss")
async def crawl_permit_mee_with_oss(request: Request):
    """爬取网页数据并将截图上传到OSS，返回OSS链接"""
    data = await request.json()
    username = data.get("username")
    password = data.get("password")
    data_id = data.get("data_id")

    client = AsyncPermitMeeClient(username, password)
    response = {
        "code": 200,
        "message": "success",
        "data": [],
    }

    try:
        chunk_data_list = await client.crawl(data_id)
        response["data"] = [
            {
                "title": chunk.chunk_type[0] if chunk.chunk_type else "未知",
                "content": chunk.content,
                "oss_urls": chunk.multy_file_list or []
            }
            for chunk in chunk_data_list
        ]
        if len(response["data"]) == 0:
            response["code"] = 404
            response["message"] = "无法获取页面数据"
    except Exception as e:
        response["code"] = 500
        response["message"] = str(e)
    finally:
        await client.session.close()

    return JSONResponse(response, status_code=response["code"])


@app.post("/crawl/permit/mee/pollutant/category")
async def crawl_permit_mee_filelist(request: Request):
    data = await request.json()
    username = data.get("username")
    password = data.get("password")
    data_id = data.get("data_id")

    response = {
        "code": 200,
        "message": "该 Response 返回的依据资料, 用于评判某企业是属于什么排污许可证管理类别",
        "data": {},
    }

    try:
        client = AsyncPermitMeeClient(username, password)
        data = await client.get_排污许可名录相关信息(data_id)
        response["data"] = data

    except Exception as e:
        response["code"] = 500
        response["message"] = str(e)

    finally:
        await client.session.close()

    return JSONResponse(response, status_code=response["code"])
