from dataclasses import dataclass
import enum
from typing import List, Dict, Any, Optional

from es import ElaasticSearchClient


@dataclass
class ChunkData:
    chunk_type: List[str]
    chunk_from: str # web or file
    content: str
    multy_file_list: List[str] = None  # 用于存储OSS链接

    def __eq__(self, other: 'ChunkData') -> bool:
        if not isinstance(other, ChunkData):
            return False

        return (
            len(self.chunk_type) == len(other.chunk_type) and
            all(a == b for a, b in zip(self.chunk_type, other.chunk_type)) and
            self.chunk_from == other.chunk_from and
            self.content == other.content and
            self.multy_file_list == other.multy_file_list
        )

    def __hash__(self):
        return hash((tuple(self.chunk_type), self.chunk_from, self.content, tuple(self.multy_file_list) if self.multy_file_list else None))


class CrawlStatus(enum.Enum):
    NO_DATA = 0
    WEB_DATA = 1
    FILE_DATA = 2
    BOTH_DATA = 3

    @classmethod
    def from_int(cls, status: str) -> 'CrawlStatus':
        if status == 1:
            return cls.WEB_DATA
        elif status == 2:
            return cls.FILE_DATA
        elif status == 3:
            return cls.BOTH_DATA
        elif status == 0:
            return cls.NO_DATA
        else:
            raise ValueError(f"Invalid status: {status}")

@dataclass
class PersistDataStruct:
    username: str
    password: str
    data_id: str
    chunk_list: List[ChunkData]
    crawl_status: CrawlStatus # 0: no data, 1: web data, 2: file data, 3: both web and file data

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the PersistDataStruct to a dictionary
        """
        return {
            "username": self.username,
            "password": self.password,
            "data_id": self.data_id,
            "crawl_status": self.crawl_status.value,
            "chunk_list": [chunk.__dict__ for chunk in self.chunk_list]
        }


class PersistService:
    def __init__(self):
        self.es_client = ElaasticSearchClient()

    def persist(self, data: PersistDataStruct):
        existing_persist = self.search(data.username, data.data_id)
        if existing_persist:
            old_crawl_status = existing_persist.crawl_status
            current_crawl_status = data.crawl_status
            data.crawl_status = CrawlStatus(old_crawl_status.value | current_crawl_status.value)
            data.chunk_list = list(set(existing_persist.chunk_list) | set(data.chunk_list))

            self.delete(data.username, data.data_id)

        return self.es_client.index(
            index="permit_mee_data",
            body=data.to_dict()
        )

    def search(self, username: str, data_id: str) -> Optional[PersistDataStruct]:
        res =  self.es_client.search(
            index="permit_mee_data",
            body={"query":{"bool":{"must":[{"term":{"username.keyword":username}},{"term":{"data_id.keyword":data_id}}]}}}
        )

        results = res["hits"]["hits"]
        if results:
            data = results[0]["_source"]
            chunk_list: List[ChunkData] = [
                ChunkData(
                    chunk_type=chunk["chunk_type"],
                    chunk_from=chunk["chunk_from"],
                    content=chunk["content"],
                    multy_file_list=chunk["multy_file_list"], 
                )
                for chunk in data["chunk_list"]
            ]
            return PersistDataStruct(
                username=data["username"],
                password=data["password"],
                data_id=data["data_id"],
                crawl_status=CrawlStatus.from_int(int(data["crawl_status"])),
                chunk_list=chunk_list
            )

        return None

    def is_exist(self, username: str, data_id: str) -> bool:
        res =  self.es_client.search(
            index="permit_mee_data",
            body={"query":{"bool":{"must":[{"term":{"username.keyword":username}},{"term":{"data_id.keyword":data_id}}]}},"_source":False}
        )

        results = res["hits"]["hits"]
        if results:
            return True
        return False

    def delete(self, username: str, data_id: str):
        res = self.es_client.client.delete_by_query(
            index="permit_mee_data",
            body={"query":{"bool":{"must":[{"term":{"username.keyword":username}},{"term":{"data_id.keyword":data_id}}]}}}
        )

        return res


# class IndustryService:
#     def __init__(self):
#         self.es_client = ElaasticSearchClient()

#     def is_important(self, username: str, data_id: str) -> bool:
#         res = self.es_client.search(
#             index="permit_mee_data",
#             body={"query":{"bool":{"must":[{"term":{"username.keyword":username}},{"term":{"data_id.keyword":data_id}}]}}}
#         )



def test():
    s = PersistService()
    s.persist(
        PersistDataStruct(
            username="test",
            password="test",
            data_id="test",
            crawl_status=CrawlStatus.WEB_DATA,
            chunk_list=[
                ChunkData(
                    chunk_type=["te1st"],
                    chunk_from="web",
                    content="tes111t"
                )
            ]
        )
    )

# test()