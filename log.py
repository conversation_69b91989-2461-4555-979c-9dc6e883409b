import os
import sys
import fcntl

from enum import Enum
from typing import Optional
from datetime import datetime


class LogLevel(Enum):
    TRACE = 0
    DEBUG = 1
    INFO = 2
    WARN = 3
    ERROR = 4


class Colors:
    GREY = "\033[38;5;240m"
    GREEN = "\033[32m"
    BLUE = "\033[34m"
    YELLOW = "\033[33m"
    RED = "\033[31m"
    BOLD = "\033[1m"
    RESET = "\033[0m"


class RustyLogger:
    def __init__(self, name: str, log_file: Optional[str] = None, level: LogLevel = LogLevel.INFO):
        self.name = name
        self.level = level
        self.log_file = log_file
        
        if self.log_file:
            if not os.path.exists(self.log_file):
                os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
                open(self.log_file, "w").close()
                

    def _log(self, level: LogLevel, color: str, message: str):
        if level.value < self.level.value:
            return

        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        level_str = f"{level.name:5}"
        
        console_line = (
            f"{Colors.GREY}{timestamp}{Colors.RESET} "
            f"{color}{Colors.BOLD}{level_str}{Colors.RESET} "
            f"{Colors.GREY}[{self.name}]{Colors.RESET} "
            f"{message}"
        )
        
        file_line = f"{timestamp} {level_str} [{self.name}] {message}"
        print(console_line, file=sys.stderr)

        self._write_to_file(file_line)
                    
    def _write_to_file(self, file_line: str):
        if self.log_file is None:
            return
        
        with open(self.log_file, "a") as f:
            fcntl.flock(f, fcntl.LOCK_EX)
            try:
                f.write(file_line + "\n")
            except:
                pass
            finally:
                fcntl.flock(f, fcntl.LOCK_UN)
                
    def trace(self, message: str):
        self._log(LogLevel.TRACE, Colors.GREY, message)

    def debug(self, message: str):
        self._log(LogLevel.DEBUG, Colors.BLUE, message)

    def info(self, message: str):
        self._log(LogLevel.INFO, Colors.GREEN, message)

    def warn(self, message: str):
        self._log(LogLevel.WARN, Colors.YELLOW, message)

    def error(self, message: str):
        self._log(LogLevel.ERROR, Colors.RED, message)


def test():
    logger = RustyLogger("test_app", log_file="test.log")
    logger.trace("Detailed trace message")
    logger.debug("Debug information")
    logger.info("Application started")
    logger.warn("Warning: resource usage high")
    logger.error("Error: connection failed")
    
# test()