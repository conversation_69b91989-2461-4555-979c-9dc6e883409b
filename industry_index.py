import csv
import json
from typing import Dict, List, Optional


class PollutionPermit:
    def __init__(self):
        self.data_list = []
        with open("source/排污许可名录.csv", mode="r", encoding="utf-8") as csvfile:
            csv_reader = csv.reader(csvfile)

            # 获取 header，并跳过第一列
            header = next(csv_reader)
            if len(header) > 1:
                processed_header = header[1:]

            for row in csv_reader:
                # 跳过空行
                if not any(cell.strip() for cell in row):
                    continue
                if len(row) > 1 and len(row[1:]) == len(processed_header):
                    row_dict = {}
                    for i in range(len(processed_header)):
                        if row[i+1].strip() != '': 
                            row_dict[processed_header[i]] = row[i+1]
                    if row_dict:
                        self.data_list.append(row_dict)



PP = PollutionPermit()


class IndustryNode:
    def __init__(self, id, pId, singlehyname, name, isParent, parentcode):
        self.id = id
        self.pId = pId
        self.singlehyname = singlehyname
        self.name = name
        self.isParent = isParent
        self.parentcode = parentcode
        
        if "TY" not in self.id:
            self.code_only_numbers = "".join(c for c in self.id if c.isdigit()) if any(c.isdigit() for c in self.id) else self.id
        else:
            self.code_only_numbers = self.id
        
    @classmethod
    def from_dict(cls, data: dict):
        """Create an IndustryNode instance from a dictionary."""
        return cls(
            id=data.get("id"),
            pId=data.get("pId"),
            singlehyname=data.get("singlehyname"),
            name=data.get("name"),
            isParent=data.get("isParent"),
            parentcode=data.get("parentcode"),
        )
        
    def get_排污许可名录(self) -> List[Dict[str, str]]:
        """Get the 排污许可名录 (Pollution Permit Directory) name."""
        result = []
        
        # 处理通用工序
        if "TY" in self.id:
            for item in PP.data_list:
                if "通用工序" in item["行业大类"]:
                    result.append(item)
            return result
        
        # 处理非通用工序
        for item in PP.data_list:
            # 直接拿到该行业的大类信息, 反正是给模型看的, 无需精确匹配
            if len(self.code_only_numbers) > 2:
                if self.code_only_numbers[:2] in item["行业大类"] or self.code_only_numbers[:2] in item["行业类别"]:
                    result.append(item)
                
        return result
        
        
class IndustryTree:
    def __init__(self):
        with open("source/国民经济行业分类.json", "r", encoding="utf-8") as f:
            ind = json.load(f)
            
        self.nodes: list[IndustryNode] = [IndustryNode.from_dict(item) for item in ind]
        
    def get_node_by_id(self, node_id: str) -> Optional[IndustryNode]:
        """Get a node by its ID."""
        for node in self.nodes:
            if node.id == node_id:
                return node
        return None
    
    def get_children(self, node_id: str) -> list[IndustryNode]:
        """Get all children of a node by its ID."""
        children = []
        for node in self.nodes:
            if node.pId == node_id:
                children.append(node)
        return children
    
    def get_parent(self, node_id: str) -> Optional[IndustryNode]:
        """Get the parent of a node by its ID."""
        for node in self.nodes:
            if node.id == node_id:
                if node.isParent:
                    return None
                # If the node is not a parent, get its parent ID
                parent_id = node.pId
                return self.get_node_by_id(parent_id)
        return None
    
    
IT = IndustryTree()

    
def test():
    tree = IndustryTree()
    node = tree.get_node_by_id("B0911")
    if node:
        print(f"Node ID: {node.id}, Name: {node.name}")
        children = tree.get_children(node.id)
        print(f"Children of {node.name}: {[child.name for child in children]}")
        parent = tree.get_parent(node.id)
        if parent:
            print(f"Parent of {node.name}: {parent.name}")
        else:
            print(f"{node.name} has no parent.")
        
        print(f"排污许可名录 for {node.name}: {node.get_排污许可名录()}")
    else:
        print("Node not found.")

# test()