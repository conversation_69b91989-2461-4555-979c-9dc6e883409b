import time

from enum import Enum
from dataclasses import dataclass
from markdown_it import MarkdownIt
from openai import OpenAI, AsyncOpenAI
from typing import Any, AsyncGenerator, Generator, List, Literal, TypedDict, Union

from log import RustyLogger
from config import global_config


logger = RustyLogger("LLM", log_file="logs/llm.log")

API_KEY = global_config.llm.apikey
BASE_URL = global_config.llm.base_url
APP_HEADER = {
    "X-Title": global_config.app.name,
    "HTTP-Referer": global_config.app.url
}


class ImageUrl(TypedDict):
    """图片链接 或 base64 str 类型"""
    url: str


class ImageUrlContent(TypedDict):
    """图片内容类型"""
    type: Literal["image_url"]
    image_url: ImageUrl


class TextContent(TypedDict):
    """文本内容类型"""
    type: Literal["text"]
    text: str


class MultiModalMessage(TypedDict):
    """
    多模态消息类型

    EXAMPLE:
    {
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": "Hello, how are you?"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": "https://example.com/image.jpg"
                }
            }
        ]
    }
    """
    role: Literal["user", "assistant", "system"]
    content: List[Union[TextContent, ImageUrlContent]]


class Message(TypedDict):
    """
    消息类型

    EXAMPLE:
    {
        "role": "user",
        "content": "Hello, how are you?"
    }
    """
    role: Literal["user", "assistant", "system"]
    content: str


Messages = List[Union[Message, MultiModalMessage]]


@dataclass
class ModelConfig:
    """ 模型配置数据类, 数据从 https://openrouter.ai/ 获取 """
    model_name: str  # OpenRouter 中的模型全称
    context: float  # 上下文长度, 单位：K
    max_output: int  # 最大输出长度, 单位：token
    input_price: float  # 输入价格, 单位：$/token
    output_price: float  # 输出价格, 单位：$/token
    latency: float  # 时延: 平均每次请求发送后, 收到第一个token的间隔时间, 统计约等值, 单位：s
    throughput: float  # token 速率, 统计值, 单位：token/s
    is_continuable: bool = False  # 是否支持自动续写


class Model(Enum):
    # 数据来源: https://openrouter.ai/anthropic/claude-3.5-sonnet
    CLAUDE_3_5_SONNET = ModelConfig(
        model_name="anthropic/claude-3.5-sonnet",
        context=200,
        max_output=8192,
        input_price=3 / 1000_000,
        output_price=15 / 1000_000,
        latency=1.4,
        throughput=42.59,
        is_continuable=True
    )

    # 数据来源: https://openrouter.ai/anthropic/claude-3.5-haiku
    CLAUDE_3_5_HAIKU = ModelConfig(
        model_name="anthropic/claude-3.5-haiku",
        context=200,
        max_output=8192,
        input_price=0.8 / 1000_000,
        output_price=4 / 1000_000,
        latency=1.42,
        throughput=64.45,
        is_continuable=True
    )

    # 数据来源: https://openrouter.ai/openai/gpt-4o-mini
    GPT_4O_MINI = ModelConfig(
        model_name="openai/gpt-4o-mini",
        context=128,
        max_output=16384,
        input_price=0.15 / 1000_000,
        output_price=0.6 / 1000_000,
        latency=0.5,
        throughput=122.9,
        is_continuable=False
    )

    # 数据来源: https://openrouter.ai/openai/o1-preview
    O1_PREVIEW = ModelConfig(
        model_name="openai/o1-preview",
        context=128,
        max_output=32768,
        input_price=15 / 1000_000,
        output_price=60 / 1000_000,
        latency=10.45,
        throughput=67.62,
        is_continuable=False
    )

    # 数据来源: https://openrouter.ai/google/gemini-2.0-flash-001
    GEMINI_FLASH_2_0 = ModelConfig(
        model_name="google/gemini-2.0-flash-001",
        context=1.05 * 1024,
        max_output=8192,
        input_price=0.1 / 1000_000,
        output_price=0.4 / 1000_000,
        latency=0.48,
        throughput=130.6,
        is_continuable=True
    )
    
    @staticmethod
    def from_str(model_name: str) -> "Model":
        for model in Model:
            if model.value.model_name == model_name:
                return model

        raise ValueError(f"未找到模型: {model_name}")


class LLMUsage:
    def __init__(self, model: Model, prompt_tokens: int=0, completion_tokens: int=0, total_tokens: int=0):
        self.model: Model = model
        self.prompt_tokens = prompt_tokens
        self.completion_tokens = completion_tokens
        self.total_tokens = total_tokens
        self.timestamp = int(time.time() * 1000)

    def to_dict(self):
        return {
            "model": self.model.value.model_name,
            "prompt_tokens": self.prompt_tokens,
            "completion_tokens": self.completion_tokens,
            "total_tokens": self.total_tokens,
            "timestamp": self.timestamp
        }

    def usd_cost(self) -> tuple[float, float, float]:
        prompt_cost = self.prompt_tokens * self.model.value.input_price
        completion_cost = self.completion_tokens * self.model.value.output_price
        total_cost = prompt_cost + completion_cost

        return prompt_cost, completion_cost, total_cost

    def usd_cost_str(self) -> str:
        prompt_cost, completion_cost, total_cost = self.usd_cost()
        return f"model: {self.model.value.model_name}, in: ${prompt_cost:.6f}, out: ${completion_cost:.6f}, total: ${total_cost:.6f}"

    def to_str(self) -> str:
        return f"model: {self.model.value.model_name}, in: {self.prompt_tokens} token, out: {self.completion_tokens} token, total: {self.total_tokens} token"

    def __add__(self, other: "LLMUsage") -> "LLMUsage":
        if self.model != other.model:
            raise ValueError(
                f"不能将不同模型的使用量相加: {self.model.value.model_name} 与 {other.model.value.model_name}")

        return LLMUsage(
            model=self.model,
            prompt_tokens=self.prompt_tokens + other.prompt_tokens,
            completion_tokens=self.completion_tokens + other.completion_tokens,
            total_tokens=self.total_tokens + other.total_tokens
        )


class LLMClient:
    def __init__(self, model: Model = Model.GEMINI_FLASH_2_0):
        self.model: Model = model

        self.client = OpenAI(
            base_url=BASE_URL,
            api_key=API_KEY
        )

        self.async_client = AsyncOpenAI(
            base_url=BASE_URL,
            api_key=API_KEY
        )

        self.usage: List[LLMUsage] = []

    async def async_stream_call_llm(self, messages: Messages, *args, **kwargs) -> AsyncGenerator[Any | Literal[''], None]:
        chat_completion = await self.async_client.chat.completions.create(  # type: ignore
            model=self.model.value.model_name,
            messages=messages,  # type: ignore
            stream=True,
            stream_options={"include_usage": True},
            extra_headers=APP_HEADER,
            *args, **kwargs
        )
        logger.info(f"Calling {self.model.value.model_name} ......")
        
        async for chunk in chat_completion:  # type: ignore
            if chunk.choices:  # type: ignore
                char = chunk.choices[0].delta.content or ""  # type: ignore
                yield char

            if chunk.usage:  # type: ignore
                usage = LLMUsage(
                    self.model,
                    chunk.usage.prompt_tokens,  # type: ignore
                    chunk.usage.completion_tokens,  # type: ignore
                    chunk.usage.total_tokens  # type: ignore
                )
                self.usage.append(usage)
                logger.info(usage.to_str())
                logger.info(usage.usd_cost_str())

    def stream_call_llm(self, messages: Messages, *args, **kwargs) -> Generator[Any | Literal[''], Any, None]:
        chat_completion = self.client.chat.completions.create(  # type: ignore
            model=self.model.value.model_name,
            messages=messages,  # type: ignore
            stream=True,
            stream_options={"include_usage": True},
            extra_headers=APP_HEADER,
            *args, **kwargs
        )
        logger.info(f"Calling {self.model.value.model_name} ......")

        for chunk in chat_completion:  # type: ignore
            if chunk.choices:  # type: ignore
                char = chunk.choices[0].delta.content or ""  # type: ignore
                yield char

            if chunk.usage:  # type: ignore
                usage = LLMUsage(
                    self.model,
                    chunk.usage.prompt_tokens,  # type: ignore
                    chunk.usage.completion_tokens,  # type: ignore
                    chunk.usage.total_tokens  # type: ignore
                )
                self.usage.append(usage)
                logger.info(usage.to_str())
                logger.info(usage.usd_cost_str())

    async def async_call_llm(self, messages: Messages, *args, **kwargs) -> str:
        chat_completion = await self.async_client.chat.completions.create(
            model=self.model.value.model_name,
            messages=messages,  # type: ignore
            extra_headers=APP_HEADER,
            *args, **kwargs
        )
        logger.info(f"Calling {self.model.value.model_name} ......")

        if chat_completion.usage:
            usage = LLMUsage(
                self.model,
                chat_completion.usage.prompt_tokens,  # type: ignore
                chat_completion.usage.completion_tokens,  # type: ignore
                chat_completion.usage.total_tokens  # type: ignore
            )
            self.usage.append(usage)
            logger.info(usage.to_str())
            logger.info(usage.usd_cost_str())
            
        response = chat_completion.choices[0].message.content
        if not response:
            logger.error("OpenRouter 返回的消息内容为空")
            return ""

        return response
        
    async def async_call_llm_struct(self, messages: Messages, response_format, *args, **kwargs):
        chat_completion = await self.async_client.beta.chat.completions.parse(
            model=self.model.value.model_name,
            messages=messages,  # type: ignore
            extra_headers=APP_HEADER,
            response_format=response_format,
            *args, **kwargs
        )
        logger.info(f"Calling {self.model.value.model_name} ......")

        if chat_completion.usage:
            usage = LLMUsage(
                self.model,
                chat_completion.usage.prompt_tokens,  # type: ignore
                chat_completion.usage.completion_tokens,  # type: ignore
                chat_completion.usage.total_tokens  # type: ignore
            )
            self.usage.append(usage)
            logger.info(usage.to_str())
            logger.info(usage.usd_cost_str())
            
        return chat_completion.choices[0].message.parsed

    def call_llm(self, messages: Messages, *args, **kwargs) -> str:
        chat_completion = self.client.chat.completions.create(
            model=self.model.value.model_name,
            messages=messages,  # type: ignore
            extra_headers=APP_HEADER,
            *args, **kwargs
        )

        if chat_completion.usage:
            usage = LLMUsage(
                self.model,
                chat_completion.usage.prompt_tokens,  # type: ignore
                chat_completion.usage.completion_tokens,  # type: ignore
                chat_completion.usage.total_tokens  # type: ignore
            )
            self.usage.append(usage)
            logger.info(usage.to_str())
            logger.info(usage.usd_cost_str())
            
        response = chat_completion.choices[0].message.content
        if not response:
            logger.error("OpenRouter 返回的消息内容为空")
            return ""

        return response
    
    async def async_call_llm_auto_continue(self, messages: Messages, max_continue: int, *args, **kwargs) -> str:
        if self.model.value.is_continuable is False:
            logger.error(f"不支持自动续写的模型: {self.model.value.model_name}")
            raise ValueError(f"不支持自动续写的模型: {self.model.value.model_name}")
        
        start_response = await self.async_call_llm(messages, *args, **kwargs)
        
        n = 0
        pre_response = start_response
        while n < max_continue: 
            # 若输出token过大, 则认为其有可能是未完成的回答, 继续调用, 最大次数为 max_continue
            if self.last_usage().completion_tokens > self.model.value.max_output * 0.98:
                messages.append({
                    "role": "assistant",
                    "content": pre_response
                })
                continue_response = await self.async_call_llm(messages, *args, **kwargs)
                start_response += continue_response
                
                n += 1
                pre_response = continue_response
            else:
                break
        
        return start_response
    
    def last_usage(self) -> LLMUsage:
        if not self.usage:
            return LLMUsage(
                model=self.model,
                prompt_tokens=0,
                completion_tokens=0,
                total_tokens=0
            )

        return self.usage[-1]

    def total_usage(self) -> LLMUsage:
        if not self.usage:
            return LLMUsage(
                model=self.model,
                prompt_tokens=0,
                completion_tokens=0,
                total_tokens=0
            )

        total = self.usage[0]
        for usage in self.usage[1:]:
            total += usage

        return total
    
    def clear_usage(self):
        self.usage = []


def extract_md_content(markdown_text: str, block_type: str) -> List[str]:
    """ 提取 markdown 文本中指定标签的文本. """

    md = MarkdownIt()
    tokens = md.parse(markdown_text)

    extracted_contents: List[str] = []

    for token in tokens:
        if token.type == "fence" and token.info.strip().lower() == block_type.lower():
            extracted_contents.append(token.content.strip())
            
    if len(extracted_contents) == 0:
        raise ValueError(f"未找到指定类型的文本块: {block_type}")

    return extracted_contents


def test():
    client = LLMClient(model=Model.GEMINI_FLASH_2_0)
    messages: Messages = [
        {
            "role": "user",
            "content": "Hello, how are you?"
        },
        {
            "role": "assistant",
            "content": "I'm fin"
        }
    ]

    response = client.call_llm(messages)
    print(response)
    
# test()