import tomli

from pathlib import Path
from typing import Optional, Dict
from dataclasses import dataclass


@dataclass
class AppConfig:
    name: str
    url: str
    splash_url: str


@dataclass
class LLMConfig:
    apikey: str
    base_url: str


@dataclass
class RedisConfig:
    host: str
    port: int
    password: str
    db: int


@dataclass
class ESConfig:
    host: str
    port: int
    auth: str


@dataclass
class OSSConfig:
    access_key_id: str
    access_key_secret: str
    endpoint: str
    bucket_name: str
    file_dir: str


class Config:
    """配置类，用于管理 TOML 配置文件"""

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置类

        Args:
            config_path: 配置文件路径，默认为当前目录下的 config.toml
        """
        self._config_path = Path(config_path if config_path else "config.toml")
        self._config_data = self._load_config()

        app_config = self._config_data.get("app", {})
        self.app = AppConfig(
            name=app_config.get("name", "app"),
            url=app_config.get("url", ""),
            splash_url=app_config.get("splash_url", "http://localhost:8050"),
        )

        llm_config = self._config_data.get("llm", {})
        self.llm = LLMConfig(
            apikey=llm_config.get("apikey", ""),
            base_url=llm_config.get("base_url", "https://api.openai.com/v1")
        )

        redis_config = self._config_data.get("redis", {})
        self.redis = RedisConfig(
            host=redis_config.get("host", "localhost"),
            port=int(redis_config.get("port", "6379")),
            password=redis_config.get("password", ""),
            db=int(redis_config.get("db", "0"))
        )

        es_config = self._config_data.get("es", {})
        self.es = ESConfig(
            host=es_config.get("host", "localhost"),
            port=int(es_config.get("port", "9200")),
            auth=es_config.get("auth", "")
        )

        oss_config = self._config_data.get("oss", {})
        self.oss = OSSConfig(
            access_key_id=oss_config.get("access_key_id", ""),
            access_key_secret=oss_config.get("access_key_secret", ""),
            endpoint=oss_config.get("endpoint", ""),
            bucket_name=oss_config.get("bucket_name", ""),
            file_dir=oss_config.get("oss_file_dir", "review_pollution_files")
        )

    def _load_config(self) -> Dict[str, Dict[str, str]]:
        """
        加载 TOML 配置文件

        Returns:
            dict: 配置文件内容

        Raises:
            FileNotFoundError: 配置文件不存在时抛出
            tomli.TOMLDecodeError: TOML 格式错误时抛出
        """
        if not self._config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self._config_path}")

        with open(self._config_path, "rb") as f:
            return tomli.load(f)


global_config = Config()